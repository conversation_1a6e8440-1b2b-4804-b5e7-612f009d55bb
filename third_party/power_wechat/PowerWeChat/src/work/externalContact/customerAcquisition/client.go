package customerAcquisition

import (
	"context"
	"github.com/ArtisanCloud/PowerLibs/v3/object"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
)

type Client struct {
	BaseClient *kernel.BaseClient
}

func NewClient(app kernel.ApplicationInterface) (*Client, error) {
	baseClient, err := kernel.NewBaseClient(&app, nil)
	if err != nil {
		return nil, err
	}
	return &Client{
		baseClient,
	}, nil
}

// ListLink 获取获客链接列表
func (comp *Client) ListLink(ctx context.Context, cursor string, limit int64) (*ListCustomerAcquisitionLinkResp, error) {

	result := &ListCustomerAcquisitionLinkResp{}

	options := &object.HashMap{
		"cursor": cursor,
		"limit":  limit,
	}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/list_link", options, nil, nil, result)

	return result, err
}

// GetLink 获取获客链接详情
func (comp *Client) GetLink(ctx context.Context, linkID string) (*GetCustomerAcquisitionLinkResp, error) {

	result := &GetCustomerAcquisitionLinkResp{}

	options := &object.HashMap{
		"link_id": linkID,
	}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/get", options, nil, nil, result)

	return result, err
}

// CreateLink 创建获客链接
func (comp *Client) CreateLink(ctx context.Context, req *CreateCustomerAcquisitionLinkReq) (*ResponseCreateCustomerAcquisitionLink, error) {

	result := &ResponseCreateCustomerAcquisitionLink{}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/create_link", req, nil, nil, result)

	return result, err
}

// UpdateLink 更新获客链接
func (comp *Client) UpdateLink(ctx context.Context, req *UpdateCustomerAcquisitionLinkReq) (*UpdateCustomerAcquisitionLinkResp, error) {

	result := &UpdateCustomerAcquisitionLinkResp{}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/update_link", req, nil, nil, result)

	return result, err
}

// DeleteLink 删除获客链接
func (comp *Client) DeleteLink(ctx context.Context, linkID string) (*DeleteCustomerAcquisitionLinkResp, error) {

	result := &DeleteCustomerAcquisitionLinkResp{}

	options := &object.HashMap{
		"link_id": linkID,
	}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/delete_link", options, nil, nil, result)

	return result, err
}

// GetCustomerList 获取获客客户列表
func (comp *Client) GetCustomerList(ctx context.Context, linkID string, cursor string, limit int64) (*GetCustomerListResp, error) {

	result := &GetCustomerListResp{}

	options := &object.HashMap{
		"link_id": linkID,
		"cursor":  cursor,
		"limit":   limit,
	}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/customer", options, nil, nil, result)

	return result, err
}

// Quota 查询剩余使用量
func (comp *Client) Quota(ctx context.Context) (*GetQuotaResp, error) {

	result := &GetQuotaResp{}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition_quota", nil, nil, nil, result)

	return result, err
}

// Statistic 查询链接使用详情
func (comp *Client) Statistic(ctx context.Context, linkID string, startTime, endTime int64) (*GetCustomerAcquisitionStatisticResp, error) {
	result := &GetCustomerAcquisitionStatisticResp{}

	options := &object.HashMap{
		"link_id":    linkID,
		"start_time": startTime,
		"end_time":   endTime,
	}

	_, err := comp.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/customer_acquisition/statistic", options, nil, nil, result)

	return result, err
}
