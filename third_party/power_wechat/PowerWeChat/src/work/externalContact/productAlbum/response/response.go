package response

import "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/response"

type AddProductAlbumResponse struct {
	response.ResponseWork
	ProductID string `json:"product_id"`
}

type Attachment struct {
	Type  string `json:"type"`
	Image struct {
		MediaID string `json:"media_id"`
	} `json:"image"`
}

type ProductAlbum struct {
	ProductID   string       `json:"product_id"`
	Description string       `json:"description"`
	Price       int          `json:"price"`
	CreateTime  *int64       `json:"create_time,omitempty"`
	ProductSn   string       `json:"product_sn"`
	Attachments []Attachment `json:"attachments"`
}

type GetProductAlbumResponse struct {
	response.ResponseWork
	Product *ProductAlbum `json:"product,omitempty"`
}

type GetProductAlbumListResponse struct {
	response.ResponseWork
	NextCursor  string          `json:"next_cursor"`
	ProductList []*ProductAlbum `json:"product_list"`
}

type UpdateProductAlbumResponse struct {
	response.ResponseWork
}

type DeleteProductAlbumResponse struct {
	response.ResponseWork
}
