package productAlbum

import (
	"context"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/productAlbum/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/productAlbum/response"
)

type Client struct {
	BaseClient *kernel.BaseClient
}

func NewClient(app kernel.ApplicationInterface) (*Client, error) {
	baseClient, err := kernel.NewBaseClient(&app, nil)
	if err != nil {
		return nil, err
	}
	return &Client{
		baseClient,
	}, nil
}

func (slf *Client) AddProductAlbum(ctx context.Context, params *request.AddProductAlbumRequest) (*response.AddProductAlbumResponse, error) {
	result := &response.AddProductAlbumResponse{}
	_, err := slf.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/add_product_album", params, nil, nil, result)
	return result, err
}

func (slf *Client) GetProductAlbum(ctx context.Context, params *request.GetProductAlbumRequest) (*response.GetProductAlbumResponse, error) {
	result := &response.GetProductAlbumResponse{}
	_, err := slf.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/get_product_album", params, nil, nil, result)
	return result, err
}

func (slf *Client) GetProductAlbumList(ctx context.Context, params *request.GetProductAlbumListRequest) (*response.GetProductAlbumListResponse, error) {
	result := &response.GetProductAlbumListResponse{}
	_, err := slf.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/get_product_album_list", params, nil, nil, result)
	return result, err
}

func (slf *Client) UpdateProductAlbum(ctx context.Context, params *request.UpdateProductAlbumRequest) (*response.UpdateProductAlbumResponse, error) {
	result := &response.UpdateProductAlbumResponse{}
	_, err := slf.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/update_product_album", params, nil, nil, result)
	return result, err
}

func (slf *Client) DeleteProductAlbum(ctx context.Context, params *request.DeleteProductAlbumRequest) (*response.DeleteProductAlbumResponse, error) {
	result := &response.DeleteProductAlbumResponse{}
	_, err := slf.BaseClient.HttpPostJson(ctx, "cgi-bin/externalcontact/delete_product_album", params, nil, nil, result)
	return result, err
}
