package aliyun_sms

import (
	"context"
	"encoding/json"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

type Client struct {
	cli    *dysmsapi.Client
	logger *log.Logger
}

type SendPayload struct {
	SignName     string
	TemplateCode string
	Phone        string
	Params       map[string]string
}

func NewClient(conf *viper.Viper, l *log.Logger) *Client {
	smsConf := conf.Sub("aliyun.sms")
	credential := credentials.NewAccessKeyCredential(smsConf.GetString("access_key_id"), smsConf.GetString("access_key_secret"))
	client, err := dysmsapi.NewClientWithOptions("cn-shanghai", sdk.NewConfig(), credential)
	if err != nil {
		panic(err)
	}
	return &Client{client, l}
}

func (slf *Client) SendSms(ctx context.Context, payload *SendPayload) (err error) {
	defer func() {
		if err != nil {
			slf.logger.WithContext(ctx).Error("failed to send sms", zap.Error(err))
		}
	}()
	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"
	request.PhoneNumbers = payload.Phone
	request.SignName = payload.SignName
	request.TemplateCode = payload.TemplateCode
	marshal, err := json.Marshal(payload.Params)
	if err != nil {
		return err
	}
	request.TemplateParam = string(marshal)
	resp, err := slf.cli.SendSms(request)
	if err != nil {
		return err
	}
	if !resp.IsSuccess() {
		return errors.Errorf("failed to send sms, response: %v", resp)
	}
	return nil
}
