package v1

import (
	"fmt"
	menuRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/menu/request"
	menuResponse "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/menu/response"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
)

type (
	OaCreateQrcodeRequest struct {
		SceneStr   string `json:"scene_str" binding:"required,min=1,max=64"`
		Remark     string `json:"remark"`
		ActionName string `json:"-"` // 固定值：QR_LIMIT_STR_SCENE
	}
	OaCreateQrcodeResponseData struct {
		Ticket        string `json:"ticket"`
		ExpireSeconds int64  `json:"expire_seconds"`
		Url           string `json:"url"`
	}
	OaCreateQrcodeResponse struct {
		Response
		Data OaCreateQrcodeResponseData `json:"data"`
	}
)

type (
	OaCreateMenuRequest      = menuRequest.RequestMenuCreate
	OaCreateMenuResponseData struct {
	}
	OaCreateMenuResponse struct {
		Response
		Data OaCreateMenuResponseData `json:"data"`
	}
)

type (
	OaGetMenuDetailRequest      struct{}
	OaGetMenuDetailResponseData struct {
		Menus            *menuResponse.Menu              `json:"menu"`
		ConditionalMenus []*menuResponse.ConditionalMenu `json:"conditionalmenu"`
	}
	OaGetMenuDetailResponse struct {
		Response
		Data OaGetMenuDetailResponseData `json:"data"`
	}
)

type (
	OaAppMaterialRequest struct {
		Type       string      `json:"-"`
		FileHeader *FileHeader `json:"-"`
		Remark     string      `json:"-"`
	}
	OaAppMaterialResponseData struct {
		MediaID string `json:"mediaID"`
		Url     string `json:"url"`
	}
	OaAppMaterialResponse struct {
		Response
		Data OaAppMaterialResponseData `json:"data"`
	}
)

type FileHeader struct {
	*multipart.FileHeader
	tempPath string
}

func (slf *FileHeader) GenerateTempFile() (string, error) {
	dir := os.TempDir()
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return "", fmt.Errorf("failed to create temp directory: %w", err)
	}
	file, err := slf.FileHeader.Open()
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()
	path := filepath.Join(dir, slf.FileHeader.Filename)
	fp, err := os.Create(path)
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	defer fp.Close()
	if _, err = io.Copy(fp, file); err != nil {
		return "", fmt.Errorf("failed to copy file content: %w", err)
	}
	slf.tempPath = path
	return slf.tempPath, nil
}

func (slf *FileHeader) DeleteTempFile() {
	if slf.tempPath != "" {
		_ = os.Remove(slf.tempPath)
	}
}
