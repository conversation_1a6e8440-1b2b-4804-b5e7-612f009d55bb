package v1

import (
	"zodiacus/internal/model"
	"zodiacus/third_party/identity"
)

type (
	MasterRequest struct {
		Birthtime  string         `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Gender     string         `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Name       string         `json:"name" example:"张三"` // 姓名
		Birthplace []string       `json:"birthplace"`        // 出生地
		User       *identity.User `json:"-"`
		UserAgent  string         `json:"-"`
		IP         string         `json:"-"`
	}
	MasterMinzhu struct {
		Name           string   `json:"name"`           // 姓名
		Birthtime      string   `json:"birthtime"`      // 出生日期
		BirthtimeLunar string   `json:"birthtimeLunar"` // 农历生日
		Birthplace     []string `json:"birthplace"`     // 出生地
		Gender         string   `json:"gender"`         // 性别
		Bazi           []string `json:"bazi"`           // 八字
		Wuxing         []string `json:"wuxing"`         // 五行：用神,喜神,忌神,仇神,闲神
		Zodiac         string   `json:"zodiac"`         // 生肖
	} // 命主信息
	MasterBirthAnalysis struct {
		Year  *model.BirthYearFortune  `json:"year"`  // 年
		Month *model.BirthMonthFortune `json:"month"` // 月
		Day   *model.BirthDayFortune   `json:"day"`   // 日
		Hour  *model.BirthHourFortune  `json:"hour"`  // 时
	} // 生辰信息
	MasterRizhuShengshi struct {
		BirthHourRizhu *model.BirthHourRizhu `json:"rizhuShengshi"` // 日主生时
		BirthDayHour   *model.BirthDayHour   `json:"birthDayHour"`  // 日柱时柱
	}
	MasterFavorableChoice struct {
		Wuxing       []string            `json:"wuxing"`       // 五行：方位、季节、颜色、数字、居住地、饮食、药学
		TianyiGuiren string              `json:"tianyiGuiren"` // 天乙贵人
		Shengxiao    string              `json:"shengxiao"`    // 生肖
		CareerPalace *LuncaiCareerPalace `json:"careerPalace"` // 事业宫
	}
	MasterYizhuLunmin struct {
		Rizhu  string   `json:"rizhu"`  // 日主
		Lunmin []string `json:"lunmin"` // 轮命
	}
	MasterTiekouZhiduan struct {
		ShangcanDuan string   `json:"shangcanDuan,omitempty"` // 伤残断
		GanzhiDuan   []string `json:"ganzhiDuan,omitempty"`   // 干支断
		ShishenDuan1 []string `json:"shishenDuan1,omitempty"` // 十神断1
		ShishenDuan2 []string `json:"shishenDuan2,omitempty"` // 十神断2
	}
	MasterGongmingGuanyun       = []string
	MasterZuyeYichan            = []string
	MasterShentiJiankang        = []string
	MasterDayunAnalysisOverview struct {
		Overview string `json:"overview"` // 大运概述
		Zhushi   string `json:"zhushi"`   // 大运主事
		Xingyun  string `json:"xingyun"`  // 星运说明
	}
	MasterDayunAnalysisHehua struct {
		TianganWuhe      string `json:"tianganWuhe,omitempty"`      // 天干合化
		DizhiLiuhe       string `json:"dizhiLiuhe,omitempty"`       // 地支六合
		DizhiSanhe       string `json:"dizhiSanhe,omitempty"`       // 地支三合
		DizhiBansanhe    string `json:"dizhiBansanhe,omitempty"`    // 地支半三合
		DizhiSanhui      string `json:"dizhiSanhui,omitempty"`      // 地支三会
		DizhiXiangchong  string `json:"dizhiXiangchong,omitempty"`  // 地支相冲
		DizhiXianghai    string `json:"dizhiXianghai,omitempty"`    // 地支相害
		DizhiSanxing     string `json:"dizhiSanxing,omitempty"`     // 地支三刑
		DizhiWuliZhixing string `json:"dizhiWuliZhixing,omitempty"` // 地支无礼之刑
		DizhiZixing      string `json:"dizhiZixing,omitempty"`      // 地支自刑
	}
	MasterDayunShensha    []string
	MasterDayunVernacular struct {
		Tiangan string `json:"tiangan"` // 天干
		Dizhi   string `json:"dizhi"`   // 地支
	}
	MasterDayunAnalysisCurrent struct {
		Number     int   `json:"number"`     // 大运序号（1～12）
		StartYear  int   `json:"startYear"`  // 开始年份
		EndYear    int   `json:"endYear"`    // 结束年份
		Years      []int `json:"years"`      // 年份列表
		Scores     []int `json:"scores"`     // 评分列表（综合评分）
		BestScore  int   `json:"bestScore"`  // 最佳评分（综合评分）
		WorstScore int   `json:"worstScore"` // 最差评分（综合评分）
		BestYears  []int `json:"bestYears"`  // 最佳年份
		WorstYears []int `json:"worstYears"` // 最差年份
	}
	MasterDayunAnalysis struct {
		Overview   *MasterDayunAnalysisOverview `json:"overview"`   // 大运概述
		Xiyong     string                       `json:"xiyong"`     // 大运喜用
		Hehua      *MasterDayunAnalysisHehua    `json:"hehua"`      // 大运合化
		Shensha    MasterDayunShensha           `json:"shensha"`    // 大运神煞
		Vernacular *MasterDayunVernacular       `json:"vernacular"` // 大运白话
		Current    *MasterDayunAnalysisCurrent  `json:"current"`    // 当前大运
	}
	MasterLiunianAnalysisOverview struct {
		OverviewS string `json:"overviewS"` // 流年概述（实岁）
		OverviewX string `json:"overviewX"` // 流年概述（虚岁）
		Zhushi    string `json:"zhushi"`    // 流年主事
	}
	MasterLiunianAnalysisHehua struct {
		TianganWuhe      string `json:"tianganWuhe,omitempty"`      // 天干合化
		DizhiLiuhe       string `json:"dizhiLiuhe,omitempty"`       // 地支六合
		DizhiSanhe       string `json:"dizhiSanhe,omitempty"`       // 地支三合
		DizhiBansanhe    string `json:"dizhiBansanhe,omitempty"`    // 地支半三合
		DizhiSanhui      string `json:"dizhiSanhui,omitempty"`      // 地支三会
		DizhiXiangchong  string `json:"dizhiXiangchong,omitempty"`  // 地支相冲
		DizhiXianghai    string `json:"dizhiXianghai,omitempty"`    // 地支相害
		DizhiSanxing     string `json:"dizhiSanxing,omitempty"`     // 地支三刑
		DizhiWuliZhixing string `json:"dizhiWuliZhixing,omitempty"` // 地支无礼之刑
		DizhiZixing      string `json:"dizhiZixing,omitempty"`      // 地支自刑
	}
	MasterLiunianAnalysisSuiyun = []string
	MasterLiunianAnalysis       struct {
		JiaoyunTime string                         `json:"jiaoyunTime"` // 交运时间
		Overview    *MasterLiunianAnalysisOverview `json:"overview"`    // 流年概述
		Xiyong      string                         `json:"xiyong"`      // 流年喜用
		Guayun      *model.Guaxiang                `json:"guayun"`      // 流年卦运
		Hehua       *MasterLiunianAnalysisHehua    `json:"hehua"`       // 流年合化
		Suiyun      MasterLiunianAnalysisSuiyun    `json:"suiyun"`      // 流年岁运
		Shensha     []string                       `json:"shensha"`     // 流年神煞
		LunduanKeys []string                       `json:"-"`           // 流年论断
		Lunduan     []string                       `json:"lunduan"`     // 流年论断
		Liuyue      []*YunshiYueScore              `json:"liuyue"`      // 流月运势（第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围））
	}
	MasterXingeRigan struct {
		Rigan   string `json:"rigan"`   // 日干
		Rizhu   string `json:"rizhu"`   // 日主
		Xinxing string `json:"xinxing"` // 心性
		Mongpie string `json:"mongpie"` // 盲派（芒果派？🥭）
	}
	MasterXingeYuezhiShishen struct {
		Shishen string `json:"shishen"` // 十神
		Xinxing string `json:"xinxing"` // 心性
		Youdot  string `json:"youdot"`  // 优点
		Quedot  string `json:"quedot"`  // 缺点
	}
	MasterXingeYuezhuXingyun struct {
		Xingyun  string `json:"xingyun"`  // 星运
		Shuoming string `json:"shuoming"` // 说明
	}
	MasterXingeYinyangPingheng      = string
	MasterXingeYueshiYongji         []string
	MasterXingeRiganWaixiang        = string
	MasterXingeRiganWuxingWangxiang struct {
		RiganWuxing     string `json:"riganWuxing"`     // 日干五行
		WuxingWangshuai string `json:"wuxingWangshuai"` // 五行旺衰
		Shuoming        string `json:"shuoming"`        // 说明
	}
	MasterXinge struct {
		Rigan                *MasterXingeRigan                `json:"rigan"`                // 日干
		YuezhiShishen        *MasterXingeYuezhiShishen        `json:"yuezhiShishen"`        // 月支十神
		YuezhuXingyun        *MasterXingeYuezhuXingyun        `json:"yuezhuXingyun"`        // 月柱星运
		YinyangPingheng      MasterXingeYinyangPingheng       `json:"yinyangPingheng"`      // 阴阳平衡
		YueshiYongji         MasterXingeYueshiYongji          `json:"yueshiYongji"`         // 月时用忌
		RiganWaixiang        MasterXingeRiganWaixiang         `json:"riganWaixiang"`        // 日干外相
		RiganWuxingWangxiang *MasterXingeRiganWuxingWangxiang `json:"riganWuxingWangxiang"` // 日干五行旺相
		Xinge                []string                         `json:"xinge"`                // 性格规则
	}
	MasterResponseData struct {
		ID              int64                         `json:"id"`
		Mingzhu         *MasterMinzhu                 `json:"mingzhu"`         // 命主信息
		BirthAnalysis   *MasterBirthAnalysis          `json:"birthAnalysis"`   // 生辰分析
		TiaoHouYongShen *model.BirthMonthRizhu        `json:"tiaoHouYongShen"` // 调侯用神
		RizhuShengshi   *MasterRizhuShengshi          `json:"rizhuShengshi"`   // 日主生时
		Minggong        *model.Minggong               `json:"minggong"`        // 命宫
		FavorableChoice *MasterFavorableChoice        `json:"favorableChoice"` // 有利选择
		YizhuLunmin     *MasterYizhuLunmin            `json:"yizhuLunmin"`     // 一柱论命
		TiekouZhiduan   *MasterTiekouZhiduan          `json:"tiekouZhiduan"`   // 铁口直断
		GongmingGuanyun MasterGongmingGuanyun         `json:"gongmingGuanyun"` // 功名官运
		ZuyeYichan      MasterZuyeYichan              `json:"zuyeYichan"`      // 祖业遗产
		ShishenLunduan  *MatchMingliRulesResponseData `json:"shishenLunduan"`  // 十神论断
		DayunAnalysis   *MasterDayunAnalysis          `json:"dayunAnalysis"`   // 大运分析
		LiunianAnalysis *MasterLiunianAnalysis        `json:"liunianAnalysis"` // 流年分析
		ShentiJiankang  MasterShentiJiankang          `json:"shentiJiankang"`  // 身体健康
		Xinge           *MasterXinge                  `json:"xinge"`           // 性格
	}
	MasterResponse struct {
		Response
		Data MasterResponseData `json:"data"`
	}
)

type (
	GeJuBianGeRequest struct {
		Birthtime  string         `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Gender     string         `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Name       string         `json:"name" example:"张三"` // 姓名
		Birthplace []string       `json:"birthplace"`        // 出生地
		User       *identity.User `json:"-"`
		UserAgent  string         `json:"-"`
	}
	GeJuBianGeResponseDataItem struct {
		Name   string   `json:"name"`   // 格局名称
		Xiyong []string `json:"xiyong"` // 喜用五行
	}
	GeJuBianGeResponseData = []*GeJuBianGeResponseDataItem
	GeJuBianGeResponse     struct {
		Response
		Data GeJuBianGeResponseData `json:"data"`
	}
)

type (
	MasterXiyongWangshuaiRequest struct {
		Birthtime string   `json:"birthtime" binding:"required" example:"2006-01-02"` // 生日
		Gender    string   `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Times     []string `json:"times" binding:"required" example:"00:30:00"` // 可能的时辰（格式为数组，00:30:00）
	}
	MasterXiyongWangshuai4Wuxing struct {
		Xiyong string `json:"xiyong"` // 喜用概率
		Chouji string `json:"chouji"` // 仇忌概率
		Xian   string `json:"xian"`   // 闲神概率
	}
	MasterXiyongWangshuaiResponseData struct {
		Xiyong    map[string]*MasterXiyongWangshuai4Wuxing
		Wangshuai map[string]string
	}
	MasterXiyongWangshuaiResponse struct {
		Response
		Data MasterXiyongWangshuaiResponseData `json:"data"`
	}
)
