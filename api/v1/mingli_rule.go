package v1

import (
	"zodiacus/internal/model"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/identity"
)

type (
	CreateMingliRuleRequest struct {
		No          string         `json:"no" binding:"required,lte=32" example:"NO1001"` // 规则编号
		Name        string         `json:"name" binding:"required" example:"规则名称"`        // 规则名称
		Module      int64          `json:"module" binding:"required" example:"1"`         // 应用模块
		IsEnabled   bool           `json:"isEnabled" binding:"required" example:"true"`   // 是否启用
		Result      string         `json:"result" binding:"required" example:"判断结果"`      // 判断结果
		Description string         `json:"description" binding:"required" example:"规则说明"` // 规则说明
		User        *identity.User `json:"-"`
	}
	CreateMingliRuleResponseData struct {
		ID int64 `json:"id" example:"1"` // 规则ID
	}
	CreateMingliRuleResponse struct {
		Response
		Data CreateMingliRuleResponseData `json:"data"`
	}
)

type (
	UpdateMingliRuleRequest struct {
		ID          int64  `json:"id" binding:"required" example:"1"`             // 规则ID
		No          string `json:"no" binding:"required,lte=32" example:"NO1001"` // 规则编号
		Name        string `json:"name" binding:"required" example:"规则名称"`        // 规则名称
		Module      int64  `json:"module" binding:"required" example:"1"`         // 应用模块
		IsEnabled   bool   `json:"isEnabled" example:"true"`                      // 是否启用
		Result      string `json:"result" binding:"required" example:"判断结果"`      // 判断结果
		Description string `json:"description" binding:"required" example:"规则说明"` // 规则说明
	}
	UpdateMingliRuleResponse struct {
		Response
	}
)

type (
	DeleteMingliRuleRequest struct {
		ID int64 `json:"id" binding:"required" example:"1"`
	}
	DeleteMingliRuleResponse struct {
		Response
	}
)

type (
	PageListMingliRuleRequestParam struct{}
	PageListMingliRuleRequest      = PagerIn[PageListMingliRuleRequestParam]
	PageListMingliRuleResponseItem struct {
		ID          int64  `json:"id" bun:"id" example:"1"`
		No          string `json:"no" bun:"no" example:"NO1001"`
		Name        string `json:"name" bun:"name" example:"规则名称"`
		Module      int64  `json:"module" bun:"module" example:"1"`
		ModuleName  string `json:"moduleName" bun:"module_name" example:"应用模块名称"`
		Source      int    `json:"source" bun:"source" example:"1"` // 规则来源：1 管理员 2 用户
		CreatorName string `json:"creatorName" bun:"creator_name" example:"创建者"`
		IsEnabled   bool   `json:"isEnabled" bun:"is_enabled" example:"true"`
		Result      string `json:"result" bun:"result" example:"判断结果"`
		Description string `json:"description" bun:"description" example:"规则说明"`
	}
	PageListMingliRuleResponseData = PagerOut[PageListMingliRuleResponseItem]
	PageListMingliRuleResponse     struct {
		Response
		Data PageListMingliRuleResponseData `json:"data"`
	}
)

type MingliRuleRelatedEnums struct {
	Zuodui          []*model.Zuodui
	Wuxing          []*model.Wuxing
	WuxingNamesMap  map[string]*model.Wuxing
	Tiangan         []*model.Tiangan
	TianganNamesMap map[string]*model.Tiangan
	Dizhi           []*model.Dizhi
	DizhiNamesMap   map[string]*model.Dizhi
	Ganzhi          []*model.Ganzhi
	GanzhiNamesMap  map[string]*model.Ganzhi
	GanzhiIDsMap    map[string]*model.Ganzhi
	Shishen         []*model.Shishen
	ShishenNamesMap map[string]*model.Shishen
	Nayin           []*model.Nayin
	NayinNamesMap   map[string]*model.Nayin
	Xiji            []*model.Xiji
	XijiNamesMap    map[string]*model.Xiji
	LunarTime       []*model.LunarTime
	LunarTimeMap    map[string]*model.LunarTime
	LunarDate       []*model.LunarDate
	LunarDateMap    map[string]*model.LunarDate
	LunarMonth      []*model.LunarMonth
	LunarMonthMap   map[string]*model.LunarMonth
}

type (
	MatchMingliRuleRequest = corona.GetPaipanJiuizhuRequest
	MatchMingliRulesInput  struct {
		Gender     int        `json:"gender" example:"1"`                  // 性别：1-男，2-女
		Zhuxing    []int64    `json:"zhuxing" example:"1,2,3,4,5,6,7,8,9"` // 主星，天干对十神时使用主星
		ZhuxingStr []string   `json:"-"`
		Tiangan    []int64    `json:"tiangan" example:"1,2,3,4,5,6,7,8,9"` // 天干
		Dizhi      []int64    `json:"dizhi" example:"1,2,3,4,5,6,7,8,9"`   // 地支
		Canggan    [][]int64  `json:"canggan"`                             // 藏干
		Fuxing     [][]int64  `json:"fuxing"`                              // 副星，地支对十神时使用副星
		FuxingStr  [][]string `json:"-"`
		Nayin      []int64    `json:"nayin" example:"1,2,3,4,5,6,7,8,9"` // 纳音
		Wuxing     []int64    `json:"wuxing" example:"1,2,3,4,5"`        // 五行（顺序为用喜忌仇闲）
		Shishen    []int64    `json:"-" example:"1,2,3,4,5,6,7,8,9"`     // 十神（主星+副星）
		Ganzhi     []int64    `json:"-" example:"1,2,3,4,5,6,7,8,9"`     // 干支
		ZuoduiMap  map[int64]*model.Zuodui
	}
	MatchMingliRuleResponseDataCondition struct {
		ID        int64  `json:"id" example:"1"`         // 条件ID
		Criterion string `json:"criterion" example:"依据"` // 条件依据
	}
	MatchMingliRulesResponseDataRule struct {
		ID         int64                                   `json:"id" example:"1"`        // 规则ID
		Result     string                                  `json:"result" example:"判断结果"` // 判断结果
		Conditions []*MatchMingliRuleResponseDataCondition `json:"conditions"`            // 匹配条件
	}
	MatchMingliRulesResponseData struct {
		Birth []string                            `json:"birth"` // 出生信息（索引：0-年、1-月、2-日、3-时）
		Rules []*MatchMingliRulesResponseDataRule `json:"rules"` //	匹配规则
	}
	MatchMingliRulesResponse struct {
		Response
		Data MatchMingliRulesResponseData `json:"data"`
	}
)
