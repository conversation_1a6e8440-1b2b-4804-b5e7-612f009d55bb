package v1

type (
	JiemengRequestParam struct {
		Content string `json:"content" example:"梦见大海（非必须）"` // 梦境内容（非必须）
	}
	JiemengRequest          = PagerIn[JiemengRequestParam]
	JiemengResponseDataItem struct {
		Title   string `json:"title" example:"梦见大海（标题）"`   // 标题
		Content string `json:"content" example:"梦见大海（内容）"` // 内容
	}
	JiemengResponseData = PagerOut[*JiemengResponseDataItem]
	JiemengResponse     struct {
		Response
		Data JiemengResponseData `json:"data"`
	}
)
