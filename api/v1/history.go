package v1

type (
	GetHistoryDayEventsRequest struct {
		Date string `json:"date" binding:"required" example:"2006-01-02"` // 日期
	}
	GetHistoryDayEventsResponseItem struct {
		Date    string `json:"date" example:"2006-01-02"`          // 日期
		AD      bool   `json:"ad" example:"true"`                  // 公元后
		Keyword string `json:"keyword" example:"大学生爱国运D"`          // 关键词
		Type    string `json:"type" example:"大事记"`                 // 类型
		Title   string `json:"title" example:"大学生爱国运D"`            // 标题
		Content string `json:"content" example:"****************"` // 内容
	}
	GetHistoryDayEventsResponseData struct {
		Actions []*GetHistoryDayEventsResponseItem `json:"actions"` // 活动
		Events  []*GetHistoryDayEventsResponseItem `json:"events"`  // 事件
	}
	GetHistoryDayEventsResponse struct {
		Response
		Data GetHistoryDayEventsResponseData `json:"data"` // 数据
	}
)
