package v1

import "zodiacus/internal/model"

type AtlasCreateProductReq struct {
	model.ProductCreate
}

type AtlasListProductReq struct {
	Name        string `json:"name"`        // 商品名称
	Sort        int    `json:"sort"`        // 排序 1-创建日期升序，2-创建日期降序，3-价格升序，4-价格降序
	Application string `json:"application"` // 应用
}

type AtlasListProductRequest = PagerIn[AtlasListProductReq]

type AtlasListProductResponse = PagerOut[model.Product]

type AtlasUpdateProductReq struct {
	model.ProductCreate
	Id int64 `json:"id"`
}

type AtlasDeleteProductReq struct {
	Id int64 `json:"id"`
}
