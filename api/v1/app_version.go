package v1

type (
	CreateAppVersionRequest struct {
		AppID         int64  `json:"appID" binding:"required"`       // 应用ID
		VersionName   string `json:"versionName" binding:"required"` // 版本名称：1.2.0
		UpdateNote    string `json:"updateNote" binding:"required"`  // 更新说明
		OsType        int    `json:"osType" binding:"required"`      // 1:android, 2:ios
		Url           string `json:"url" binding:"required"`         // 下载地址
		IsForceUpdate bool   `json:"isForceUpdate"`                  // 是否强制更新
		IsHotUpdate   bool   `json:"isHotUpdate"`                    // 是否热更新
		Remark        string `json:"remark"`                         // 备注
	}
	CreateAppVersionResponseData struct {
		ID int64 `json:"id"`
	}
	CreateAppVersionResponse struct {
		Response
		Data *CreateAppVersionResponseData `json:"data"`
	}
)

type (
	UpdateAppVersionRequest struct {
		ID            int64  `json:"id" binding:"required"`          // ID
		VersionName   string `json:"versionName" binding:"required"` // 版本名称：1.2.0
		UpdateNote    string `json:"updateNote" binding:"required"`  // 更新说明
		Url           string `json:"url" binding:"required"`         // 下载地址
		IsForceUpdate bool   `json:"isForceUpdate"`                  // 是否强制更新
		IsHotUpdate   bool   `json:"isHotUpdate"`                    // 是否热更新
		Remark        string `json:"remark"`                         // 备注
	}
	UpdateAppVersionResponse struct {
		Response
	}
)

type (
	PublishAppVersionRequest struct {
		ID int64 `json:"id" binding:"required"` // ID
	}
	PublishAppVersionResponse struct {
		Response
	}
)

type (
	RecallAppVersionRequest struct {
		ID int64 `json:"id" binding:"required"` // ID
	}
	RecallAppVersionResponse struct {
		Response
	}
)

type (
	PageListAppVersionRequestParam struct {
		AppIDs      []int64 `json:"appIDs"`      // 应用ID
		VersionName *string `json:"versionName"` // 版本名称
		UpdateNote  *string `json:"updateNote"`  // 更新说明
		Remark      *string `json:"remark"`      // 备注
		OsType      []int   `json:"osType"`      // 1:android, 2:ios
	}
	PageListAppVersionRequest          = PagerIn[PageListAppVersionRequestParam]
	PageListAppVersionResponseDateItem struct {
		ID            int64  `json:"id" bun:"id"`                         // ID
		AppID         int64  `json:"appID" bun:"app_id"`                  // 应用ID
		AppName       string `json:"appName" bun:"app_name"`              // 应用名称
		VersionCode   int    `json:"versionCode" bun:"version_code"`      // 版本号
		VersionName   string `json:"versionName" bun:"version_name"`      // 版本名称
		UpdateNote    string `json:"updateNote" bun:"update_note"`        // 更新说明
		OsType        int    `json:"osType" bun:"os_type"`                // 1:android, 2:ios
		Url           string `json:"url" bun:"url"`                       // 下载地址
		IsForceUpdate bool   `json:"isForceUpdate" bun:"is_force_update"` // 是否强制更新
		IsHotUpdate   bool   `json:"isHotUpdate" bun:"is_hot_update"`     // 是否热更新
		Remark        string `json:"remark" bun:"remark"`                 // 备注
		Status        int    `json:"status" bun:"status"`                 // 状态：0-未发布、1-已发布、2-已撤回
		CreatedAt     string `json:"createdAt" bun:"created_at"`          // 创建时间
		UpdatedAt     string `json:"updatedAt" bun:"updated_at"`          // 更新时间
	}
	PageListAppVersionResponseData = PagerOut[*PageListAppVersionResponseDateItem]
	PageListAppVersionResponse     struct {
		Response
		Data *PageListAppVersionResponseData `json:"data"`
	}
)

type (
	CheckAppUpdateRequest struct {
		VersionName string `json:"versionName" binding:"required"` // 版本名称
		OsType      int    `json:"osType" binding:"required"`      // 1:android, 2:ios
		Application string `json:"-"`                              // 应用名称
	}
	CheckAppUpdateResponseData struct {
		VersionCode   int    `json:"versionCode" bun:"version_code"` // 版本号
		VersionName   string `json:"versionName" bun:"version_name"` // 版本名称
		UpdateNote    string `json:"updateNote" bun:"update_note"`   // 更新说明
		Url           string `json:"url" bun:"url"`                  // 下载地址
		IsForceUpdate bool   `json:"isForceUpdate"`                  // 是否强制更新
		IsHotUpdate   bool   `json:"isHotUpdate"`                    // 是否热更新
		CreatedAt     string `json:"createdAt" bun:"created_at"`     // 创建时间
		UpdatedAt     string `json:"updatedAt" bun:"updated_at"`     // 更新时间
	}
	CheckAppUpdateResponse struct {
		Response
		Data *CheckAppUpdateResponseData `json:"data"`
	}
)
