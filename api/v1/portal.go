package v1

type (
	CreatePortalWebsiteRequest struct {
		Name   string `json:"name" binding:"required"`
		Domain string `json:"domain" binding:"required"`
		Remark string `json:"remark"`
	}
	CreatePortalWebsiteResponseData struct {
		ID int64 `json:"id"`
	}
	CreatePortalWebsiteResponse struct {
		Response
		Data *CreatePortalWebsiteResponseData `json:"data"`
	}
)

type (
	UpdatePortalWebsiteRequest struct {
		ID     int64  `json:"id" binding:"required"`
		Name   string `json:"name" binding:"required"`
		Domain string `json:"domain" binding:"required"`
		Remark string `json:"remark"`
	}
	UpdatePortalWebsiteResponse struct {
		Response
	}
)

type (
	DeletePortalWebsiteRequest struct {
		ID int64 `json:"id" binding:"required"`
	}
	DeletePortalWebsiteResponse struct {
		Response
	}
)

type (
	PageListPortalWebsiteRequestParam     struct{}
	PageListPortalWebsiteRequest          = PagerIn[PageListAppVersionRequestParam]
	PageListPortalWebsiteResponseDataItem struct {
		ID        int64  `json:"id" bun:"id"`
		Name      string `json:"name" bun:"name"`
		Domain    string `json:"domain" bun:"domain"`
		Remark    string `json:"remark" bun:"remark"`
		CreatedAt string `json:"createdAt" bun:"created_at"`
		UpdatedAt string `json:"updatedAt" bun:"updated_at"`
	}
	PageListPortalWebsiteResponseData = PagerOut[*PageListPortalWebsiteResponseDataItem]
	PageListPortalWebsiteResponse     struct {
		Response
		Data *PageListPortalWebsiteResponseData `json:"data"`
	}
)

type (
	CreatePortalSectionRequest struct {
		WebsiteID int64  `json:"websiteId" binding:"required"`
		Name      string `json:"name" binding:"required"`
		Remark    string `json:"remark"`
	}
	CreatePortalSectionResponseData struct {
		ID int64 `json:"id"`
	}
	CreatePortalSectionResponse struct {
		Response
		Data *CreatePortalSectionResponseData `json:"data"`
	}
)

type (
	UpdatePortalSectionRequest struct {
		ID     int64  `json:"id" binding:"required"`
		Name   string `json:"name" binding:"required"`
		Remark string `json:"remark"`
	}
	UpdatePortalSectionResponse struct {
		Response
	}
)

type (
	DeletePortalSectionRequest struct {
		ID int64 `json:"id" binding:"required"`
	}
	DeletePortalSectionResponse struct {
		Response
	}
)

type (
	PageListPortalSectionRequestParam struct {
		WebsiteID int64 `json:"websiteId" binding:"required"`
	}
	PageListPortalSectionRequest          = PagerIn[PageListPortalSectionRequestParam]
	PageListPortalSectionResponseDataItem struct {
		ID          int64  `json:"id" bun:"id"`
		WebsiteID   int64  `json:"websiteId" bun:"website_id"`
		WebsiteName string `json:"websiteName" bun:"website_name"`
		Name        string `json:"name" bun:"name"`
		Remark      string `json:"remark" bun:"remark"`
		CreatedAt   string `json:"createdAt" bun:"created_at"`
		UpdatedAt   string `json:"updatedAt" bun:"updated_at"`
	}
	PageListPortalSectionResponseData = PagerOut[*PageListPortalSectionResponseDataItem]
	PageListPortalSectionResponse     struct {
		Response
		Data *PageListPortalSectionResponseData `json:"data"`
	}
)

type (
	CreatePortalArticleRequest struct {
		SectionID int64  `json:"sectionId" binding:"required"`
		Title     string `json:"title" binding:"required"`
		Author    string `json:"author" binding:"required"`
		Source    string `json:"source" binding:"required"`
		Content   string `json:"content" binding:"required"`
		Remark    string `json:"remark"`
	}
	CreatePortalArticleResponseData struct {
		ID int64 `json:"id"`
	}
	CreatePortalArticleResponse struct {
		Response
		Data *CreatePortalArticleResponseData `json:"data"`
	}
)

type (
	UpdatePortalArticleRequest struct {
		ID      int64  `json:"id" binding:"required"`
		Title   string `json:"title" binding:"required"`
		Author  string `json:"author" binding:"required"`
		Source  string `json:"source" binding:"required"`
		Content string `json:"content" binding:"required"`
		Remark  string `json:"remark"`
	}
	UpdatePortalArticleResponse struct {
		Response
	}
)

type (
	DeletePortalArticleRequest struct {
		ID int64 `json:"id" binding:"required"`
	}
	DeletePortalArticleResponse struct {
		Response
	}
)

type (
	PageListPortalArticleRequestParam struct {
		WebsiteID  *int64  `json:"websiteId"`
		SectionIDs []int64 `json:"sectionIds"`
	}
	PageListPortalArticleRequest          = PagerIn[PageListPortalArticleRequestParam]
	PageListPortalArticleResponseDataItem struct {
		ID          int64  `json:"id" bun:"id"`
		SectionID   int64  `json:"sectionId" bun:"section_id"`
		SectionName string `json:"sectionName" bun:"section_name"`
		Title       string `json:"title" bun:"title"`
		Author      string `json:"author" bun:"author"`
		Source      string `json:"source" bun:"source"`
		Content     string `json:"content" bun:"content"`
		Remark      string `json:"remark" bun:"remark"`
		CreatedAt   string `json:"createdAt" bun:"created_at"`
		UpdatedAt   string `json:"updatedAt" bun:"updated_at"`
	}
	PageListPortalArticleResponseData = PagerOut[*PageListPortalArticleResponseDataItem]
	PageListPortalArticleResponse     struct {
		Response
		Data *PageListPortalArticleResponseData `json:"data"`
	}
)
