definitions:
  model.MingliRuleConditionValue:
    properties:
      type:
        description: 类型：1-支、2-干、3-干支、4-十神、5-五行
        example: 1
        type: integer
      value:
        description: 值
        example: 1
        type: integer
    type: object
  model.MpQrcode:
    properties:
      appID:
        type: integer
      createdAt:
        description: 创建时间
        type: string
      deletedAt:
        type: string
      id:
        type: integer
      imageData:
        items:
          type: integer
        type: array
      imageType:
        type: string
      page:
        type: string
      remark:
        type: string
      sceneStr:
        type: string
      updatedAt:
        description: 更新时间
        type: string
    type: object
  model.Product:
    properties:
      application:
        description: 所属应用
        type: string
      createdAt:
        description: 创建时间
        type: string
      enable:
        description: 是否启用：0-禁用，1-启用
        type: integer
      id:
        type: integer
      originalPrice:
        description: 划线价
        type: integer
      remark:
        description: 备注
        type: string
      salePrice:
        description: 出售价
        type: integer
      sku_code:
        description: SKU编码
        type: string
      sku_name:
        description: SKU名称
        type: string
      specification:
        description: 规格
        type: string
      stock:
        description: 库存量
        type: integer
      type:
        description: 商品类型：0-其他，1-虚拟会员商品，2-咨询服务
        type: integer
      updatedAt:
        description: 更新时间
        type: string
      validityDays:
        description: 会员有效期天数
        type: integer
    type: object
  model.QwContactFollowEvent:
    properties:
      add_state:
        description: 添加参数
        type: string
      add_way:
        description: 添加方式
        type: integer
      del_flag:
        description: 删除标记：0-未删除 1-被删除 2-主动删除 3-自动删除（接替）
        type: integer
      del_source:
        description: 删除来源
        type: string
      time:
        description: 事件时间
        type: integer
      type:
        description: 事件类型：1-添加 2-删除
        type: integer
    type: object
  model.UserMingliDayun:
    properties:
      endYear:
        type: integer
      startYear:
        type: integer
      values:
        items:
          type: string
        type: array
    type: object
  model.UserMingliXiaoyun:
    properties:
      endYear:
        type: integer
      startYear:
        type: integer
      subYear:
        type: integer
      values:
        items:
          type: string
        type: array
    type: object
  request.Button:
    properties:
      appid:
        type: string
      key:
        type: string
      media_id:
        type: string
      name:
        type: string
      pagepath:
        type: string
      sub_button:
        items:
          $ref: '#/definitions/request.SubButton'
        type: array
      type:
        type: string
      url:
        type: string
    type: object
  request.SubButton:
    properties:
      appid:
        type: string
      key:
        type: string
      media_id:
        type: string
      name:
        type: string
      pagepath:
        type: string
      sub_button:
        items:
          $ref: '#/definitions/request.SubButton'
        type: array
      type:
        type: string
      url:
        type: string
    type: object
  response.Button:
    properties:
      appid:
        type: string
      key:
        type: string
      name:
        type: string
      pagepath:
        type: string
      sub_button:
        items:
          $ref: '#/definitions/response.Button'
        type: array
      type:
        type: string
      url:
        type: string
    type: object
  response.ConditionalMenu:
    properties:
      button:
        items:
          $ref: '#/definitions/response.Button'
        type: array
      matchrule:
        $ref: '#/definitions/response.MatchRule'
      menuid:
        type: integer
    type: object
  response.MatchRule:
    properties:
      city:
        type: string
      client_platform_type:
        type: integer
      country:
        type: string
      group_id:
        type: integer
      province:
        type: string
      sex:
        type: integer
    type: object
  response.Menu:
    properties:
      button:
        items:
          $ref: '#/definitions/response.Button'
        type: array
      menuid:
        type: integer
    type: object
  v1.AppUserMemberDuration:
    properties:
      appsName:
        items:
          type: string
        type: array
      expireTime:
        description: 到期时间
        type: string
      isExpired:
        description: 是否过期
        type: boolean
      lastGainTime:
        description: 最后一次获取时间
        type: string
      lastGainType:
        description: 最后一次获取类型：0 购买，1 管理员赠送，2 注册赠送，3 app登录赠送
        type: integer
      roleID:
        type: integer
      roleName:
        type: string
      startTime:
        description: 开始时间
        type: string
    type: object
  v1.AtlasCreateProductReq:
    properties:
      application:
        description: 所属应用
        type: string
      enable:
        description: 是否启用：0-禁用，1-启用
        type: integer
      originalPrice:
        description: 划线价
        type: integer
      remark:
        description: 备注
        type: string
      salePrice:
        description: 出售价
        type: integer
      sku_name:
        description: SKU名称
        type: string
      specification:
        description: 规格
        type: string
      stock:
        description: 库存量
        type: integer
      type:
        description: 商品类型：0-其他，1-虚拟会员商品，2-咨询服务
        type: integer
      validityDays:
        description: 会员有效期天数
        type: integer
    type: object
  v1.AtlasDeleteProductReq:
    properties:
      id:
        type: integer
    type: object
  v1.AtlasListProductReq:
    properties:
      application:
        description: 应用
        type: string
      name:
        description: 商品名称
        type: string
      sort:
        description: 排序 1-创建日期升序，2-创建日期降序，3-价格升序，4-价格降序
        type: integer
    type: object
  v1.AtlasListProductRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.AtlasListProductReq'
    required:
    - pageNum
    - pageSize
    type: object
  v1.AtlasListProductResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/model.Product'
        type: array
      total:
        type: integer
    type: object
  v1.AtlasUpdateProductReq:
    properties:
      application:
        description: 所属应用
        type: string
      enable:
        description: 是否启用：0-禁用，1-启用
        type: integer
      id:
        type: integer
      originalPrice:
        description: 划线价
        type: integer
      remark:
        description: 备注
        type: string
      salePrice:
        description: 出售价
        type: integer
      sku_name:
        description: SKU名称
        type: string
      specification:
        description: 规格
        type: string
      stock:
        description: 库存量
        type: integer
      type:
        description: 商品类型：0-其他，1-虚拟会员商品，2-咨询服务
        type: integer
      validityDays:
        description: 会员有效期天数
        type: integer
    type: object
  v1.ChannelAppStatistics:
    properties:
      appID:
        type: integer
      appName:
        type: string
      enableWxMiniProgram:
        type: boolean
      signupNum:
        type: integer
      wxMiniProgramQrCode:
        $ref: '#/definitions/model.MpQrcode'
    type: object
  v1.ChannelStatistics:
    properties:
      apps:
        items:
          $ref: '#/definitions/v1.ChannelAppStatistics'
        type: array
      signupNum:
        type: integer
    type: object
  v1.CreateAppChannelRequest:
    properties:
      name:
        type: string
      remark:
        type: string
    required:
    - name
    - remark
    type: object
  v1.CreateAppChannelResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreateAppChannelResponseData'
      message:
        type: string
    type: object
  v1.CreateAppChannelResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.CreateAppVersionRequest:
    properties:
      appID:
        description: 应用ID
        type: integer
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      osType:
        description: 1:android, 2:ios
        type: integer
      remark:
        description: 备注
        type: string
      updateNote:
        description: 更新说明
        type: string
      url:
        description: 下载地址
        type: string
      versionName:
        description: 版本名称：1.2.0
        type: string
    required:
    - appID
    - osType
    - updateNote
    - url
    - versionName
    type: object
  v1.CreateAppVersionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreateAppVersionResponseData'
      message:
        type: string
    type: object
  v1.CreateAppVersionResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.CreateBaziAnalysisLinkRequest:
    properties:
      birthplace:
        description: 出生地点：省、市、区
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间：2006-01-02 15:04:05
        type: string
      gender:
        description: 性别：1-男、2-女
        type: integer
      ip:
        description: IP
        type: string
      name:
        description: 姓名
        type: string
      orderID:
        description: 订单ID
        type: integer
      phone:
        description: 手机号
        type: string
      ua:
        description: User-Agent
        type: string
    required:
    - birthplace
    - birthtime
    - gender
    - name
    - orderID
    - phone
    type: object
  v1.CreateBaziAnalysisLinkResponse:
    properties:
      code:
        type: integer
      data:
        type: string
      message:
        type: string
    type: object
  v1.CreateMingliRuleConditionRequest:
    properties:
      category:
        description: 类别：2-坐对、3-喜忌、1-全选
        example: 1
        type: integer
      criterion:
        description: 判断依据说明
        example: 判断依据说明
        type: string
      gender:
        description: 日主性别（2-男、3-女、1-无关性别）
        example: 1
        type: integer
      mingliRuleId:
        description: 命理规则ID
        example: 1
        type: integer
      name:
        description: 条件名称
        example: 条件名称
        type: string
      "no":
        description: 条件编号
        example: 条件编号
        type: string
      type:
        description: 条件类型（坐-对）
        example: 1
        type: integer
      weizhiDui:
        description: 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      weizhiZuo:
        description: 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
    required:
    - mingliRuleId
    - name
    - "no"
    type: object
  v1.CreateMingliRuleConditionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreateMingliRuleConditionResponseData'
      message:
        type: string
    type: object
  v1.CreateMingliRuleConditionResponseData:
    properties:
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
    type: object
  v1.CreateMingliRuleRequest:
    properties:
      description:
        description: 规则说明
        example: 规则说明
        type: string
      isEnabled:
        description: 是否启用
        example: true
        type: boolean
      module:
        description: 应用模块
        example: 1
        type: integer
      name:
        description: 规则名称
        example: 规则名称
        type: string
      "no":
        description: 规则编号
        example: NO1001
        maxLength: 32
        type: string
      result:
        description: 判断结果
        example: 判断结果
        type: string
    required:
    - description
    - isEnabled
    - module
    - name
    - "no"
    - result
    type: object
  v1.CreateMingliRuleResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreateMingliRuleResponseData'
      message:
        type: string
    type: object
  v1.CreateMingliRuleResponseData:
    properties:
      id:
        description: 规则ID
        example: 1
        type: integer
    type: object
  v1.CreatePortalArticleRequest:
    properties:
      author:
        type: string
      content:
        type: string
      remark:
        type: string
      sectionId:
        type: integer
      source:
        type: string
      title:
        type: string
    required:
    - author
    - content
    - sectionId
    - source
    - title
    type: object
  v1.CreatePortalArticleResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreatePortalArticleResponseData'
      message:
        type: string
    type: object
  v1.CreatePortalArticleResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.CreatePortalSectionRequest:
    properties:
      name:
        type: string
      remark:
        type: string
      websiteId:
        type: integer
    required:
    - name
    - websiteId
    type: object
  v1.CreatePortalSectionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreatePortalSectionResponseData'
      message:
        type: string
    type: object
  v1.CreatePortalSectionResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.CreatePortalWebsiteRequest:
    properties:
      domain:
        type: string
      name:
        type: string
      remark:
        type: string
    required:
    - domain
    - name
    type: object
  v1.CreatePortalWebsiteResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreatePortalWebsiteResponseData'
      message:
        type: string
    type: object
  v1.CreatePortalWebsiteResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.DeleteMingliRuleConditionRequest:
    properties:
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.DeleteMingliRuleConditionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.DeleteMingliRuleRequest:
    properties:
      id:
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.DeleteMingliRuleResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.DeletePortalArticleRequest:
    properties:
      id:
        type: integer
    required:
    - id
    type: object
  v1.DeletePortalSectionRequest:
    properties:
      id:
        type: integer
    required:
    - id
    type: object
  v1.DeletePortalWebsiteRequest:
    properties:
      id:
        type: integer
    required:
    - id
    type: object
  v1.EnumsAppRequest:
    properties:
      onlySelf:
        example: true
        type: boolean
    type: object
  v1.EnumsAppResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsAppResponseDataItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsAppResponseDataItem:
    properties:
      app:
        type: string
      appID:
        type: string
      clientID:
        type: string
      code:
        example: app_code
        type: string
      id:
        example: 1
        type: integer
      isSelf:
        example: true
        type: boolean
      name:
        example: 应用名称
        type: string
      org:
        type: string
      role:
        type: integer
      wxMpAppID:
        type: string
    type: object
  v1.EnumsDizhiRequest:
    type: object
  v1.EnumsDizhiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsDizhiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsDizhiResponseItem:
    properties:
      dizhi:
        description: 地支
        type: string
      id:
        example: 1
        type: integer
      jieqi:
        description: 节气
        type: string
      name:
        example: 子
        type: string
      shichen:
        description: 时辰
        type: string
      shuxiang:
        description: 属相
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
      yuefen:
        description: 月份
        type: string
      zhongqi:
        description: 中气
        type: string
    type: object
  v1.EnumsGanzhiRequest:
    type: object
  v1.EnumsGanzhiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsGanzhiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsGanzhiResponseItem:
    properties:
      dizhiID:
        example: 1
        type: integer
      id:
        example: 1
        type: integer
      name:
        example: 甲子
        type: string
      tianganID:
        example: 1
        type: integer
    type: object
  v1.EnumsLunarRequest:
    properties:
      year:
        description: 年份
        example: "2020"
        type: string
    required:
    - year
    type: object
  v1.EnumsLunarResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsLunarResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsLunarResponseItem:
    properties:
      days:
        description: 日期
        items:
          $ref: '#/definitions/v1.LunarDay'
        type: array
      month:
        description: 月份
        example: 正月
        type: string
    type: object
  v1.EnumsModuleRequest:
    type: object
  v1.EnumsModuleResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsModuleResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsModuleResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 应用模块名称
        type: string
    type: object
  v1.EnumsNayinRequest:
    type: object
  v1.EnumsNayinResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsNayinResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsNayinResponseItem:
    properties:
      hanyi:
        description: 含义
        example: '...'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 纳音名称
        example: 盖中王
        type: string
      wuxing:
        description: 五行
        example: 木
        type: string
      zhu1:
        description: 纳音名称
        example: 甲
        type: string
      zhu2:
        description: 纳音名称
        example: 子
        type: string
    type: object
  v1.EnumsShenshaRequest:
    type: object
  v1.EnumsShenshaResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShenshaResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsShenshaResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 天乙贵人
        type: string
    type: object
  v1.EnumsShierchangshengRequest:
    type: object
  v1.EnumsShierchangshengResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShierchangshengResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsShierchangshengResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 长生
        type: string
    type: object
  v1.EnumsShishenRequest:
    type: object
  v1.EnumsShishenResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShishenResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsShishenResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 正官
        type: string
    type: object
  v1.EnumsTianganRequest:
    type: object
  v1.EnumsTianganResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsTianganResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTianganResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 甲
        type: string
      tiangan:
        description: 天干
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
    type: object
  v1.EnumsWuxingItemShishen:
    properties:
      hecheng:
        description: 合称
        type: string
      list:
        description: 十神
        items:
          type: string
        type: array
    type: object
  v1.EnumsWuxingRequest:
    type: object
  v1.EnumsWuxingResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsWuxingResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsWuxingResponseItem:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 五行名称
        example: 金
        type: string
      position:
        description: 方位
        type: string
      profession:
        description: 职业
        type: string
      season:
        description: 季节
        type: string
      shishen:
        $ref: '#/definitions/v1.EnumsWuxingItemShishen'
      wuxing:
        description: 五行
        type: string
    type: object
  v1.EnumsXijiRequest:
    type: object
  v1.EnumsXijiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsXijiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsXijiResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 喜
        type: string
    type: object
  v1.EnumsZuoduiPlusRequest:
    type: object
  v1.EnumsZuoduiPlusResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.EnumsZuoduiPlusResponseData'
      message:
        type: string
    type: object
  v1.EnumsZuoduiPlusResponseData:
    additionalProperties: {}
    type: object
  v1.EnumsZuoduiRequest:
    type: object
  v1.EnumsZuoduiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsZuoduiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsZuoduiResponseItem:
    properties:
      dui:
        example: 支
        type: string
      id:
        example: 1
        type: integer
      type:
        example: 1
        type: integer
      zuo:
        example: 干
        type: string
    type: object
  v1.GetMingliRuleConditionDetailRequest:
    properties:
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.GetMingliRuleConditionDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GetMingliRuleConditionDetailResponseData'
      message:
        type: string
    type: object
  v1.GetMingliRuleConditionDetailResponseData:
    properties:
      category:
        description: 类别：2-坐对、3-喜忌、1-全选
        example: 1
        type: integer
      criterion:
        description: 判断依据说明
        example: 判断依据说明
        type: string
      gender:
        description: 日主性别（2-男、3-女、1-无关性别）
        example: 1
        type: integer
      id:
        description: 主键ID
        example: 1
        type: integer
      name:
        description: 条件名称
        example: 条件名称
        type: string
      "no":
        description: 条件编号
        example: 条件编号
        type: string
      type:
        description: 条件类型（坐-对）
        example: 1
        type: integer
      weizhiDui:
        description: 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      weizhiZuo:
        description: 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      xiji:
        description: 喜忌
        items:
          $ref: '#/definitions/v1.MingliRuleConditionXiji'
        type: array
      zuodui:
        description: 坐对
        items:
          $ref: '#/definitions/v1.MingliRuleConditionZuodui'
        type: array
    type: object
  v1.LunarDay:
    properties:
      date:
        description: 日期
        example: "2020-01-01"
        type: string
      lunarDate:
        description: 农历日期
        example: 正月初一
        type: string
      name:
        description: 名称
        example: 初一
        type: string
    type: object
  v1.MatchMingliRuleRequest:
    properties:
      birthday:
        description: 出生时间
        type: string
      currentDate:
        description: 当前时间
        type: string
      gender:
        description: 性别
        type: integer
    type: object
  v1.MatchMingliRuleResponseDataCondition:
    properties:
      criterion:
        description: 条件依据
        example: 依据
        type: string
      id:
        description: 条件ID
        example: 1
        type: integer
    type: object
  v1.MatchMingliRulesResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MatchMingliRulesResponseData'
      message:
        type: string
    type: object
  v1.MatchMingliRulesResponseData:
    properties:
      birth:
        description: 出生信息（索引：0-年、1-月、2-日、3-时）
        items:
          type: string
        type: array
      rules:
        description: 匹配规则
        items:
          $ref: '#/definitions/v1.MatchMingliRulesResponseDataRule'
        type: array
    type: object
  v1.MatchMingliRulesResponseDataRule:
    properties:
      conditions:
        description: 匹配条件
        items:
          $ref: '#/definitions/v1.MatchMingliRuleResponseDataCondition'
        type: array
      id:
        description: 规则ID
        example: 1
        type: integer
      result:
        description: 判断结果
        example: 判断结果
        type: string
    type: object
  v1.MingliRuleConditionXiji:
    properties:
      checked:
        description: 是否选中
        example: true
        type: boolean
      key:
        example: 1
        type: integer
      values:
        items:
          $ref: '#/definitions/model.MingliRuleConditionValue'
        type: array
    type: object
  v1.MingliRuleConditionZuodui:
    properties:
      checked:
        description: 是否选中
        example: true
        type: boolean
      key:
        example: 1
        type: integer
      values:
        items:
          $ref: '#/definitions/model.MingliRuleConditionValue'
        type: array
    type: object
  v1.MpQrCodePageListRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.MpQrCodePageListRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.MpQrCodePageListRequestParam:
    properties:
      appIDs:
        items:
          type: integer
        type: array
      createAtEnd:
        type: string
      createAtStart:
        type: string
      remark:
        type: string
      sceneStr:
        type: string
    type: object
  v1.MpQrCodePageListResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MpQrCodePageListResponseData'
      message:
        type: string
    type: object
  v1.MpQrCodePageListResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.MpQrCodePageListResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.MpQrCodePageListResponseDataItem:
    properties:
      appID:
        type: string
      appName:
        type: string
      createdAt:
        type: string
      imageData:
        items:
          type: integer
        type: array
      imageType:
        type: string
      page:
        type: string
      remark:
        type: string
      sceneStr:
        type: string
    type: object
  v1.MpQrCodeRequest:
    properties:
      appID:
        type: integer
      channelID:
        type: integer
      page:
        type: string
    required:
    - appID
    - channelID
    - page
    type: object
  v1.OaAppMaterialResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.OaAppMaterialResponseData'
      message:
        type: string
    type: object
  v1.OaAppMaterialResponseData:
    properties:
      mediaID:
        type: string
      url:
        type: string
    type: object
  v1.OaCreateMenuRequest:
    properties:
      button:
        items:
          $ref: '#/definitions/request.Button'
        type: array
    type: object
  v1.OaCreateMenuResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.OaCreateMenuResponseData'
      message:
        type: string
    type: object
  v1.OaCreateMenuResponseData:
    type: object
  v1.OaCreateQrcodeRequest:
    properties:
      remark:
        type: string
      scene_str:
        maxLength: 64
        minLength: 1
        type: string
    required:
    - scene_str
    type: object
  v1.OaCreateQrcodeResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.OaCreateQrcodeResponseData'
      message:
        type: string
    type: object
  v1.OaCreateQrcodeResponseData:
    properties:
      expire_seconds:
        type: integer
      ticket:
        type: string
      url:
        type: string
    type: object
  v1.OaGetMenuDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.OaGetMenuDetailResponseData'
      message:
        type: string
    type: object
  v1.OaGetMenuDetailResponseData:
    properties:
      conditionalmenu:
        items:
          $ref: '#/definitions/response.ConditionalMenu'
        type: array
      menu:
        $ref: '#/definitions/response.Menu'
    type: object
  v1.OrderDetailRequest:
    properties:
      orderNo:
        description: 订单编号
        type: string
    required:
    - orderNo
    type: object
  v1.OrderDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.OrderDetailResponseData'
      message:
        type: string
    type: object
  v1.OrderDetailResponseData:
    properties:
      amount:
        description: 订单金额
        type: integer
      app_id:
        type: integer
      createdAt:
        description: 创建时间
        type: string
      expireTime:
        description: 订单过期时间
        type: string
      extraInfo:
        additionalProperties: {}
        description: 额外信息 // 备注
        type: object
      id:
        type: integer
      ip:
        type: string
      orderNo:
        description: 订单号
        type: string
      payAmount:
        description: 支付金额
        type: integer
      payChannel:
        description: 支付渠道：1-微信支付，2-支付宝
        type: integer
      payStatus:
        description: 支付状态：0-待支付，1-支付成功，2-支付失败，3-已退款
        type: integer
      payTime:
        description: 支付时间
        type: string
      productId:
        description: 商品ID
        type: integer
      productSnapshot:
        description: 商品快照
        items:
          type: integer
        type: array
      quantity:
        description: 购买数量
        type: integer
      remark:
        type: string
      source:
        description: 订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5
        type: integer
      status:
        description: 订单状态：0-待支付，1-已完成，2-已取消，3-已退款
        type: integer
      transactionId:
        description: 支付平台交易号
        type: string
      ua:
        type: string
      updatedAt:
        description: 更新时间
        type: string
      userId:
        description: 用户ID
        type: string
    type: object
  v1.PageListAppChannelRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListAppChannelRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListAppChannelRequestParam:
    type: object
  v1.PageListAppChannelResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListAppChannelResponseData'
      message:
        type: string
    type: object
  v1.PageListAppChannelResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListAppChannelResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListAppChannelResponseDataItem:
    properties:
      code:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
      statistics:
        $ref: '#/definitions/v1.ChannelStatistics'
    type: object
  v1.PageListAppUserRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListAppUserRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListAppUserRequestParam:
    properties:
      name:
        type: string
      phone:
        type: string
      roleIDs:
        items:
          type: integer
        type: array
      signupApplicationIDs:
        items:
          type: integer
        type: array
      signupTimeEnd:
        type: string
      signupTimeStart:
        type: string
    type: object
  v1.PageListAppUserResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListAppUserResponseData'
      message:
        type: string
    type: object
  v1.PageListAppUserResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListAppUserResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListAppUserResponseDataItem:
    properties:
      avatar:
        type: string
      channel:
        type: string
      channelID:
        type: integer
      channelName:
        type: string
      countryCode:
        type: string
      displayName:
        type: string
      isMember:
        description: 是否会员：0-否，1-是，2-曾是
        type: integer
      isPaid:
        description: 是否付费
        type: boolean
      lastLoginApp:
        type: string
      lastLoginTime:
        type: string
      memberDuration:
        items:
          $ref: '#/definitions/v1.AppUserMemberDuration'
        type: array
      name:
        type: string
      phone:
        type: string
      signupApplication:
        type: string
      signupApplicationID:
        type: integer
      signupApplicationName:
        type: string
      signupTime:
        type: string
      user_id:
        type: string
    type: object
  v1.PageListAppUserVipRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListAppUserVipRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListAppUserVipRequestParam:
    properties:
      appIDs:
        items:
          type: integer
        type: array
      expireTimeEnd:
        type: string
      expireTimeStart:
        type: string
      name:
        type: string
      phone:
        type: string
      roleIDs:
        items:
          type: integer
        type: array
    type: object
  v1.PageListAppUserVipResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListAppUserVipResponseData'
      message:
        type: string
    type: object
  v1.PageListAppUserVipResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListAppUserVipResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListAppUserVipResponseDataItem:
    properties:
      avatar:
        type: string
      channel:
        type: string
      channelID:
        type: integer
      channelName:
        type: string
      countryCode:
        type: string
      displayName:
        type: string
      expireTime:
        type: string
      name:
        type: string
      phone:
        type: string
      roleID:
        type: integer
      roleName:
        type: string
      signupApplication:
        type: string
      signupApplicationID:
        type: integer
      signupApplicationName:
        type: string
      signupTime:
        type: string
      userID:
        type: string
    type: object
  v1.PageListAppVersionRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListAppVersionRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListAppVersionRequestParam:
    properties:
      appIDs:
        description: 应用ID
        items:
          type: integer
        type: array
      osType:
        description: 1:android, 2:ios
        items:
          type: integer
        type: array
      remark:
        description: 备注
        type: string
      updateNote:
        description: 更新说明
        type: string
      versionName:
        description: 版本名称
        type: string
    type: object
  v1.PageListAppVersionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListAppVersionResponseData'
      message:
        type: string
    type: object
  v1.PageListAppVersionResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListAppVersionResponseDateItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListAppVersionResponseDateItem:
    properties:
      appID:
        description: 应用ID
        type: integer
      appName:
        description: 应用名称
        type: string
      createdAt:
        description: 创建时间
        type: string
      id:
        description: ID
        type: integer
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      osType:
        description: 1:android, 2:ios
        type: integer
      remark:
        description: 备注
        type: string
      status:
        description: 状态：0-未发布、1-已发布、2-已撤回
        type: integer
      updateNote:
        description: 更新说明
        type: string
      updatedAt:
        description: 更新时间
        type: string
      url:
        description: 下载地址
        type: string
      versionCode:
        description: 版本号
        type: integer
      versionName:
        description: 版本名称
        type: string
    type: object
  v1.PageListFeedbackRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListFeedbackRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListFeedbackRequestParam:
    type: object
  v1.PageListFeedbackResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListFeedbackResponseData'
      message:
        type: string
    type: object
  v1.PageListFeedbackResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListFeedbackResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListFeedbackResponseDataItem:
    properties:
      application:
        type: string
      applicationName:
        type: string
      avatar:
        type: string
      content:
        type: string
      createdAtStr:
        type: string
      displayName:
        type: string
      id:
        type: integer
      imageKey:
        items:
          type: string
        type: array
      imageUrl:
        items:
          type: string
        type: array
      name:
        type: string
      phone:
        type: string
      replyContent:
        type: string
      replyImageKey:
        items:
          type: string
        type: array
      replyImageUrl:
        items:
          type: string
        type: array
      replyTimeStr:
        type: string
      userId:
        type: string
    type: object
  v1.PageListMingliRuleConditionRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListMingliRuleConditionRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListMingliRuleConditionRequestParam:
    properties:
      mingliRuleId:
        description: 命理规则ID
        example: 1
        type: integer
    required:
    - mingliRuleId
    type: object
  v1.PageListMingliRuleConditionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListMingliRuleConditionResponseData'
      message:
        type: string
    type: object
  v1.PageListMingliRuleConditionResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListMingliRuleConditionResponseItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListMingliRuleConditionResponseItem:
    properties:
      category:
        description: 类别：2-坐对、3-喜忌、1-全选
        example: 1
        type: integer
      criterion:
        description: 判断依据说明
        example: 判断依据说明
        type: string
      gender:
        description: 日主性别（2-男、3-女、1-无关性别）
        example: 1
        type: integer
      id:
        description: 主键ID
        example: 1
        type: integer
      name:
        description: 条件名称
        example: 条件名称
        type: string
      "no":
        description: 条件编号
        example: 条件编号
        type: string
      type:
        description: 条件类型（坐-对）
        example: 1
        type: integer
      weizhiDui:
        description: 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      weizhiZuo:
        description: 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      xiji:
        description: 喜忌
        items:
          $ref: '#/definitions/v1.MingliRuleConditionXiji'
        type: array
      zuodui:
        description: 坐对
        items:
          $ref: '#/definitions/v1.MingliRuleConditionZuodui'
        type: array
    required:
    - weizhiDui
    - weizhiZuo
    type: object
  v1.PageListMingliRuleRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListMingliRuleRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListMingliRuleRequestParam:
    type: object
  v1.PageListMingliRuleResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListMingliRuleResponseData'
      message:
        type: string
    type: object
  v1.PageListMingliRuleResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListMingliRuleResponseItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListMingliRuleResponseItem:
    properties:
      creatorName:
        example: 创建者
        type: string
      description:
        example: 规则说明
        type: string
      id:
        example: 1
        type: integer
      isEnabled:
        example: true
        type: boolean
      module:
        example: 1
        type: integer
      moduleName:
        example: 应用模块名称
        type: string
      name:
        example: 规则名称
        type: string
      "no":
        example: NO1001
        type: string
      result:
        example: 判断结果
        type: string
      source:
        description: 规则来源：1 管理员 2 用户
        example: 1
        type: integer
    type: object
  v1.PageListPaipanRecordRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListPaipanRecordRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListPaipanRecordRequestParam:
    properties:
      appIDs:
        description: 应用ID：2-排盘、3-万年历、4-运势、5-论财
        items:
          type: integer
        type: array
      application:
        description: 应用标识
        type: string
      bazi:
        description: 八字
        type: string
      birthTimeEnd:
        description: 出生日期结束
        type: string
      birthTimeStart:
        description: 出生日期开始
        type: string
      birthTimeSunEnd:
        description: 真太阳时结束
        type: string
      birthTimeSunStart:
        description: 真太阳时开始
        type: string
      birthplace:
        description: 出生地
        type: string
      gender:
        description: 性别：1-男、2-女
        items:
          type: integer
        type: array
      name:
        description: 命例名称
        type: string
    type: object
  v1.PageListPaipanRecordResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListPaipanRecordResponseData'
      message:
        type: string
    type: object
  v1.PageListPaipanRecordResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListPaipanRecordResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListPaipanRecordResponseDataItem:
    properties:
      appID:
        description: 应用ID
        example: 1
        type: integer
      appName:
        description: 应用名称
        type: string
      appPlatformID:
        description: 应用平台ID
        example: 1
        type: integer
      appPlatformName:
        description: 应用平台名称
        type: string
      bazi:
        description: 八字
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生日期
        example: "2021-01-01 00:00:00"
        type: string
      birthtimeLunar:
        description: 农历出生日期
        type: string
      birthtimeSun:
        description: 真太阳时
        type: string
      createdAt:
        description: 创建时间
        type: string
      createdTime:
        description: 创建时间
        type: string
      gender:
        description: 性别：1-男, 2-女
        example: "1"
        type: string
      id:
        type: integer
      name:
        description: 命例名称
        type: string
      saveTime:
        description: 保存时间
        type: string
      type:
        description: 类型
        type: string
      userAgent:
        description: 用户代理
        type: string
      userID:
        description: 用户ID
        type: string
    type: object
  v1.PageListPortalArticleRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListPortalArticleRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListPortalArticleRequestParam:
    properties:
      sectionIds:
        items:
          type: integer
        type: array
      websiteId:
        type: integer
    type: object
  v1.PageListPortalArticleResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListPortalArticleResponseData'
      message:
        type: string
    type: object
  v1.PageListPortalArticleResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListPortalArticleResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListPortalArticleResponseDataItem:
    properties:
      author:
        type: string
      content:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      remark:
        type: string
      sectionId:
        type: integer
      sectionName:
        type: string
      source:
        type: string
      title:
        type: string
      updatedAt:
        type: string
    type: object
  v1.PageListPortalSectionRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListPortalSectionRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListPortalSectionRequestParam:
    properties:
      websiteId:
        type: integer
    required:
    - websiteId
    type: object
  v1.PageListPortalSectionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListPortalSectionResponseData'
      message:
        type: string
    type: object
  v1.PageListPortalSectionResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListPortalSectionResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListPortalSectionResponseDataItem:
    properties:
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
      updatedAt:
        type: string
      websiteId:
        type: integer
      websiteName:
        type: string
    type: object
  v1.PageListPortalWebsiteRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListAppVersionRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListPortalWebsiteResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListPortalWebsiteResponseData'
      message:
        type: string
    type: object
  v1.PageListPortalWebsiteResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListPortalWebsiteResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListPortalWebsiteResponseDataItem:
    properties:
      createdAt:
        type: string
      domain:
        type: string
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
      updatedAt:
        type: string
    type: object
  v1.PageListUserMingliRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListUserMingliRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListUserMingliRequestParam:
    properties:
      appIDs:
        description: 应用ID：2-排盘、3-万年历、4-运势、5-论财
        items:
          type: integer
        type: array
      bazi:
        description: 八字
        type: string
      birthTimeEnd:
        description: 出生日期结束
        type: string
      birthTimeStart:
        description: 出生日期开始
        type: string
      birthTimeSunEnd:
        description: 真太阳时结束
        type: string
      birthTimeSunStart:
        description: 真太阳时开始
        type: string
      birthplace:
        description: 出生地
        type: string
      gender:
        description: 性别：1-男、2-女
        items:
          type: integer
        type: array
      name:
        description: 命例名称
        type: string
    type: object
  v1.PageListUserMingliResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListUserMingliResponseData'
      message:
        type: string
    type: object
  v1.PageListUserMingliResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListUserMingliResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListUserMingliResponseDataItem:
    properties:
      address:
        description: 地址
        example:
        - 北京市
        - 市辖区
        - 东城区
        items:
          type: string
        type: array
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 2
        type: integer
      appName:
        description: 应用名称
        type: string
      bazi:
        description: 八字：年份干支,月份干支,日期干支,时辰干支
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        example:
        - '["北京市"'
        - '"市辖区"'
        - '"东城区"]'
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间（公历）
        example: "2021-01-01 12:00:00"
        type: string
      birthtimeLunar:
        description: 出生时间（农历）
        example: 2021年闰四月十一子时
        type: string
      birthtimeSun:
        description: 真太阳时
        example: "2021-01-01 12:00:00"
        type: string
      dayun:
        allOf:
        - $ref: '#/definitions/model.UserMingliDayun'
        description: 大运
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
      groupID:
        description: 分组ID
        example: 1
        type: integer
      groupName:
        description: 分组名称
        type: string
      id:
        description: ID
        example: 1
        type: integer
      isDefault:
        description: 是否默认
        example: true
        type: boolean
      lunarBirthtime:
        description: 出生时间（农历）
        example: 2021年闰四月十一子时
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
      userID:
        description: 用户ID
        example: "1"
        type: string
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      xiaoyun:
        allOf:
        - $ref: '#/definitions/model.UserMingliXiaoyun'
        description: 小运
    type: object
  v1.PageListUserOrderRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListUserOrderRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListUserOrderRequestParam:
    properties:
      createdTimeEnd:
        description: 创建时间结束
        type: string
      createdTimeStart:
        description: 创建时间开始
        type: string
      orderNo:
        description: 订单ID
        type: string
      payAmountMax:
        description: 支付金额最大值
        type: integer
      payAmountMin:
        description: 支付金额最小值
        type: integer
      payStatus:
        description: 支付状态：0-待支付、1-已支付、2-支付失败、3-已退款
        items:
          type: integer
        type: array
      payTimeEnd:
        description: 支付时间结束
        type: string
      payTimeStart:
        description: 支付时间开始
        type: string
      productID:
        description: 商品ID
        items:
          type: integer
        type: array
      skuName:
        description: SKU名称
        type: string
      userID:
        description: 用户ID
        items:
          type: string
        type: array
      userName:
        description: 用户名
        type: string
      userPhone:
        description: 用户手机号
        type: string
    type: object
  v1.PageListUserOrderResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListUserOrderResponseData'
      message:
        type: string
    type: object
  v1.PageListUserOrderResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListUserOrderResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListUserOrderResponseDataItem:
    properties:
      amount:
        description: 订单金额
        type: integer
      appId:
        description: app id
        type: string
      appName:
        description: app name
        type: string
      createdTime:
        description: 创建时间
        type: string
      expireTime:
        description: 订单未支付失效时间
        type: string
      ip:
        description: ip
        type: string
      orderNo:
        description: 订单号
        type: string
      payAmount:
        description: 实际支付金额
        type: integer
      payChannel:
        description: 支付渠道：1-微信、2-支付宝
        type: integer
      payStatus:
        description: 支付状态：0-待支付、1-已支付、2-支付失败、3-已退款
        type: integer
      payTime:
        description: 支付时间
        type: string
      productId:
        description: 商品ID
        type: integer
      productSnapshot:
        allOf:
        - $ref: '#/definitions/v1.ProductSnapshot'
        description: 商品快照
      quantity:
        description: 购买数量
        type: integer
      source:
        description: 订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5
        type: integer
      transactionId:
        description: 交易流水号
        type: string
      ua:
        description: ua
        type: string
      userAvatar:
        description: 用户头像
        type: string
      userDisplayName:
        description: 用户昵称
        type: string
      userId:
        description: 用户ID
        type: string
      userName:
        description: 用户名
        type: string
      userPhone:
        description: 用户手机号
        type: string
    type: object
  v1.ProductSnapshot:
    properties:
      enable:
        description: 是否启用
        type: integer
      id:
        description: ID
        type: integer
      remark:
        description: 备注
        type: string
      sku_code:
        description: SKU编码
        type: string
      sku_name:
        description: SKU名称
        type: string
      stock:
        description: 库存
        type: integer
      type:
        description: 类型
        type: integer
    type: object
  v1.PublishAppVersionRequest:
    properties:
      id:
        description: ID
        type: integer
    required:
    - id
    type: object
  v1.PublishAppVersionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.QueryTermRequest:
    properties:
      name:
        example: 术语
        type: string
    type: object
  v1.QueryTermResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.QueryTermResponseData'
      message:
        type: string
    type: object
  v1.QueryTermResponseData:
    properties:
      category1:
        example: 类别1
        type: string
      description:
        example: 描述
        type: string
      name:
        example: 术语
        type: string
    type: object
  v1.QwBillCommodity:
    properties:
      amount:
        description: 商品数量
        type: integer
      description:
        description: 商品描述
        type: string
    type: object
  v1.QwBillContact:
    properties:
      address:
        description: 联系人地址
        type: string
      name:
        description: 联系人姓名
        type: string
      phone:
        description: 联系人电话
        type: string
    type: object
  v1.QwBillMiniProgram:
    properties:
      appid:
        description: 小程序appid
        type: string
      name:
        description: 小程序名称
        type: string
    type: object
  v1.QwBillRefund:
    properties:
      out_refund_no:
        description: 退款单号
        type: string
      refund_comment:
        description: 退款备注
        type: string
      refund_fee:
        description: 退款金额（单位：分）
        type: integer
      refund_reqtime:
        description: 退款发起时间
        type: integer
      refund_status:
        description: 退款状态：0-已申请退款 1-退款处理中 2-退款成功 3-退款关闭 4-退款异常 5-审批中 6-审批失败 7-审批取消
        type: integer
      refund_userid:
        description: 退款发起人ID
        type: string
    type: object
  v1.QwContactFollowUsersRequest:
    type: object
  v1.QwContactFollowUsersResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.QwMember'
        type: array
      message:
        type: string
    type: object
  v1.QwCreateAcquisitionLinkRequest:
    properties:
      appID:
        description: 应用ID
        type: integer
      name:
        description: 联系我的配置名称
        type: string
      platformID:
        description: 平台ID
        type: integer
      skipVerify:
        description: 联系我是否跳过验证
        type: boolean
      userIDs:
        description: 联系我的成员列表
        items:
          type: string
        type: array
    required:
    - appID
    - name
    - platformID
    - userIDs
    type: object
  v1.QwCreateAcquisitionLinkResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.QwCreateContactWayRequest:
    properties:
      addState:
        description: 联系我的添加参数
        type: string
      appID:
        description: 应用ID
        type: integer
      name:
        description: 联系我的配置名称
        type: string
      platformID:
        description: 平台ID
        type: integer
      skipVerify:
        description: 联系我是否跳过验证
        type: boolean
      userIDs:
        description: 联系我的成员列表
        items:
          type: string
        type: array
    required:
    - appID
    - name
    - platformID
    - userIDs
    type: object
  v1.QwCreateContactWayResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.QwDeleteContactWayRequest:
    properties:
      id:
        description: 联系我的配置ID
        type: integer
    required:
    - id
    type: object
  v1.QwDeleteContactWayResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.QwDepartmentTreeNode:
    properties:
      children:
        items:
          $ref: '#/definitions/v1.QwDepartmentTreeNode'
        type: array
      id:
        description: 部门ID
        type: integer
      members:
        description: 部门成员
        items:
          $ref: '#/definitions/v1.QwMember'
        type: array
      name:
        description: 部门名称
        type: string
      order:
        description: 部门排序
        type: integer
      parent_id:
        description: 父部门ID
        type: integer
    type: object
  v1.QwDepartmentsRequest:
    properties:
      containsMember:
        description: 是否包含成员
        type: boolean
      parentID:
        description: 父部门ID
        type: integer
    type: object
  v1.QwDepartmentsResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.QwDepartmentTreeNode'
        type: array
      message:
        type: string
    type: object
  v1.QwMember:
    properties:
      name:
        description: 成员名称
        type: string
      user_id:
        description: 成员UserID
        type: string
    type: object
  v1.QwPageListBillRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.QwPageListBillRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.QwPageListBillRequestParam:
    properties:
      billType:
        description: 交易类型：0-收款记录 1-退款记录
        type: integer
      id:
        description: 联系我的配置ID
        type: integer
      payEndTime:
        description: 支付时间结束
        type: string
      payStartTime:
        description: 支付时间起始
        type: string
      payeeUserIDs:
        description: 收款人UserID列表
        items:
          type: string
        type: array
      tradeState:
        description: 交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款
        type: integer
      transactionID:
        description: 交易单号
        type: string
    type: object
  v1.QwPageListBillResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.QwPageListBillResponseData'
      message:
        type: string
    type: object
  v1.QwPageListBillResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.QwPageListBillResponseItem'
        type: array
      total:
        type: integer
    type: object
  v1.QwPageListBillResponseItem:
    properties:
      bill_type:
        description: 交易类型：0-收款记录 1-退款记录
        type: integer
      commodity_list:
        description: 商品信息详情列表（仅收款记录返回）
        items:
          $ref: '#/definitions/v1.QwBillCommodity'
        type: array
      contact_info:
        allOf:
        - $ref: '#/definitions/v1.QwBillContact'
        description: 联系人信息（如创建收款项目时设置为不需要联系地址，则该字段为空，退款记录不返回该字段）
      external_userid:
        description: 付款人的userid
        type: string
      id:
        description: 主键ID
        type: integer
      mch_id:
        description: 收款商户号id
        type: string
      miniprogram_info:
        allOf:
        - $ref: '#/definitions/v1.QwBillMiniProgram'
        description: 小程序信息（收款方式为小程序时返回该字段）
      out_refund_no:
        description: 商户退款单号（仅退款记录返回该字段）
        type: string
      out_trade_no:
        description: 商户单号（退款记录返回对应收款记录的商户单号）
        type: string
      pay_time:
        description: 支付时间
        type: integer
      payee_userid:
        description: 收款成员/退款成员的userid
        type: string
      payment_type:
        description: 收款方式：0-聊天中收款 1-收款码收款 2-直播间收款 3-产品图册收款 4-转账 5-小程序
        type: integer
      refund_list:
        description: 退款单据详情列表（仅收款记录返回）
        items:
          $ref: '#/definitions/v1.QwBillRefund'
        type: array
      remark:
        description: 收款/退款备注
        type: string
      total_fee:
        description: 收款总金额（单位：分）
        type: integer
      total_refund_fee:
        description: 退款总金额（单位：分）
        type: integer
      trade_state:
        description: 交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款
        type: integer
      transaction_id:
        description: 交易单号
        type: string
    type: object
  v1.QwPageListContactFollowRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.QwPageListContactFollowRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.QwPageListContactFollowRequestParam:
    properties:
      addEndAt:
        description: 添加时间结束
        type: string
      addStartAt:
        description: 添加时间起始
        type: string
      appIDs:
        description: 应用ID
        items:
          type: integer
        type: array
      appPlatformIDs:
        description: 应用平台ID
        items:
          type: integer
        type: array
      delFlag:
        description: 删除标记
        items:
          type: integer
        type: array
      gender:
        description: 性别：1-男 2-女 0-未知
        items:
          type: integer
        type: array
      name:
        description: 昵称
        type: string
      phone:
        description: 手机号（手动填写）
        type: string
      realName:
        description: 真实姓名（手动填写）
        type: string
      remark:
        description: 姓名
        type: string
      userIDs:
        description: 成员UserID列表
        items:
          type: string
        type: array
    type: object
  v1.QwPageListContactFollowResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.QwPageListContactFollowResponseData'
      message:
        type: string
    type: object
  v1.QwPageListContactFollowResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.QwPageListContactFollowResponseItem'
        type: array
      total:
        type: integer
    type: object
  v1.QwPageListContactFollowResponseItem:
    properties:
      addState:
        description: 外部联系人的添加状态
        type: string
      addTime:
        description: 外部联系人的添加时间
        type: integer
      addWay:
        description: 外部联系人添加方式
        type: integer
      appID:
        description: 应用ID（暂无）
        type: string
      appName:
        description: 应用名称（暂无）
        type: string
      appPlatformID:
        description: 应用平台ID（暂无）
        type: integer
      appPlatformName:
        description: 应用平台名称（暂无）
        type: string
      avatar:
        description: 外部联系人的头像
        type: string
      delFlag:
        description: 外部联系人的删除时间
        type: integer
      delSource:
        description: 外部联系人的删除来源
        type: string
      delTime:
        description: 外部联系人的删除时间
        type: integer
      description:
        description: 外部联系人的描述
        type: string
      events:
        description: 外部联系人的跟进事件
        items:
          $ref: '#/definitions/model.QwContactFollowEvent'
        type: array
      gender:
        description: 外部联系人的性别
        type: integer
      id:
        description: 外部联系人的userid
        type: integer
      name:
        description: 外部联系人的名称
        type: string
      phone:
        description: 手机号（手动填写）
        type: string
      realName:
        description: 真实姓名（手动填写）
        type: string
      remark:
        description: 外部联系人的备注
        type: string
      userID:
        description: 外部联系人的userid
        type: string
    type: object
  v1.QwPageListContactWayRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.QwPageListContactWayRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.QwPageListContactWayRequestParam:
    properties:
      addState:
        description: 联系我的添加参数
        type: string
      appIDs:
        description: 应用ID列表
        items:
          type: integer
        type: array
      id:
        description: 联系我的配置ID
        type: integer
      name:
        description: 联系我的配置名称
        type: string
      platformIDs:
        description: 平台ID列表
        items:
          type: integer
        type: array
    type: object
  v1.QwPageListContactWayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.QwPageListContactWayResponseData'
      message:
        type: string
    type: object
  v1.QwPageListContactWayResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.QwPageListContactWayResponseItem'
        type: array
      total:
        type: integer
    type: object
  v1.QwPageListContactWayResponseItem:
    properties:
      addState:
        description: 联系我的添加参数
        type: string
      appID:
        description: 应用ID
        type: string
      appName:
        description: 应用名称
        type: string
      createdTime:
        description: 创建时间
        type: string
      id:
        description: 联系我的配置ID
        type: integer
      link:
        description: 联系我的链接
        type: string
      name:
        description: 联系我的配置名称
        type: string
      platformID:
        description: 平台ID
        type: integer
      platformName:
        description: 平台名称
        type: string
      skipVerify:
        description: 联系我是否跳过验证
        type: boolean
      updatedTime:
        description: 更新时间
        type: string
      userIDs:
        description: 联系我的成员列表
        type: string
    type: object
  v1.QwUpdateContactRequest:
    properties:
      gender:
        description: 性别：1-男 2-女 0-未知
        type: integer
      id:
        type: integer
      phone:
        description: 手机号
        type: string
      realName:
        description: 真实姓名
        type: string
    required:
    - id
    type: object
  v1.QwUpdateContactResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.QwUpdateContactWayRequest:
    properties:
      id:
        description: 联系我的配置ID
        type: integer
      name:
        description: 联系我的配置名称
        type: string
      skipVerify:
        description: 联系我是否跳过验证
        type: boolean
      userIDs:
        description: 联系我的成员列表
        items:
          type: string
        type: array
    required:
    - id
    - name
    - userIDs
    type: object
  v1.QwUpdateContactWayResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.RecallAppVersionRequest:
    properties:
      id:
        description: ID
        type: integer
    required:
    - id
    type: object
  v1.RecallAppVersionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.ReplyFeedbackRequest:
    properties:
      id:
        type: integer
      replyContent:
        type: string
      replyImageKey:
        items:
          type: string
        type: array
    type: object
  v1.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.SetMingliRuleConditionXijiRequest:
    properties:
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
      xiji:
        description: 喜忌
        items:
          $ref: '#/definitions/v1.MingliRuleConditionXiji'
        type: array
    required:
    - id
    - xiji
    type: object
  v1.SetMingliRuleConditionXijiResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.SetMingliRuleConditionZuoduiRequest:
    properties:
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
      zuodui:
        description: 坐对
        items:
          $ref: '#/definitions/v1.MingliRuleConditionZuodui'
        type: array
    required:
    - id
    - zuodui
    type: object
  v1.SetMingliRuleConditionZuoduiResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdateAppChannelRequest:
    properties:
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
    required:
    - id
    - name
    - remark
    type: object
  v1.UpdateAppChannelResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdateAppVersionRequest:
    properties:
      id:
        description: ID
        type: integer
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      remark:
        description: 备注
        type: string
      updateNote:
        description: 更新说明
        type: string
      url:
        description: 下载地址
        type: string
      versionName:
        description: 版本名称：1.2.0
        type: string
    required:
    - id
    - updateNote
    - url
    - versionName
    type: object
  v1.UpdateAppVersionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdateMingliRuleConditionRequest:
    properties:
      category:
        description: 类别：2-坐对、3-喜忌、1-全选
        example: 1
        type: integer
      criterion:
        description: 判断依据说明
        example: 判断依据说明
        type: string
      gender:
        description: 日主性别（2-男、3-女、1-无关性别）
        example: 1
        type: integer
      id:
        description: 命理规则条件ID
        example: 1
        type: integer
      name:
        description: 条件名称
        example: 条件名称
        type: string
      "no":
        description: 条件编号
        example: 条件编号
        type: string
      type:
        description: 条件类型（坐-对）
        example: 1
        type: integer
      weizhiDui:
        description: 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      weizhiZuo:
        description: 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
    required:
    - id
    - name
    - "no"
    type: object
  v1.UpdateMingliRuleConditionResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdateMingliRuleRequest:
    properties:
      description:
        description: 规则说明
        example: 规则说明
        type: string
      id:
        description: 规则ID
        example: 1
        type: integer
      isEnabled:
        description: 是否启用
        example: true
        type: boolean
      module:
        description: 应用模块
        example: 1
        type: integer
      name:
        description: 规则名称
        example: 规则名称
        type: string
      "no":
        description: 规则编号
        example: NO1001
        maxLength: 32
        type: string
      result:
        description: 判断结果
        example: 判断结果
        type: string
    required:
    - description
    - id
    - module
    - name
    - "no"
    - result
    type: object
  v1.UpdateMingliRuleResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdatePortalArticleRequest:
    properties:
      author:
        type: string
      content:
        type: string
      id:
        type: integer
      remark:
        type: string
      source:
        type: string
      title:
        type: string
    required:
    - author
    - content
    - id
    - source
    - title
    type: object
  v1.UpdatePortalSectionRequest:
    properties:
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
    required:
    - id
    - name
    type: object
  v1.UpdatePortalWebsiteRequest:
    properties:
      domain:
        type: string
      id:
        type: integer
      name:
        type: string
      remark:
        type: string
    required:
    - domain
    - id
    - name
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a http server template.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: CMS API
  version: 1.0.0
paths:
  /app/channel/create:
    post:
      consumes:
      - application/json
      description: 创建渠道
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateAppChannelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建渠道
          schema:
            $ref: '#/definitions/v1.CreateAppChannelResponse'
      summary: 创建渠道
      tags:
      - 渠道
  /app/channel/pageList:
    post:
      consumes:
      - application/json
      description: 渠道列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListAppChannelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 渠道列表
          schema:
            $ref: '#/definitions/v1.PageListAppChannelResponse'
      security:
      - BearerAuth: []
      summary: 渠道列表
      tags:
      - 渠道
  /app/channel/update:
    post:
      consumes:
      - application/json
      description: 更新渠道
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateAppChannelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新渠道
          schema:
            $ref: '#/definitions/v1.UpdateAppChannelResponse'
      security:
      - BearerAuth: []
      summary: 更新渠道
      tags:
      - 渠道
  /app/version/create:
    post:
      consumes:
      - application/json
      description: 创建版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateAppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建版本
          schema:
            $ref: '#/definitions/v1.CreateAppVersionResponse'
      security:
      - BearerAuth: []
      summary: 创建版本
      tags:
      - 应用版本
  /app/version/pageList:
    post:
      consumes:
      - application/json
      description: 版本列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListAppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 版本列表
          schema:
            $ref: '#/definitions/v1.PageListAppVersionResponse'
      security:
      - BearerAuth: []
      summary: 版本列表
      tags:
      - 应用版本
  /app/version/publish:
    post:
      consumes:
      - application/json
      description: 发布版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PublishAppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发布版本
          schema:
            $ref: '#/definitions/v1.PublishAppVersionResponse'
      security:
      - BearerAuth: []
      summary: 发布版本
      tags:
      - 应用版本
  /app/version/recall:
    post:
      consumes:
      - application/json
      description: 撤回版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.RecallAppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 撤回版本
          schema:
            $ref: '#/definitions/v1.RecallAppVersionResponse'
      security:
      - BearerAuth: []
      summary: 撤回版本
      tags:
      - 应用版本
  /app/version/update:
    post:
      consumes:
      - application/json
      description: 更新版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateAppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新版本
          schema:
            $ref: '#/definitions/v1.UpdateAppVersionResponse'
      security:
      - BearerAuth: []
      summary: 更新版本
      tags:
      - 应用版本
  /appUser/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询用户列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListAppUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListAppUserResponse'
      summary: 分页查询用户列表
      tags:
      - 用户
  /appUser/pageListVip:
    post:
      consumes:
      - application/json
      description: 分页查询VIP用户列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListAppUserVipRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListAppUserVipResponse'
      summary: 分页查询VIP用户列表
      tags:
      - 用户
  /atlasProduct/create:
    post:
      consumes:
      - application/json
      description: 创建产品
      parameters:
      - description: 创建产品请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.AtlasCreateProductReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建产品成功
          schema:
            allOf:
            - $ref: '#/definitions/v1.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: 创建产品
      tags:
      - 产品
  /atlasProduct/delete:
    post:
      consumes:
      - application/json
      description: 删除产品
      parameters:
      - description: 删除产品请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.AtlasDeleteProductReq'
      produces:
      - application/json
      responses:
        "200":
          description: 删除产品成功
          schema:
            allOf:
            - $ref: '#/definitions/v1.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: 删除产品
      tags:
      - 产品
  /atlasProduct/list:
    post:
      consumes:
      - application/json
      description: 获取产品列表
      parameters:
      - description: 获取产品列表请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.AtlasListProductRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取产品列表成功
          schema:
            allOf:
            - $ref: '#/definitions/v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/v1.AtlasListProductResponse'
              type: object
      summary: 获取产品列表
      tags:
      - 产品
  /atlasProduct/update:
    post:
      consumes:
      - application/json
      description: 更新产品
      parameters:
      - description: 更新产品请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.AtlasUpdateProductReq'
      produces:
      - application/json
      responses:
        "200":
          description: 更新产品成功
          schema:
            allOf:
            - $ref: '#/definitions/v1.Response'
            - properties:
                data:
                  type: string
              type: object
      summary: 更新产品
      tags:
      - 产品
  /bal/create:
    post:
      consumes:
      - application/json
      description: 创建八字分析链接
      parameters:
      - description: 创建八字分析链接请求
        in: body
        name: param
        required: true
        schema:
          $ref: '#/definitions/v1.CreateBaziAnalysisLinkRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CreateBaziAnalysisLinkResponse'
      summary: 创建八字分析链接
      tags:
      - 八字分析链接
  /enums/app:
    post:
      consumes:
      - application/json
      description: App
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsAppRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsAppResponse'
      security:
      - BearerAuth: []
      summary: App
      tags:
      - 枚举
  /enums/dizhi:
    post:
      consumes:
      - application/json
      description: 地支
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsDizhiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsDizhiResponse'
      security:
      - BearerAuth: []
      summary: 地支
      tags:
      - 枚举
  /enums/ganzhi:
    post:
      consumes:
      - application/json
      description: 干支
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsGanzhiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsGanzhiResponse'
      security:
      - BearerAuth: []
      summary: 干支
      tags:
      - 枚举
  /enums/lunar:
    post:
      consumes:
      - application/json
      description: 获取农历列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLunarRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLunarResponse'
      summary: 获取农历列表
      tags:
      - 枚举
  /enums/module:
    post:
      consumes:
      - application/json
      description: 模块
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsModuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsModuleResponse'
      security:
      - BearerAuth: []
      summary: 模块
      tags:
      - 枚举
  /enums/nayin:
    post:
      consumes:
      - application/json
      description: 纳音
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsNayinRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsNayinResponse'
      security:
      - BearerAuth: []
      summary: 纳音
      tags:
      - 枚举
  /enums/shensha:
    post:
      consumes:
      - application/json
      description: 神煞
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsShenshaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShenshaResponse'
      security:
      - BearerAuth: []
      summary: 神煞
      tags:
      - 枚举
  /enums/shierchangsheng:
    post:
      consumes:
      - application/json
      description: 十二长生
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsShierchangshengRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShierchangshengResponse'
      security:
      - BearerAuth: []
      summary: 十二长生
      tags:
      - 枚举
  /enums/shishen:
    post:
      consumes:
      - application/json
      description: 十神
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsShishenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShishenResponse'
      security:
      - BearerAuth: []
      summary: 十神
      tags:
      - 枚举
  /enums/tiangan:
    post:
      consumes:
      - application/json
      description: 天干
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsTianganRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsTianganResponse'
      security:
      - BearerAuth: []
      summary: 天干
      tags:
      - 枚举
  /enums/wuxing:
    post:
      consumes:
      - application/json
      description: 五行
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsWuxingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsWuxingResponse'
      security:
      - BearerAuth: []
      summary: 五行
      tags:
      - 枚举
  /enums/xiji:
    post:
      consumes:
      - application/json
      description: 喜忌
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsXijiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsXijiResponse'
      security:
      - BearerAuth: []
      summary: 喜忌
      tags:
      - 枚举
  /enums/zuodui:
    post:
      consumes:
      - application/json
      description: 坐-对
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsZuoduiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsZuoduiResponse'
      security:
      - BearerAuth: []
      summary: 坐-对
      tags:
      - 枚举
  /enums/zuoduiPlus:
    post:
      consumes:
      - application/json
      description: 坐-对+
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsZuoduiPlusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsZuoduiPlusResponse'
      summary: 坐-对+
      tags:
      - 枚举
  /feedback/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询反馈列表
      parameters:
      - description: 分页查询反馈列表请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListFeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListFeedbackResponse'
      security:
      - BearerAuth: []
      summary: 分页查询反馈列表
      tags:
      - 反馈
  /feedback/reply:
    post:
      consumes:
      - application/json
      description: 回复反馈
      parameters:
      - description: 回复反馈请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.ReplyFeedbackRequest'
      produces:
      - application/json
      responses: {}
      security:
      - BearerAuth: []
      summary: 回复反馈
      tags:
      - 反馈
  /mingliRule/create:
    post:
      consumes:
      - application/json
      description: 创建命理规则
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateMingliRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CreateMingliRuleResponse'
      security:
      - BearerAuth: []
      summary: 创建命理规则
      tags:
      - 命理规则
  /mingliRule/delete:
    post:
      consumes:
      - application/json
      description: 删除命理规则
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteMingliRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeleteMingliRuleResponse'
      security:
      - BearerAuth: []
      summary: 删除命理规则
      tags:
      - 命理规则
  /mingliRule/match:
    post:
      consumes:
      - application/json
      description: 匹配命理规则
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MatchMingliRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MatchMingliRulesResponse'
      summary: 匹配命理规则
      tags:
      - 命理规则
  /mingliRule/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询命理规则
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListMingliRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListMingliRuleResponse'
      security:
      - BearerAuth: []
      summary: 分页查询命理规则
      tags:
      - 命理规则
  /mingliRule/update:
    post:
      consumes:
      - application/json
      description: 更新命理规则
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateMingliRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.UpdateMingliRuleResponse'
      security:
      - BearerAuth: []
      summary: 更新命理规则
      tags:
      - 命理规则
  /mingliRuleCondition/create:
    post:
      consumes:
      - application/json
      description: 创建命理规则条件
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateMingliRuleConditionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CreateMingliRuleConditionResponse'
      security:
      - BearerAuth: []
      summary: 创建命理规则条件
      tags:
      - 命理规则条件
  /mingliRuleCondition/delete:
    post:
      consumes:
      - application/json
      description: 删除命理规则条件
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteMingliRuleConditionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeleteMingliRuleConditionResponse'
      security:
      - BearerAuth: []
      summary: 删除命理规则条件
      tags:
      - 命理规则条件
  /mingliRuleCondition/detail:
    post:
      consumes:
      - application/json
      description: 获取命理规则条件
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetMingliRuleConditionDetailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetMingliRuleConditionDetailResponse'
      security:
      - BearerAuth: []
      summary: 获取命理规则条件
      tags:
      - 命理规则条件
  /mingliRuleCondition/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询命理规则条件
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListMingliRuleConditionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListMingliRuleConditionResponse'
      security:
      - BearerAuth: []
      summary: 分页查询命理规则条件
      tags:
      - 命理规则条件
  /mingliRuleCondition/setXiji:
    post:
      consumes:
      - application/json
      description: 设置喜忌
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.SetMingliRuleConditionXijiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.SetMingliRuleConditionXijiResponse'
      security:
      - BearerAuth: []
      summary: 设置喜忌
      tags:
      - 命理规则条件
  /mingliRuleCondition/setZuodui:
    post:
      consumes:
      - application/json
      description: 设置坐对
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.SetMingliRuleConditionZuoduiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.SetMingliRuleConditionZuoduiResponse'
      security:
      - BearerAuth: []
      summary: 设置坐对
      tags:
      - 命理规则条件
  /mingliRuleCondition/update:
    post:
      consumes:
      - application/json
      description: 更新命理规则条件
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateMingliRuleConditionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.UpdateMingliRuleConditionResponse'
      security:
      - BearerAuth: []
      summary: 更新命理规则条件
      tags:
      - 命理规则条件
  /mp/qrcode/create:
    post:
      consumes:
      - application/json
      description: 小程序生成二维码
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MpQrCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: file
      security:
      - BearerAuth: []
      summary: 小程序生成二维码
      tags:
      - 小程序
  /mp/qrcode/pageList:
    post:
      consumes:
      - application/json
      description: 小程序二维码列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MpQrCodePageListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 二维码列表
          schema:
            $ref: '#/definitions/v1.MpQrCodePageListResponse'
      security:
      - BearerAuth: []
      summary: 小程序二维码列表
      tags:
      - 小程序
  /oa/material/add:
    post:
      consumes:
      - application/json
      description: 公众号上传永久素材
      parameters:
      - description: 素材类型
        in: formData
        name: type
        required: true
        type: string
      - description: 素材备注
        in: formData
        name: remark
        required: true
        type: string
      - description: 素材文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.OaAppMaterialResponse'
      security:
      - BearerAuth: []
      summary: 公众号上传永久素材
      tags:
      - 微信公众号
  /oa/menu/create:
    post:
      consumes:
      - application/json
      description: 创建公众号菜单
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.OaCreateMenuRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.OaCreateMenuResponse'
      security:
      - BearerAuth: []
      summary: 创建公众号菜单
      tags:
      - 微信公众号
  /oa/menu/detail:
    post:
      consumes:
      - application/json
      description: 获取公众号菜单
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.OaGetMenuDetailResponse'
      security:
      - BearerAuth: []
      summary: 获取公众号菜单
      tags:
      - 微信公众号
  /oa/qrcode/create:
    post:
      consumes:
      - application/json
      description: 创建公众号自定义参数二维码
      parameters:
      - description: body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.OaCreateQrcodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.OaCreateQrcodeResponse'
      security:
      - BearerAuth: []
      summary: 创建公众号自定义参数二维码
      tags:
      - 微信公众号
  /order/detail:
    post:
      consumes:
      - application/json
      description: 获取订单详情
      parameters:
      - description: 获取订单详情请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.OrderDetailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取订单详情成功
          schema:
            $ref: '#/definitions/v1.OrderDetailResponse'
      summary: 获取订单详情
      tags:
      - 订单
  /order/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询用户订单
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListUserOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListUserOrderResponse'
      security:
      - BearerAuth: []
      summary: 分页查询用户订单
      tags:
      - 用户订单
  /paipanRecord/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询用户排盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListPaipanRecordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListPaipanRecordResponse'
      security:
      - BearerAuth: []
      summary: 分页查询用户排盘记录
      tags:
      - 用户排盘记录
  /portal/article/create:
    post:
      consumes:
      - application/json
      description: 创建门户文章
      parameters:
      - description: 创建门户文章请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreatePortalArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建门户文章成功
          schema:
            $ref: '#/definitions/v1.CreatePortalArticleResponse'
      security:
      - BearerAuth: []
      summary: 创建门户文章
      tags:
      - 门户
  /portal/article/delete:
    post:
      consumes:
      - application/json
      description: 删除门户文章
      parameters:
      - description: 删除门户文章请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeletePortalArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除门户文章成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 删除门户文章
      tags:
      - 门户
  /portal/article/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询门户文章
      parameters:
      - description: 分页查询门户文章请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListPortalArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分页查询门户文章成功
          schema:
            $ref: '#/definitions/v1.PageListPortalArticleResponse'
      summary: 分页查询门户文章
      tags:
      - 门户
  /portal/article/update:
    post:
      consumes:
      - application/json
      description: 更新门户文章
      parameters:
      - description: 更新门户文章请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdatePortalArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新门户文章成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 更新门户文章
      tags:
      - 门户
  /portal/section/create:
    post:
      consumes:
      - application/json
      description: 创建门户栏目
      parameters:
      - description: 创建门户栏目请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreatePortalSectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建门户栏目成功
          schema:
            $ref: '#/definitions/v1.CreatePortalSectionResponse'
      security:
      - BearerAuth: []
      summary: 创建门户栏目
      tags:
      - 门户
  /portal/section/delete:
    post:
      consumes:
      - application/json
      description: 删除门户栏目
      parameters:
      - description: 删除门户栏目请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeletePortalSectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除门户栏目成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 删除门户栏目
      tags:
      - 门户
  /portal/section/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询门户栏目
      parameters:
      - description: 分页查询门户栏目请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListPortalSectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分页查询门户栏目成功
          schema:
            $ref: '#/definitions/v1.PageListPortalSectionResponse'
      summary: 分页查询门户栏目
      tags:
      - 门户
  /portal/section/update:
    post:
      consumes:
      - application/json
      description: 更新门户栏目
      parameters:
      - description: 更新门户栏目请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdatePortalSectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新门户栏目成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 更新门户栏目
      tags:
      - 门户
  /portal/website/create:
    post:
      consumes:
      - application/json
      description: 创建门户网站
      parameters:
      - description: 创建门户网站请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreatePortalWebsiteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建门户网站成功
          schema:
            $ref: '#/definitions/v1.CreatePortalWebsiteResponse'
      security:
      - BearerAuth: []
      summary: 创建门户网站
      tags:
      - 门户
  /portal/website/delete:
    post:
      consumes:
      - application/json
      description: 删除门户网站
      parameters:
      - description: 删除门户网站请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeletePortalWebsiteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除门户网站成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 删除门户网站
      tags:
      - 门户
  /portal/website/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询门户网站
      parameters:
      - description: 分页查询门户网站请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListPortalWebsiteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分页查询门户网站成功
          schema:
            $ref: '#/definitions/v1.PageListPortalWebsiteResponse'
      summary: 分页查询门户网站
      tags:
      - 门户
  /portal/website/update:
    post:
      consumes:
      - application/json
      description: 更新门户网站
      parameters:
      - description: 更新门户网站请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdatePortalWebsiteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新门户网站成功
          schema:
            $ref: '#/definitions/v1.Response'
      security:
      - BearerAuth: []
      summary: 更新门户网站
      tags:
      - 门户
  /qw/acquisitionLink/create:
    post:
      consumes:
      - application/json
      description: 创建获客链接
      parameters:
      - description: 请求参数
        in: body
        name: createAcquisitionLink
        required: true
        schema:
          $ref: '#/definitions/v1.QwCreateAcquisitionLinkRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwCreateAcquisitionLinkResponse'
      security:
      - BearerAuth: []
      summary: 创建获客链接
      tags:
      - 企业微信
  /qw/bill/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询企微收款记录
      parameters:
      - description: 请求参数
        in: body
        name: pageListBill
        required: true
        schema:
          $ref: '#/definitions/v1.QwPageListBillRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwPageListBillResponse'
      security:
      - BearerAuth: []
      summary: 分页查询企微收款记录
      tags:
      - 企业微信
  /qw/contact/follow/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询客户跟进记录
      parameters:
      - description: 请求参数
        in: body
        name: pageListContactFollow
        required: true
        schema:
          $ref: '#/definitions/v1.QwPageListContactFollowRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwPageListContactFollowResponse'
      security:
      - BearerAuth: []
      summary: 分页查询客户跟进记录
      tags:
      - 企业微信
  /qw/contact/follow/users:
    post:
      consumes:
      - application/json
      description: 获取客户跟进人员列表
      parameters:
      - description: 请求参数
        in: body
        name: contactFollowUsers
        required: true
        schema:
          $ref: '#/definitions/v1.QwContactFollowUsersRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwContactFollowUsersResponse'
      security:
      - BearerAuth: []
      summary: 获取客户跟进人员列表
      tags:
      - 企业微信
  /qw/contact/update:
    post:
      consumes:
      - application/json
      description: 更新客户信息
      parameters:
      - description: 请求参数
        in: body
        name: updateContact
        required: true
        schema:
          $ref: '#/definitions/v1.QwUpdateContactRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwUpdateContactResponse'
      security:
      - BearerAuth: []
      summary: 更新客户信息
      tags:
      - 企业微信
  /qw/contactWay/create:
    post:
      consumes:
      - application/json
      description: 配置客户联系「联系我」方式
      parameters:
      - description: 请求参数
        in: body
        name: addContactWay
        required: true
        schema:
          $ref: '#/definitions/v1.QwCreateContactWayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwCreateContactWayResponse'
      security:
      - BearerAuth: []
      summary: 配置客户联系「联系我」方式
      tags:
      - 企业微信
  /qw/contactWay/delete:
    post:
      consumes:
      - application/json
      description: 删除客户联系「联系我」方式
      parameters:
      - description: 请求参数
        in: body
        name: deleteContactWay
        required: true
        schema:
          $ref: '#/definitions/v1.QwDeleteContactWayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwDeleteContactWayResponse'
      security:
      - BearerAuth: []
      summary: 删除客户联系「联系我」方式
      tags:
      - 企业微信
  /qw/contactWay/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询客户联系「联系我」方式
      parameters:
      - description: 请求参数
        in: body
        name: pageListContactWay
        required: true
        schema:
          $ref: '#/definitions/v1.QwPageListContactWayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwPageListContactWayResponse'
      security:
      - BearerAuth: []
      summary: 分页查询客户联系「联系我」方式
      tags:
      - 企业微信
  /qw/contactWay/update:
    post:
      consumes:
      - application/json
      description: 更新客户联系「联系我」方式
      parameters:
      - description: 请求参数
        in: body
        name: updateContactWay
        required: true
        schema:
          $ref: '#/definitions/v1.QwUpdateContactWayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwUpdateContactWayResponse'
      security:
      - BearerAuth: []
      summary: 更新客户联系「联系我」方式
      tags:
      - 企业微信
  /qw/departments:
    post:
      consumes:
      - application/json
      description: 获取企业微信部门列表
      parameters:
      - description: 请求参数
        in: body
        name: departments
        required: true
        schema:
          $ref: '#/definitions/v1.QwDepartmentsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回数据
          schema:
            $ref: '#/definitions/v1.QwDepartmentsResponse'
      security:
      - BearerAuth: []
      summary: 获取企业微信部门列表
      tags:
      - 企业微信
  /term/query:
    post:
      consumes:
      - application/json
      description: 查询术语
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.QueryTermRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.QueryTermResponse'
      summary: 查询术语
      tags:
      - 术语
  /userMingli/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询用户命例
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 分页查询用户命例
      tags:
      - 用户命例
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
