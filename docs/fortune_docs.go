// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplatefortune = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/app/version/newest": {
            "post": {
                "description": "最新版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用版本"
                ],
                "summary": "最新版本",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CheckAppUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "最新版本",
                        "schema": {
                            "$ref": "#/definitions/v1.CheckAppUpdateResponse"
                        }
                    }
                }
            }
        },
        "/date/day": {
            "post": {
                "description": "获取本日日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本日日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarDayResponse"
                        }
                    }
                }
            }
        },
        "/date/month": {
            "post": {
                "description": "获取本月日历",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日历"
                ],
                "summary": "获取本月日历",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.CalendarMonthResponse"
                        }
                    }
                }
            }
        },
        "/datetime/fromSizhu": {
            "post": {
                "description": "从四柱获取时间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "时间"
                ],
                "summary": "从四柱获取时间",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.GetDatetimeBySizhuRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.GetDatetimeBySizhuResponse"
                        }
                    }
                }
            }
        },
        "/enums/location": {
            "post": {
                "description": "获取地区列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取地区列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLocationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLocationResponse"
                        }
                    }
                }
            }
        },
        "/enums/lunar": {
            "post": {
                "description": "获取农历列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "枚举"
                ],
                "summary": "获取农历列表",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLunarRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.EnumsLunarResponse"
                        }
                    }
                }
            }
        },
        "/location": {
            "post": {
                "description": "获取地区树",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "地区"
                ],
                "summary": "获取地区树",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.LocationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.LocationResponse"
                        }
                    }
                }
            }
        },
        "/paipanRecord/own": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "占有排盘记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "排盘"
                ],
                "summary": "占有排盘记录",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.PaipanRecordOwnRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.PaipanRecordOwnResponse"
                        }
                    }
                }
            }
        },
        "/yunshi": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "2025运势",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "2025运势"
                ],
                "summary": "2025运势",
                "parameters": [
                    {
                        "description": "params",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.YunshiRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.YunshiResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "model.Guaxiang": {
            "type": "object",
            "properties": {
                "duanri": {
                    "description": "断日",
                    "type": "string"
                },
                "gejue": {
                    "description": "歌诀",
                    "type": "string"
                },
                "guaji": {
                    "description": "卦吉",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "idiom": {
                    "description": "成语",
                    "type": "string"
                },
                "jieshi": {
                    "description": "解释",
                    "type": "string"
                },
                "name": {
                    "description": "卦名",
                    "type": "string"
                },
                "topdown": {
                    "description": "上下卦",
                    "type": "string"
                },
                "value": {
                    "description": "卦值",
                    "type": "integer"
                },
                "yao1": {
                    "description": "爻1",
                    "type": "integer"
                },
                "yao2": {
                    "description": "爻2",
                    "type": "integer"
                },
                "yao3": {
                    "description": "爻3",
                    "type": "integer"
                },
                "yao4": {
                    "description": "爻4",
                    "type": "integer"
                },
                "yao5": {
                    "description": "爻5",
                    "type": "integer"
                },
                "yao6": {
                    "description": "爻6",
                    "type": "integer"
                },
                "yaoValue": {
                    "description": "爻值",
                    "type": "integer"
                }
            }
        },
        "v1.CalendarDayRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer",
                    "example": 3
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2020-01-01"
                }
            }
        },
        "v1.CalendarDayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CalendarDayResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CalendarDayResponseData": {
            "type": "object",
            "properties": {
                "bazi1": {
                    "description": "八字1（年份干支）",
                    "type": "string",
                    "example": "庚申"
                },
                "bazi2": {
                    "description": "八字2（月份干支）",
                    "type": "string",
                    "example": "壬午"
                },
                "bazi2Next": {
                    "description": "八字2（下个月份干支）",
                    "type": "string",
                    "example": "壬午"
                },
                "bazi3": {
                    "description": "八字3（日期干支）",
                    "type": "string",
                    "example": "辛巳"
                },
                "caiLocation": {
                    "description": "财位",
                    "type": "string",
                    "example": "东北"
                },
                "constellation": {
                    "description": "星座",
                    "type": "string",
                    "example": "双鱼座"
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2099-03-12"
                },
                "festival": {
                    "description": "节日",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北方小年",
                        "南方小年"
                    ]
                },
                "fuLocation": {
                    "description": "福位",
                    "type": "string",
                    "example": "西南"
                },
                "heidao": {
                    "description": "黑道",
                    "type": "string",
                    "example": "白虎"
                },
                "hou": {
                    "description": "七十二侯",
                    "type": "string",
                    "example": "半夏生"
                },
                "huangdao": {
                    "description": "黄道",
                    "type": "string",
                    "example": "青龙"
                },
                "ji": {
                    "description": "忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "阴宅破土",
                        "安葬",
                        "启攒",
                        "探亲访友"
                    ]
                },
                "jieqi": {
                    "description": "节气（今日或之前的最后一个节气）",
                    "type": "string",
                    "example": "大雪"
                },
                "jieqiDate": {
                    "description": "节气日期",
                    "type": "string",
                    "example": "2006-01-02"
                },
                "jieqiTime": {
                    "description": "节气时间",
                    "type": "string",
                    "example": "2006-01-02 15:00:59"
                },
                "jishen": {
                    "description": "吉神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "天德合",
                        "月德合"
                    ]
                },
                "luLocation": {
                    "description": "禄位",
                    "type": "string",
                    "example": "东南"
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "二月廿一"
                },
                "pengzubaiji": {
                    "description": "彭祖百忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "乙不栽植 千株不长",
                        "未不服药 毒气入肠"
                    ]
                },
                "pengzubaijiOverview": {
                    "description": "彭祖百忌概述",
                    "type": "string",
                    "example": "猴日冲虎煞南"
                },
                "shierjianri": {
                    "description": "十二建日",
                    "type": "string",
                    "example": "定日"
                },
                "taishen": {
                    "description": "胎神",
                    "type": "string",
                    "example": "房床厕外"
                },
                "taishenLocation": {
                    "description": "胎神位置",
                    "type": "string",
                    "example": "西北"
                },
                "times": {
                    "description": "时辰（共13个，包含早子时与晚子时）",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.CalendarShichen"
                    }
                },
                "weekday": {
                    "description": "星期",
                    "type": "string",
                    "example": "星期四"
                },
                "wuxing": {
                    "description": "五行",
                    "type": "string",
                    "example": "山下火"
                },
                "xiLocation": {
                    "description": "喜位",
                    "type": "string",
                    "example": "西北"
                },
                "xingxiu": {
                    "description": "星宿",
                    "type": "string",
                    "example": "张月鹿"
                },
                "xiongshen": {
                    "description": "凶神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "月破",
                        "大耗",
                        "四击",
                        "九空"
                    ]
                },
                "yellowYears": {
                    "description": "黄帝纪年：公元年+2697",
                    "type": "integer",
                    "example": 4721
                },
                "yellowYearsZh": {
                    "description": "黄帝纪年",
                    "type": "string",
                    "example": "四千七百二十一"
                },
                "yi": {
                    "description": "宜",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "祭祀",
                        "打扫",
                        "破屋坏垣"
                    ]
                },
                "zeri": {
                    "description": "择日",
                    "type": "string",
                    "example": "大吉"
                },
                "zodiac": {
                    "description": "生肖",
                    "type": "string",
                    "example": "鼠"
                }
            }
        },
        "v1.CalendarEachDayOfMonth": {
            "type": "object",
            "properties": {
                "bazi": {
                    "description": "八字",
                    "type": "string",
                    "example": "庚申"
                },
                "currentMonth": {
                    "description": "是否为当前月份",
                    "type": "boolean",
                    "example": true
                },
                "date": {
                    "description": "公历日期",
                    "type": "string",
                    "example": "2020-01-01"
                },
                "holidayOff": {
                    "description": "节假日调休：1休，2班",
                    "type": "integer",
                    "example": 1
                },
                "ji": {
                    "description": "忌",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "阴宅破土",
                        "安葬",
                        "启攒",
                        "探亲访友"
                    ]
                },
                "jieqi": {
                    "description": "节气",
                    "type": "string",
                    "example": "大雪"
                },
                "jieqiTime": {
                    "description": "节气时间",
                    "type": "string"
                },
                "jieri": {
                    "description": "节日",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "北方小年",
                        "南方小年"
                    ]
                },
                "liuriShensha": {
                    "description": "流日神煞",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "二月廿一"
                },
                "vipShishen": {
                    "description": "VIP的干十神与支十神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "weekday": {
                    "description": "星期",
                    "type": "string",
                    "example": "星期四"
                },
                "yi": {
                    "description": "宜",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "祭祀",
                        "打扫",
                        "破屋坏垣"
                    ]
                }
            }
        },
        "v1.CalendarMonthRequest": {
            "type": "object",
            "properties": {
                "appID": {
                    "description": "应用：2-排盘、3-万年历、4-运势、5-论财",
                    "type": "integer"
                },
                "month": {
                    "description": "公历月份",
                    "type": "string",
                    "example": "2020-01"
                }
            }
        },
        "v1.CalendarMonthResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "type": "array",
                        "items": {
                            "$ref": "#/definitions/v1.CalendarEachDayOfMonth"
                        }
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CalendarShichen": {
            "type": "object",
            "properties": {
                "bazi": {
                    "description": "八字",
                    "type": "string",
                    "example": "丙子"
                },
                "caiLocation": {
                    "description": "财位",
                    "type": "string",
                    "example": "财神东北"
                },
                "chong": {
                    "description": "冲",
                    "type": "string",
                    "example": "虎"
                },
                "fuLocation": {
                    "description": "福位",
                    "type": "string",
                    "example": "福神西南"
                },
                "ji": {
                    "description": "忌",
                    "type": "string",
                    "example": "忌"
                },
                "jixiong": {
                    "description": "吉",
                    "type": "string",
                    "example": "吉"
                },
                "luLocation": {
                    "description": "禄位",
                    "type": "string",
                    "example": "阳贵东南"
                },
                "sha": {
                    "description": "煞",
                    "type": "string",
                    "example": "南"
                },
                "time": {
                    "description": "时辰",
                    "type": "string",
                    "example": "23:00-00:59"
                },
                "xiLocation": {
                    "description": "喜位",
                    "type": "string",
                    "example": "喜神西北"
                },
                "yi": {
                    "description": "宜",
                    "type": "string",
                    "example": "宜"
                }
            }
        },
        "v1.CheckAppUpdateRequest": {
            "type": "object",
            "required": [
                "osType",
                "versionName"
            ],
            "properties": {
                "osType": {
                    "description": "1:android, 2:ios",
                    "type": "integer"
                },
                "versionName": {
                    "description": "版本名称",
                    "type": "string"
                }
            }
        },
        "v1.CheckAppUpdateResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.CheckAppUpdateResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.CheckAppUpdateResponseData": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "isForceUpdate": {
                    "description": "是否强制更新",
                    "type": "boolean"
                },
                "isHotUpdate": {
                    "description": "是否热更新",
                    "type": "boolean"
                },
                "updateNote": {
                    "description": "更新说明",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "url": {
                    "description": "下载地址",
                    "type": "string"
                },
                "versionCode": {
                    "description": "版本号",
                    "type": "integer"
                },
                "versionName": {
                    "description": "版本名称",
                    "type": "string"
                }
            }
        },
        "v1.EnumsLocationRequest": {
            "type": "object",
            "properties": {
                "overseas": {
                    "description": "是否海外",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "v1.EnumsLocationResponse": {
            "type": "object",
            "properties": {
                "EnumsLocationResponseData": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LocationTree"
                    }
                },
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsLunarRequest": {
            "type": "object",
            "required": [
                "year"
            ],
            "properties": {
                "year": {
                    "description": "年份",
                    "type": "string",
                    "example": "2020"
                }
            }
        },
        "v1.EnumsLunarResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.EnumsLunarResponseItem"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.EnumsLunarResponseItem": {
            "type": "object",
            "properties": {
                "days": {
                    "description": "日期",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LunarDay"
                    }
                },
                "month": {
                    "description": "月份",
                    "type": "string",
                    "example": "正月"
                }
            }
        },
        "v1.GetDatetimeBySizhuRequest": {
            "type": "object",
            "properties": {
                "endYear": {
                    "description": "默认2099",
                    "type": "integer",
                    "example": 2099
                },
                "sizhu": {
                    "description": "四柱",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "癸亥",
                        "戊午",
                        "壬寅",
                        "己酉"
                    ]
                },
                "startYear": {
                    "description": "默认1801",
                    "type": "integer",
                    "example": 1801
                }
            }
        },
        "v1.GetDatetimeBySizhuResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.LocationRequest": {
            "type": "object",
            "properties": {
                "overseas": {
                    "description": "是否海外",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "v1.LocationResponse": {
            "type": "object",
            "properties": {
                "EnumsLocationResponseData": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LocationTree"
                    }
                },
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.LocationTree": {
            "type": "object",
            "properties": {
                "children": {
                    "description": "子节点，省包含市，市包含区",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.LocationTree"
                    }
                },
                "code": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "v1.LunarDay": {
            "type": "object",
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string",
                    "example": "2020-01-01"
                },
                "lunarDate": {
                    "description": "农历日期",
                    "type": "string",
                    "example": "正月初一"
                },
                "name": {
                    "description": "名称",
                    "type": "string",
                    "example": "初一"
                }
            }
        },
        "v1.PaipanRecordOwnRequest": {
            "type": "object",
            "required": [
                "ids"
            ],
            "properties": {
                "ids": {
                    "description": "ID",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "v1.PaipanRecordOwnResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.YunshiMinzhu": {
            "type": "object",
            "properties": {
                "bazi": {
                    "description": "八字",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "birthplace": {
                    "description": "出生地",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "birthtime": {
                    "description": "出生日期",
                    "type": "string"
                },
                "birthtimeLunar": {
                    "description": "农历生日",
                    "type": "string"
                },
                "gender": {
                    "description": "性别",
                    "type": "string"
                },
                "name": {
                    "description": "姓名",
                    "type": "string"
                },
                "wuxing": {
                    "description": "五行：用神,喜神,忌神,仇神,闲神",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zodiac": {
                    "description": "生肖",
                    "type": "string"
                }
            }
        },
        "v1.YunshiNianScore": {
            "type": "object",
            "properties": {
                "finalScore": {
                    "type": "number"
                },
                "ganzhi": {
                    "type": "string"
                },
                "keyword": {
                    "type": "string"
                }
            }
        },
        "v1.YunshiRequest": {
            "type": "object",
            "required": [
                "birthtime",
                "currentYear",
                "gender"
            ],
            "properties": {
                "birthplace": {
                    "description": "出生地",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "birthtime": {
                    "description": "生日",
                    "type": "string",
                    "example": "2006-01-02 15:04:05"
                },
                "currentYear": {
                    "description": "当前年份",
                    "type": "integer",
                    "example": 2021
                },
                "gender": {
                    "type": "string",
                    "enum": [
                        "男",
                        "女"
                    ],
                    "example": "男"
                },
                "name": {
                    "description": "姓名",
                    "type": "string",
                    "example": "张三"
                }
            }
        },
        "v1.YunshiResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/v1.YunshiResponseData"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.YunshiResponseData": {
            "type": "object",
            "properties": {
                "guaxiang": {
                    "description": "卦象",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.Guaxiang"
                        }
                    ]
                },
                "id": {
                    "type": "integer"
                },
                "mingzhu": {
                    "description": "命主信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.YunshiMinzhu"
                        }
                    ]
                },
                "score": {
                    "description": "评分",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.YunshiScore"
                        }
                    ]
                },
                "suggestions": {
                    "description": "建议",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.YunshiScore": {
            "type": "object",
            "properties": {
                "dayun": {
                    "description": "大运",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.YunshiScoreDayun"
                        }
                    ]
                },
                "liunian": {
                    "description": "流年",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.YunshiNianScore"
                        }
                    ]
                },
                "liuyue": {
                    "description": "流月（十三个月，一月到下一年一月）",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.YunshiYueScore"
                    }
                }
            }
        },
        "v1.YunshiScoreDayun": {
            "type": "object",
            "properties": {
                "ganzhi": {
                    "type": "string"
                },
                "score": {
                    "type": "number"
                }
            }
        },
        "v1.YunshiYueScore": {
            "type": "object",
            "properties": {
                "finalScore": {
                    "type": "number"
                },
                "ganzhi": {
                    "type": "string"
                },
                "jieqi": {
                    "type": "string"
                },
                "jieqiTime": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfofortune holds exported Swagger Info so clients can modify it
var SwaggerInfofortune = &swag.Spec{
	Version:          "1.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "2025运势",
	Description:      "This is a http server template.",
	InfoInstanceName: "fortune",
	SwaggerTemplate:  docTemplatefortune,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfofortune.InstanceName(), SwaggerInfofortune)
}
