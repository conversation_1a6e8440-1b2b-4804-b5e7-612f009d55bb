{"swagger": "2.0", "info": {"description": "This is a http server template.", "title": "在线投放", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "paths": {"/app/version/newest": {"post": {"description": "最新版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "最新版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CheckAppUpdateRequest"}}], "responses": {"200": {"description": "最新版本", "schema": {"$ref": "#/definitions/v1.CheckAppUpdateResponse"}}}}}, "/date/day": {"post": {"description": "获取本日日历", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日历"], "summary": "获取本日日历", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CalendarDayRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CalendarDayResponse"}}}}}, "/date/month": {"post": {"description": "获取本月日历", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["日历"], "summary": "获取本月日历", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CalendarMonthRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CalendarMonthResponse"}}}}}, "/datetime/bazi": {"post": {"description": "八字", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["时间"], "summary": "八字", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetBaziRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetBaziResponse"}}}}}, "/datetime/fromSizhu": {"post": {"description": "从四柱获取时间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["时间"], "summary": "从四柱获取时间", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetDatetimeBySizhuRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetDatetimeBySizhuResponse"}}}}}, "/enums/dizhi": {"post": {"description": "获取地支", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取地支", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsDizhiResponse"}}}}}, "/enums/location": {"post": {"description": "获取地区列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取地区列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsLocationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsLocationResponse"}}}}}, "/enums/lunar": {"post": {"description": "获取农历列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取农历列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsLunarRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsLunarResponse"}}}}}, "/enums/shishen/property": {"post": {"description": "获取十神特征", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取十神特征", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsShishenPropertyResponse"}}}}}, "/enums/tiangan": {"post": {"description": "获取天干", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取天干", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsTianganResponse"}}}}}, "/enums/wuxing": {"post": {"description": "获取五行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取五行", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsWuxingResponse"}}}}}, "/location": {"post": {"description": "获取地区树", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["地区"], "summary": "获取地区树", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.LocationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.LocationResponse"}}}}}, "/minglibazi/": {"post": {"description": "命理八字", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MingliBaziRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MingliBaziResponse"}}}}}, "/minglibazi/click": {"post": {"description": "命理八字点击", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字点击", "responses": {}}}, "/minglibazi/qw": {"post": {"description": "命理八字测算企微联系我", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字测算企微联系我", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MingliBaziQwLinkRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MingliBaziQwLinkResponse"}}}}}, "/minglibazi/replay": {"post": {"description": "命理八字重播", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字重播", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MingliBaziReplayRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MingliBaziReplayResponse"}}}}}, "/minglibazi/sms": {"post": {"description": "命理八字短信", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字短信", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MingliBaziSMSRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MingliBaziSMSResponse"}}}}}, "/minglibazi/year": {"post": {"description": "命理八字年运", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理八字"], "summary": "命理八字年运", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MingliBaziYearRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MingliBaziYearResponse"}}}}}, "/paipanRecord/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除用户排盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户排盘记录"], "summary": "删除用户排盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeletePaipanRecordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DeletePaipanRecordResponse"}}}}}, "/paipanRecord/own": {"post": {"security": [{"BearerAuth": []}], "description": "占有排盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["排盘"], "summary": "占有排盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PaipanRecordOwnRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PaipanRecordOwnResponse"}}}}}, "/paipanRecord/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询用户排盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户排盘记录"], "summary": "分页查询用户排盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListPaipanRecordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListPaipanRecordResponse"}}}}}}, "definitions": {"corona.PaipanBaziLiutongGraph": {"type": "object", "properties": {"edges": {"type": "array", "items": {"type": "object", "properties": {"from": {"type": "string"}, "isReverse": {"type": "boolean"}, "isSingle": {"type": "boolean"}, "relation": {"type": "string"}, "to": {"type": "string"}}}}, "nodes": {"type": "array", "items": {"type": "string"}}, "weight": {"type": "object", "additionalProperties": {}}}}, "corona.PaipanShishenPowerItem": {"type": "object", "properties": {"attr": {"description": "五行", "type": "string"}, "cangNum": {"description": "藏干数量", "type": "integer"}, "liliang": {"description": "力量", "type": "integer"}, "num": {"description": "五行数量", "type": "integer"}, "power": {"description": "能量占比", "type": "array", "items": {"type": "number"}}, "shiShen": {"description": "十神", "type": "array", "items": {"type": "string"}}, "shiShenName": {"description": "十神合称", "type": "string"}}}, "model.Guaxiang": {"type": "object", "properties": {"duanri": {"description": "断日", "type": "string"}, "gejue": {"description": "歌诀", "type": "string"}, "guaji": {"description": "卦吉", "type": "string"}, "id": {"description": "主键ID", "type": "integer"}, "idiom": {"description": "成语", "type": "string"}, "jieshi": {"description": "解释", "type": "string"}, "name": {"description": "卦名", "type": "string"}, "topdown": {"description": "上下卦", "type": "string"}, "value": {"description": "卦值", "type": "integer"}, "yao1": {"description": "爻1", "type": "integer"}, "yao2": {"description": "爻2", "type": "integer"}, "yao3": {"description": "爻3", "type": "integer"}, "yao4": {"description": "爻4", "type": "integer"}, "yao5": {"description": "爻5", "type": "integer"}, "yao6": {"description": "爻6", "type": "integer"}, "yaoValue": {"description": "爻值", "type": "integer"}}}, "v1.CalendarDayRequest": {"type": "object", "properties": {"appID": {"description": "应用：2-排盘、3-万年历、4-运势、5-论财", "type": "integer", "example": 3}, "date": {"description": "公历日期", "type": "string", "example": "2020-01-01"}}}, "v1.CalendarDayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CalendarDayResponseData"}, "message": {"type": "string"}}}, "v1.CalendarDayResponseData": {"type": "object", "properties": {"bazi1": {"description": "八字1（年份干支）", "type": "string", "example": "庚申"}, "bazi2": {"description": "八字2（月份干支）", "type": "string", "example": "壬午"}, "bazi2Next": {"description": "八字2（下个月份干支）", "type": "string", "example": "壬午"}, "bazi3": {"description": "八字3（日期干支）", "type": "string", "example": "辛巳"}, "caiLocation": {"description": "财位", "type": "string", "example": "东北"}, "constellation": {"description": "星座", "type": "string", "example": "双鱼座"}, "date": {"description": "公历日期", "type": "string", "example": "2099-03-12"}, "festival": {"description": "节日", "type": "array", "items": {"type": "string"}, "example": ["北方小年", "南方小年"]}, "fuLocation": {"description": "福位", "type": "string", "example": "西南"}, "heidao": {"description": "黑道", "type": "string", "example": "白虎"}, "hou": {"description": "七十二侯", "type": "string", "example": "半夏生"}, "huangdao": {"description": "黄道", "type": "string", "example": "青龙"}, "ji": {"description": "忌", "type": "array", "items": {"type": "string"}, "example": ["阴宅破土", "安葬", "启攒", "探亲访友"]}, "jieqi": {"description": "节气（今日或之前的最后一个节气）", "type": "string", "example": "大雪"}, "jieqiDate": {"description": "节气日期", "type": "string", "example": "2006-01-02"}, "jieqiTime": {"description": "节气时间", "type": "string", "example": "2006-01-02 15:00:59"}, "jishen": {"description": "吉神", "type": "array", "items": {"type": "string"}, "example": ["天德合", "月德合"]}, "luLocation": {"description": "禄位", "type": "string", "example": "东南"}, "lunarDate": {"description": "农历日期", "type": "string", "example": "二月廿一"}, "pengzubaiji": {"description": "彭祖百忌", "type": "array", "items": {"type": "string"}, "example": ["乙不栽植 千株不长", "未不服药 毒气入肠"]}, "pengzubaijiOverview": {"description": "彭祖百忌概述", "type": "string", "example": "猴日冲虎煞南"}, "shierjianri": {"description": "十二建日", "type": "string", "example": "定日"}, "taishen": {"description": "胎神", "type": "string", "example": "房床厕外"}, "taishenLocation": {"description": "胎神位置", "type": "string", "example": "西北"}, "times": {"description": "时辰（共13个，包含早子时与晚子时）", "type": "array", "items": {"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON>"}}, "weekday": {"description": "星期", "type": "string", "example": "星期四"}, "wuxing": {"description": "五行", "type": "string", "example": "山下火"}, "xiLocation": {"description": "喜位", "type": "string", "example": "西北"}, "xingxiu": {"description": "星宿", "type": "string", "example": "张月鹿"}, "xiongshen": {"description": "凶神", "type": "array", "items": {"type": "string"}, "example": ["月破", "大耗", "四击", "九空"]}, "yellowYears": {"description": "黄帝纪年：公元年+2697", "type": "integer", "example": 4721}, "yellowYearsZh": {"description": "黄帝纪年", "type": "string", "example": "四千七百二十一"}, "yi": {"description": "宜", "type": "array", "items": {"type": "string"}, "example": ["祭祀", "打扫", "破屋坏垣"]}, "zeri": {"description": "择日", "type": "string", "example": "大吉"}, "zodiac": {"description": "生肖", "type": "string", "example": "鼠"}}}, "v1.CalendarEachDayOfMonth": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "string", "example": "庚申"}, "currentMonth": {"description": "是否为当前月份", "type": "boolean", "example": true}, "date": {"description": "公历日期", "type": "string", "example": "2020-01-01"}, "holidayOff": {"description": "节假日调休：1休，2班", "type": "integer", "example": 1}, "ji": {"description": "忌", "type": "array", "items": {"type": "string"}, "example": ["阴宅破土", "安葬", "启攒", "探亲访友"]}, "jieqi": {"description": "节气", "type": "string", "example": "大雪"}, "jieqiTime": {"description": "节气时间", "type": "string"}, "jieri": {"description": "节日", "type": "array", "items": {"type": "string"}, "example": ["北方小年", "南方小年"]}, "liuriShensha": {"description": "流日神煞", "type": "array", "items": {"type": "string"}}, "lunarDate": {"description": "农历日期", "type": "string", "example": "二月廿一"}, "vipShishen": {"description": "VIP的干十神与支十神", "type": "array", "items": {"type": "string"}}, "weekday": {"description": "星期", "type": "string", "example": "星期四"}, "yi": {"description": "宜", "type": "array", "items": {"type": "string"}, "example": ["祭祀", "打扫", "破屋坏垣"]}}}, "v1.CalendarMonthRequest": {"type": "object", "properties": {"appID": {"description": "应用：2-排盘、3-万年历、4-运势、5-论财", "type": "integer"}, "month": {"description": "公历月份", "type": "string", "example": "2020-01"}}}, "v1.CalendarMonthResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/definitions/v1.CalendarEachDayOfMonth"}}}, "message": {"type": "string"}}}, "v1.CalendarShichen": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "string", "example": "丙子"}, "caiLocation": {"description": "财位", "type": "string", "example": "财神东北"}, "chong": {"description": "冲", "type": "string", "example": "虎"}, "fuLocation": {"description": "福位", "type": "string", "example": "福神西南"}, "ji": {"description": "忌", "type": "string", "example": "忌"}, "jixiong": {"description": "吉", "type": "string", "example": "吉"}, "luLocation": {"description": "禄位", "type": "string", "example": "阳贵东南"}, "sha": {"description": "煞", "type": "string", "example": "南"}, "time": {"description": "时辰", "type": "string", "example": "23:00-00:59"}, "xiLocation": {"description": "喜位", "type": "string", "example": "喜神西北"}, "yi": {"description": "宜", "type": "string", "example": "宜"}}}, "v1.CheckAppUpdateRequest": {"type": "object", "required": ["osType", "versionName"], "properties": {"osType": {"description": "1:android, 2:ios", "type": "integer"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.CheckAppUpdateResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CheckAppUpdateResponseData"}, "message": {"type": "string"}}}, "v1.CheckAppUpdateResponseData": {"type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "isForceUpdate": {"description": "是否强制更新", "type": "boolean"}, "isHotUpdate": {"description": "是否热更新", "type": "boolean"}, "updateNote": {"description": "更新说明", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}, "url": {"description": "下载地址", "type": "string"}, "versionCode": {"description": "版本号", "type": "integer"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.DeletePaipanRecordRequest": {"type": "object", "properties": {"ids": {"description": "ID", "type": "array", "items": {"type": "integer"}}}}, "v1.DeletePaipanRecordResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.EnumsDizhiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsDizhiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsDizhiResponseItem": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "string"}, "id": {"type": "integer", "example": 1}, "jieqi": {"description": "节气", "type": "string"}, "name": {"type": "string", "example": "子"}, "shichen": {"description": "时辰", "type": "string"}, "shuxiang": {"description": "属相", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}, "yuefen": {"description": "月份", "type": "string"}, "zhongqi": {"description": "中气", "type": "string"}}}, "v1.EnumsLocationRequest": {"type": "object", "properties": {"overseas": {"description": "是否海外", "type": "boolean", "example": false}}}, "v1.EnumsLocationResponse": {"type": "object", "properties": {"EnumsLocationResponseData": {"type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.EnumsLunarRequest": {"type": "object", "required": ["year"], "properties": {"year": {"description": "年份", "type": "string", "example": "2020"}}}, "v1.EnumsLunarResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsLunarResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsLunarResponseItem": {"type": "object", "properties": {"days": {"description": "日期", "type": "array", "items": {"$ref": "#/definitions/v1.LunarDay"}}, "month": {"description": "月份", "type": "string", "example": "正月"}}}, "v1.EnumsShishenPropertyItem": {"type": "object", "properties": {"ability": {"description": "能力", "type": "string"}, "bias": {"description": "偏向", "type": "string"}, "category": {"description": "十神类型", "type": "string"}, "insufficient": {"description": "不足", "type": "string"}, "overview": {"description": "概述", "type": "string"}, "preferredRegion": {"description": "适合地区", "type": "string"}, "profession": {"description": "职业", "type": "string"}, "superiority": {"description": "擅长", "type": "string"}, "type": {"description": "类型", "type": "string"}, "wealthBy": {"description": "财富来源", "type": "string"}}}, "v1.EnumsShishenPropertyResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsShishenPropertyItem"}}, "message": {"type": "string"}}}, "v1.EnumsTianganResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsTianganResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsTianganResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "甲"}, "tiangan": {"description": "天干", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}}}, "v1.EnumsWuxingItemShishen": {"type": "object", "properties": {"hecheng": {"description": "合称", "type": "string"}, "list": {"description": "十神", "type": "array", "items": {"type": "string"}}}}, "v1.EnumsWuxingResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsWuxingResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsWuxingResponseItem": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer", "example": 1}, "name": {"description": "五行名称", "type": "string", "example": "金"}, "position": {"description": "方位", "type": "string"}, "profession": {"description": "职业", "type": "string"}, "season": {"description": "季节", "type": "string"}, "shishen": {"$ref": "#/definitions/v1.EnumsWuxingItemShishen"}, "wuxing": {"description": "五行", "type": "string"}}}, "v1.GetBaziRequest": {"type": "object", "required": ["birthtime", "gender"], "properties": {"birthtime": {"description": "出生时间：2006-01-02 15:03:04", "type": "string", "example": "2006-01-02 15:03:04"}, "gender": {"description": "性别：1-男、2-女", "type": "integer", "example": 1}}}, "v1.GetBaziResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetBaziResponseData"}, "message": {"type": "string"}}}, "v1.GetBaziResponseData": {"type": "object", "properties": {"bazi": {"type": "array", "items": {"type": "string"}}}}, "v1.GetDatetimeBySizhuRequest": {"type": "object", "properties": {"endYear": {"description": "默认2099", "type": "integer", "example": 2099}, "sizhu": {"description": "四柱", "type": "array", "items": {"type": "string"}, "example": ["癸亥", "戊午", "壬寅", "己酉"]}, "startYear": {"description": "默认1801", "type": "integer", "example": 1801}}}, "v1.GetDatetimeBySizhuResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}}}, "v1.LocationRequest": {"type": "object", "properties": {"overseas": {"description": "是否海外", "type": "boolean", "example": false}}}, "v1.LocationResponse": {"type": "object", "properties": {"EnumsLocationResponseData": {"type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.LocationTree": {"type": "object", "properties": {"children": {"description": "子节点，省包含市，市包含区", "type": "array", "items": {"$ref": "#/definitions/v1.LocationTree"}}, "code": {"type": "integer"}, "name": {"type": "string"}}}, "v1.LunarDay": {"type": "object", "properties": {"date": {"description": "日期", "type": "string", "example": "2020-01-01"}, "lunarDate": {"description": "农历日期", "type": "string", "example": "正月初一"}, "name": {"description": "名称", "type": "string", "example": "初一"}}}, "v1.LuncaiBelong": {"type": "object", "properties": {"detail": {"description": "详情", "allOf": [{"$ref": "#/definitions/v1.LuncaiBelongDetail"}]}, "table": {"description": "表", "allOf": [{"$ref": "#/definitions/v1.LuncaiBelongTable"}]}}}, "v1.LuncaiBelongDetail": {"type": "object", "properties": {"caixingXiji": {"description": "财星喜忌", "type": "string"}, "cangcai": {"description": "命局藏财情况", "allOf": [{"$ref": "#/definitions/v1.LuncaiBelongDetailCangcai"}]}, "opportunity": {"description": "位置与时机", "type": "string"}}}, "v1.LuncaiBelongDetailCangcai": {"type": "object", "properties": {"ancangRuku": {"description": "是否暗藏入库", "type": "boolean"}, "deling": {"description": "得令", "type": "boolean"}, "dizhiCaiNum": {"description": "地支财位个数", "type": "integer"}, "shiling": {"description": "失令", "type": "boolean"}, "tianganCaiNum": {"description": "天干财位个数", "type": "integer"}}}, "v1.LuncaiBelongTable": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "array", "items": {"type": "string"}}, "dizhiCaiwei": {"description": "地支财位", "type": "array", "items": {"type": "string"}}, "gongwei": {"description": "宫位", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "lifeStage": {"description": "人生阶段", "type": "array", "items": {"type": "string"}}, "nianling": {"description": "年龄", "type": "array", "items": {"type": "string"}}, "sizhu": {"description": "四柱", "type": "array", "items": {"type": "string"}}, "tiangan": {"description": "天干", "type": "array", "items": {"type": "string"}}, "tianganCaiwei": {"description": "天干财位", "type": "array", "items": {"type": "string"}}}}, "v1.LuncaiCareerPalace": {"type": "object", "properties": {"down": {"description": "下（六合地支）", "type": "string"}, "left": {"description": "左（三合地支1）", "type": "string"}, "middle": {"description": "中（月支）", "type": "string"}, "right": {"description": "右（三合地支2）", "type": "string"}, "shishen": {"description": "月支十神", "type": "string"}, "yueling": {"description": "月令", "allOf": [{"$ref": "#/definitions/v1.LuncaiCareerPalaceYueling"}]}}}, "v1.LuncaiCareerPalaceYueling": {"type": "object", "properties": {"dizhi": {"description": "地支（三合或六合地支）", "type": "array", "items": {"type": "string"}}, "xiyong": {"description": "喜用（地支与地支五行）", "type": "string", "example": "卯木"}}}, "v1.LuncaiDancai": {"type": "object", "properties": {"ability": {"type": "string"}, "dangyi": {"type": "array", "items": {"type": "integer"}}, "dangyiPercent": {"type": "array", "items": {"type": "number"}}, "gejuCankao": {"type": "string"}, "recommendation": {"type": "string"}, "wangshuai": {"type": "string"}}}, "v1.LuncaiDayunliunian": {"type": "object", "properties": {"dayunList": {"description": "大运列表", "type": "array", "items": {"type": "string"}}, "endYear": {"description": "结束年份", "type": "integer"}, "ganzhiList": {"description": "干支列表", "type": "array", "items": {"type": "string"}}, "scoreList": {"description": "评分列表", "type": "array", "items": {"type": "integer"}}, "startYear": {"description": "开始年份", "type": "integer"}, "yearList": {"description": "年份列表", "type": "array", "items": {"type": "integer"}}}}, "v1.LuncaiMingli": {"type": "object", "properties": {"gejucankao": {"description": "格局参考", "type": "string"}, "graph": {"description": "流通图", "allOf": [{"$ref": "#/definitions/corona.PaipanBaziLiutongGraph"}]}, "liuTongNum": {"description": "流通个数", "type": "integer"}, "pingfen": {"description": "评分", "type": "string"}, "shishen": {"description": "四柱十神", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "tiaohou": {"description": "调候", "type": "string"}, "tongYi": {"description": "同异", "type": "array", "items": {"type": "integer"}}, "tongYiPercent": {"description": "同异百分比", "type": "array", "items": {"type": "number"}}, "wangshuai": {"description": "旺衰", "type": "string"}, "wuxing": {"description": "五行：用神,喜神,忌神,仇神,闲神", "type": "array", "items": {"type": "string"}}, "yingYang": {"description": "阴阳数量", "type": "array", "items": {"type": "integer"}}, "yingYangPercent": {"description": "阴阳百分比", "type": "array", "items": {"type": "number"}}, "zuaiNum": {"description": "阻碍个数", "type": "integer"}}}, "v1.LuncaiMinzhu": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "array", "items": {"type": "string"}}, "benqi": {"description": "本气", "type": "array", "items": {"type": "string"}}, "benqiShishen": {"description": "本气十神", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生日期", "type": "string"}, "birthtimeLunar": {"description": "农历生日", "type": "string"}, "dizhi": {"description": "地支", "type": "array", "items": {"type": "string"}}, "dizhiShishen": {"description": "地支十神", "type": "array", "items": {"type": "string"}}, "dizhiWuxing": {"description": "地支五行", "type": "array", "items": {"type": "string"}}, "gender": {"description": "性别", "type": "string"}, "jiaoyun": {"description": "交运", "type": "string"}, "name": {"description": "姓名", "type": "string"}, "nayin": {"description": "纳音", "type": "array", "items": {"type": "string"}}, "qiyun": {"description": "命主起运", "type": "string"}, "riyuan": {"description": "日元", "type": "string"}, "shenshaJishen": {"description": "神煞吉神", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "tiangan": {"description": "天干，索引：0-年柱、1-月柱、2-日柱、3-时柱、4-大运、5-流年、6-流月、7-流日、8-流时", "type": "array", "items": {"type": "string"}}, "tianganWuxing": {"description": "天干五行", "type": "array", "items": {"type": "string"}}, "wuxing": {"description": "五行：用神,喜神,忌神,仇神,闲神", "type": "array", "items": {"type": "string"}}, "wuxingNeed": {"description": "五行缺", "type": "string"}, "wuxingWxxqs": {"description": "五行旺相休囚死", "type": "object", "additionalProperties": {"type": "string"}}, "yuqi": {"description": "余气", "type": "array", "items": {"type": "string"}}, "yuqiShishen": {"description": "余气十神", "type": "array", "items": {"type": "string"}}, "zhongqi": {"description": "中气", "type": "array", "items": {"type": "string"}}, "zhongqiShishen": {"description": "中气十神", "type": "array", "items": {"type": "string"}}, "zhuxing": {"description": "主星", "type": "array", "items": {"type": "string"}}, "zodiac": {"description": "生肖", "type": "string"}}}, "v1.LuncaiRisk": {"type": "object", "properties": {"detail": {"description": "风险详情", "type": "array", "items": {"type": "string"}}, "shishen": {"description": "四柱十神", "type": "array", "items": {"type": "array", "items": {"type": "string"}}}}}, "v1.MingliBaziCurrentDayun": {"type": "object", "properties": {"bestYear": {"description": "最佳年份", "type": "array", "items": {"type": "array", "items": {}}}, "dayun": {"description": "当前大运", "type": "string"}, "inDayun": {"description": "是否在大运中", "type": "boolean"}, "jiaoyun": {"description": "交运", "type": "string"}, "wangshuai": {"description": "旺衰", "allOf": [{"$ref": "#/definitions/v1.MingliBaziCurrentDayunWangshuai"}]}, "worstYear": {"description": "最差年份", "type": "array", "items": {"type": "array", "items": {}}}}}, "v1.MingliBaziCurrentDayunWangshuai": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "string"}, "tiangan": {"description": "天干", "type": "string"}}}, "v1.MingliBaziQwLinkRequest": {"type": "object", "required": ["btn", "id", "type"], "properties": {"btn": {"description": "按钮ID", "type": "string", "example": "00"}, "id": {"description": "记录ID", "type": "integer", "example": 1}, "phone": {"description": "手机号", "type": "string"}, "type": {"description": "类型：1-联系我、2-获客链接", "type": "integer", "example": 1}}}, "v1.MingliBaziQwLinkResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "string"}, "message": {"type": "string"}}}, "v1.MingliBaziReplayRequest": {"type": "object", "required": ["currentTime", "id"], "properties": {"currentTime": {"description": "当前时间", "type": "string", "example": "2006-01-02 15:04:05"}, "id": {"description": "记录ID", "type": "integer", "example": 1}}}, "v1.MingliBaziReplayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MingliBaziReplayResponseData"}, "message": {"type": "string"}}}, "v1.MingliBaziReplayResponseData": {"type": "object", "properties": {"advice": {"description": "建议", "type": "array", "items": {"type": "string"}}, "belong": {"description": "财富所属", "allOf": [{"$ref": "#/definitions/v1.LuncaiBelong"}]}, "caiyuan": {"description": "财源与求财意向", "type": "array", "items": {"type": "string"}}, "careerPalace": {"description": "事业宫看财富贵人", "allOf": [{"$ref": "#/definitions/v1.LuncaiCareerPalace"}]}, "careerSuggestion": {"description": "职业建议", "type": "array", "items": {"type": "string"}}, "currentDayun": {"description": "当前大运", "allOf": [{"$ref": "#/definitions/v1.MingliBaziCurrentDayun"}]}, "dancai": {"description": "担财能力", "allOf": [{"$ref": "#/definitions/v1.LuncaiDancai"}]}, "dayunliunian": {"description": "大运流年", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "id": {"description": "返回记录ID（用于用户付费）", "type": "integer"}, "isShow": {"description": "是否显示：true-显示，false-不显示", "type": "boolean"}, "liunian": {"description": "流年", "type": "string"}, "mingli": {"description": "命理", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON>"}]}, "mingzhu": {"description": "命主信息", "allOf": [{"$ref": "#/definitions/v1.LuncaiMinzhu"}]}, "nengliang": {"description": "五行能量", "type": "array", "items": {"$ref": "#/definitions/corona.PaipanShishenPowerItem"}}, "risk": {"description": "风险与偏好", "allOf": [{"$ref": "#/definitions/v1.LuncaiRisk"}]}, "shensha": {"description": "神煞看财运", "type": "array", "items": {"type": "string"}}, "wuxingTiaohe": {"description": "五行调和", "allOf": [{"$ref": "#/definitions/v1.MingliBaziWuxingTiaohe"}]}, "xingge": {"description": "性格", "type": "string"}}}, "v1.MingliBaziRequest": {"type": "object", "required": ["birthtime", "currentTime", "gender"], "properties": {"birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "生日", "type": "string", "example": "2006-01-02 15:04:05"}, "currentTime": {"description": "当前时间", "type": "string", "example": "2006-01-02 15:04:05"}, "gender": {"type": "string", "enum": ["男", "女"], "example": "男"}, "name": {"description": "姓名", "type": "string", "example": "张三"}, "scene": {"description": "场景", "type": "string", "example": "1"}}}, "v1.MingliBaziResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MingliBaziResponseData"}, "message": {"type": "string"}}}, "v1.MingliBaziResponseData": {"type": "object", "properties": {"advice": {"description": "建议", "type": "array", "items": {"type": "string"}}, "belong": {"description": "财富所属", "allOf": [{"$ref": "#/definitions/v1.LuncaiBelong"}]}, "caiyuan": {"description": "财源与求财意向", "type": "array", "items": {"type": "string"}}, "careerPalace": {"description": "事业宫看财富贵人", "allOf": [{"$ref": "#/definitions/v1.LuncaiCareerPalace"}]}, "careerSuggestion": {"description": "职业建议", "type": "array", "items": {"type": "string"}}, "currentDayun": {"description": "当前大运", "allOf": [{"$ref": "#/definitions/v1.MingliBaziCurrentDayun"}]}, "dancai": {"description": "担财能力", "allOf": [{"$ref": "#/definitions/v1.LuncaiDancai"}]}, "dayunliunian": {"description": "大运流年", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "id": {"description": "返回记录ID（用于用户付费）", "type": "integer"}, "isShow": {"description": "是否显示：true-显示，false-不显示", "type": "boolean"}, "liunian": {"description": "流年", "type": "string"}, "mingli": {"description": "命理", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON><PERSON>"}]}, "mingzhu": {"description": "命主信息", "allOf": [{"$ref": "#/definitions/v1.LuncaiMinzhu"}]}, "nengliang": {"description": "五行能量", "type": "array", "items": {"$ref": "#/definitions/corona.PaipanShishenPowerItem"}}, "risk": {"description": "风险与偏好", "allOf": [{"$ref": "#/definitions/v1.LuncaiRisk"}]}, "shensha": {"description": "神煞看财运", "type": "array", "items": {"type": "string"}}, "wuxingTiaohe": {"description": "五行调和", "allOf": [{"$ref": "#/definitions/v1.MingliBaziWuxingTiaohe"}]}, "xingge": {"description": "性格", "type": "string"}}}, "v1.MingliBaziSMSRequest": {"type": "object", "required": ["id", "phone"], "properties": {"id": {"description": "论财ID（排盘ID）", "type": "integer", "example": 1}, "phone": {"description": "手机号", "type": "string", "example": "13800138000"}}}, "v1.MingliBaziSMSResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.MingliBaziWuxingTiaohe": {"type": "object", "properties": {"pingheng": {"description": "平衡", "type": "array", "items": {"type": "string"}}, "wangshuai": {"description": "旺衰", "type": "array", "items": {"type": "string"}}}}, "v1.MingliBaziYearRequest": {"type": "object", "required": ["currentYear", "id"], "properties": {"currentYear": {"description": "当前年份", "type": "integer", "example": 2021}, "id": {"description": "记录ID", "type": "integer", "example": 1}}}, "v1.MingliBaziYearResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.YunshiResponseData"}, "message": {"type": "string"}}}, "v1.PageListPaipanRecordRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListPaipanRecordRequestParam"}}}, "v1.PageListPaipanRecordRequestParam": {"type": "object", "properties": {"appIDs": {"description": "应用ID：2-排盘、3-万年历、4-运势、5-论财", "type": "array", "items": {"type": "integer"}}, "application": {"description": "应用标识", "type": "string"}, "bazi": {"description": "八字", "type": "string"}, "birthTimeEnd": {"description": "出生日期结束", "type": "string"}, "birthTimeStart": {"description": "出生日期开始", "type": "string"}, "birthTimeSunEnd": {"description": "真太阳时结束", "type": "string"}, "birthTimeSunStart": {"description": "真太阳时开始", "type": "string"}, "birthplace": {"description": "出生地", "type": "string"}, "gender": {"description": "性别：1-男、2-女", "type": "array", "items": {"type": "integer"}}, "name": {"description": "命例名称", "type": "string"}}}, "v1.PageListPaipanRecordResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListPaipanRecordResponseData"}, "message": {"type": "string"}}}, "v1.PageListPaipanRecordResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListPaipanRecordResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListPaipanRecordResponseDataItem": {"type": "object", "properties": {"appID": {"description": "应用ID", "type": "integer", "example": 1}, "appName": {"description": "应用名称", "type": "string"}, "appPlatformID": {"description": "应用平台ID", "type": "integer", "example": 1}, "appPlatformName": {"description": "应用平台名称", "type": "string"}, "bazi": {"description": "八字", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生日期", "type": "string", "example": "2021-01-01 00:00:00"}, "birthtimeLunar": {"description": "农历出生日期", "type": "string"}, "birthtimeSun": {"description": "真太阳时", "type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "createdTime": {"description": "创建时间", "type": "string"}, "gender": {"description": "性别：1-男, 2-女", "type": "string", "example": "1"}, "id": {"type": "integer"}, "name": {"description": "命例名称", "type": "string"}, "saveTime": {"description": "保存时间", "type": "string"}, "type": {"description": "类型", "type": "string"}, "userAgent": {"description": "用户代理", "type": "string"}, "userID": {"description": "用户ID", "type": "string"}}}, "v1.PaipanRecordOwnRequest": {"type": "object", "required": ["ids"], "properties": {"ids": {"description": "ID", "type": "array", "items": {"type": "integer"}}}}, "v1.PaipanRecordOwnResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.YunshiMinzhu": {"type": "object", "properties": {"bazi": {"description": "八字", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生日期", "type": "string"}, "birthtimeLunar": {"description": "农历生日", "type": "string"}, "gender": {"description": "性别", "type": "string"}, "name": {"description": "姓名", "type": "string"}, "wuxing": {"description": "五行：用神,喜神,忌神,仇神,闲神", "type": "array", "items": {"type": "string"}}, "zodiac": {"description": "生肖", "type": "string"}}}, "v1.YunshiNianScore": {"type": "object", "properties": {"finalScore": {"type": "number"}, "ganzhi": {"type": "string"}, "keyword": {"type": "string"}}}, "v1.YunshiResponseData": {"type": "object", "properties": {"guaxiang": {"description": "卦象", "allOf": [{"$ref": "#/definitions/model.Guaxiang"}]}, "id": {"type": "integer"}, "mingzhu": {"description": "命主信息", "allOf": [{"$ref": "#/definitions/v1.<PERSON><PERSON>"}]}, "score": {"description": "评分", "allOf": [{"$ref": "#/definitions/v1.YunshiScore"}]}, "suggestions": {"description": "建议", "type": "array", "items": {"type": "string"}}}}, "v1.YunshiScore": {"type": "object", "properties": {"dayun": {"description": "大运", "allOf": [{"$ref": "#/definitions/v1.YunshiScoreDayun"}]}, "liunian": {"description": "流年", "allOf": [{"$ref": "#/definitions/v1.YunshiNianScore"}]}, "liuyue": {"description": "流月（十三个月，一月到下一年一月）", "type": "array", "items": {"$ref": "#/definitions/v1.YunshiYueScore"}}}}, "v1.YunshiScoreDayun": {"type": "object", "properties": {"ganzhi": {"type": "string"}, "score": {"type": "number"}}}, "v1.YunshiYueScore": {"type": "object", "properties": {"finalScore": {"type": "number"}, "ganzhi": {"type": "string"}, "jieqi": {"type": "string"}, "jieqiTime": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}