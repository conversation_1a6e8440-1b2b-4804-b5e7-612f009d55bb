package sid

import (
	"github.com/sony/sonyflake"
)

type Sid struct {
	sf *sonyflake.Sonyflake
}

func NewSid() *Sid {
	sf := sonyflake.NewSonyflake(sonyflake.Settings{})
	if sf == nil {
		panic("sonyflake not created")
	}
	return &Sid{sf}
}
func (slf *Sid) String() string {
	id, _ := slf.sf.NextID()
	return IntToBase62(int(id))
}
func (slf *Sid) Uint64() uint64 {
	id, _ := slf.sf.NextID()
	return id
}

func (slf *Sid) Int64() int64 {
	id, _ := slf.sf.NextID()
	return int64(id)
}
