package log

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
	"zodiacus/config"
	aliyun_sls "zodiacus/third_party/aliyun/sls"
)

const ctxLoggerKey = "zapLogger"

type Logger struct {
	*zap.Logger
}

func NewLogger(conf *viper.Viper) *Logger {
	fileName := conf.GetString("log.log_file_name")
	lvl := conf.GetString("log.log_level")
	var level zapcore.Level
	switch lvl {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "warn":
		level = zap.WarnLevel
	case "error":
		level = zap.ErrorLevel
	default:
		level = zap.InfoLevel
	}

	var encoder zapcore.Encoder
	if conf.GetString("log.encoding") == "console" {
		encoder = zapcore.NewConsoleEncoder(zapcore.EncoderConfig{
			TimeKey:        "ts",
			LevelKey:       "level",
			NameKey:        "ZapLogger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseColorLevelEncoder,
			EncodeTime:     timeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.FullCallerEncoder,
		})
	} else {
		encoder = zapcore.NewJSONEncoder(zapcore.EncoderConfig{
			TimeKey:        "ts",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			FunctionKey:    zapcore.OmitKey,
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     timeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		})
	}

	var syncers []zapcore.WriteSyncer
	if fileName != "" {
		hook := lumberjack.Logger{
			Filename:   fileName,
			MaxSize:    conf.GetInt("log.max_size"),
			MaxBackups: conf.GetInt("log.max_backups"),
			MaxAge:     conf.GetInt("log.max_age"),
			Compress:   conf.GetBool("log.compress"),
		}
		syncers = []zapcore.WriteSyncer{
			zapcore.AddSync(os.Stdout),
			zapcore.AddSync(&hook),
		}
	} else {
		syncers = []zapcore.WriteSyncer{
			zapcore.AddSync(os.Stdout),
		}
	}
	if conf.GetBool("sls.enabled") {
		// 添加sls作为日志输出器
		slsWriter := aliyun_sls.NewLogger(aliyun_sls.Config{
			ProjectName:     conf.GetString("sls.project_name"),
			LogStoreName:    config.AppName,
			LogTopic:        config.AppName,
			LogSource:       config.AppName,
			LogContentKey:   conf.GetString("sls.log_content_key"),
			Endpoint:        conf.GetString("sls.endpoint"),
			AccessKeyId:     conf.GetString("sls.access_key_id"),
			AccessKeySecret: conf.GetString("sls.access_key_secret"),
			SecurityToken:   conf.GetString("sls.security_token"),
		})
		syncers = append(syncers, zapcore.AddSync(slsWriter))
	}
	var syncer zapcore.WriteSyncer
	if len(syncers) == 1 {
		syncer = syncers[0]
	} else {
		syncer = zapcore.NewMultiWriteSyncer(syncers...)
	}
	core := zapcore.NewCore(
		encoder,
		syncer,
		level,
	)
	if conf.GetString("env") != "prod" {
		return &Logger{zap.New(core, zap.Development(), zap.AddCaller(), zap.AddStacktrace(zap.ErrorLevel))}
	}
	return &Logger{zap.New(core, zap.AddCaller(), zap.AddStacktrace(zap.ErrorLevel))}
}

func timeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

func (slf *Logger) WithValue(ctx context.Context, fields ...zapcore.Field) context.Context {
	if c, ok := ctx.(*gin.Context); ok {
		ctx = c.Request.Context()
		c.Request = c.Request.WithContext(context.WithValue(ctx, ctxLoggerKey, slf.WithContext(ctx).With(fields...)))
		return c
	}
	return context.WithValue(ctx, ctxLoggerKey, slf.WithContext(ctx).With(fields...))
}

func (slf *Logger) WithContext(ctx context.Context) *Logger {
	if c, ok := ctx.(*gin.Context); ok {
		ctx = c.Request.Context()
	}
	zl := ctx.Value(ctxLoggerKey)
	ctxLogger, ok := zl.(*zap.Logger)
	if ok {
		return &Logger{ctxLogger}
	}
	return slf
}
