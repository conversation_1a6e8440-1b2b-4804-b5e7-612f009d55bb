package aliyun_oss

import (
	"io"
	"mime"
	"net/url"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

type Client struct {
	baseUrl      string
	bucketName   string
	aliyunClient *oss.Client
	aliyunBucket *oss.Bucket
}

func NewClient(conf *viper.Viper) *Client {
	client, err := oss.New(conf.GetString("oss.endpoint"), conf.GetString("oss.access_key_id"), conf.GetString("oss.access_key_secret"))
	if err != nil {
		panic(err)
	}
	bucket, err := client.Bucket(conf.GetString("oss.bucket"))
	if err != nil {
		panic(err)
	}
	return &Client{
		bucketName:   conf.GetString("oss.bucket"),
		baseUrl:      conf.GetString("oss.base_url"),
		aliyunClient: client,
		aliyunBucket: bucket,
	}
}

// GenerateUploadKey typ-文件类别（images/videos/xlsx），ext 文件后缀，category 文件分类
func (slf *Client) GenerateUploadKey(typ int, ext string, category ...string) (key string, contentType string, err error) {
	if ext == "" || strings.Contains(ext, ".") {
		return "", "", errors.New("invalid file format: " + ext)
	}
	contentType = mime.TypeByExtension("." + ext)
	if ext == "glb" {
		contentType = "glb"
	}
	if strings.ToLower(ext) == "jfif" {
		contentType = "image/jpeg"
	}
	if strings.ToLower(ext) == "mp4" {
		contentType = "video/mp4"
	}
	if contentType == "" {
		return "", "", errors.New("unsupported file type: " + ext)
	}
	var categoryPath string
	if len(category) > 0 {
		categoryPath = category[0]
	} else {
		categoryPath = "default"
	}
	var typPath string
	switch typ {
	case 1:
		typPath = "images"
	case 2:
		typPath = "videos"
	case 3:
		typPath = "xlsx"
	default:
		return "", "", errors.Errorf("unsupported upload type: %v", typ)
	}
	filePath := uuid.New().String() + "." + ext
	return categoryPath + "/" + typPath + "/" + filePath, contentType, nil
}

func (slf *Client) GenerateUploadUrl(key, contentTyp string) (string, error) {
	signUrl, err := slf.aliyunBucket.SignURL(key, oss.HTTPPut, 1800, oss.ContentType(contentTyp), oss.ResponseContentType(contentTyp))
	if err != nil {
		return "", errors.Wrap(err, "sign url error")
	}
	return slf.replaceWithBaseUrl(signUrl)
}

func (slf *Client) GetObjectReadCloser(key string) (io.ReadCloser, error) {
	if key == "" {
		return nil, nil
	}
	object, err := slf.aliyunBucket.GetObject(key)
	if err != nil {
		return nil, err
	}
	return object, nil
}

func (slf *Client) GetObjectSignUrl(key string) (string, error) {
	if key == "" {
		return "", nil
	}
	expire := slf.getExpireForNextMonth()
	signUrl, err := slf.aliyunBucket.SignURL(key, oss.HTTPGet, expire)
	if err != nil {
		return "", err
	}
	return slf.replaceWithBaseUrl(signUrl)
}

// GetObjectSignUrls 获取多个对象的签名url
func (slf *Client) GetObjectSignUrls(keys []string) ([]string, error) {
	urls := make([]string, 0, len(keys))
	for _, key := range keys {
		url, err := slf.GetObjectSignUrl(key)
		if err != nil {
			return nil, err
		}
		urls = append(urls, url)
	}
	return urls, nil
}
func (slf *Client) UploadByReader(typ int, ext string, reader io.Reader, category ...string) (string, error) {
	key, _, err := slf.GenerateUploadKey(typ, ext, category...)
	if err != nil {
		return "", err
	}
	if err = slf.aliyunBucket.PutObject(key, reader); err != nil {
		return "", err
	}
	return key, nil
}

func (slf *Client) replaceWithBaseUrl(srcUrl string) (string, error) {
	base, err := url.Parse(slf.baseUrl)
	if err != nil {
		return "", errors.Wrap(err, "parse base url error")
	}
	res, err := url.Parse(srcUrl)
	if err != nil {
		return "", errors.Wrap(err, "parse sign url error")
	}
	res.Scheme = base.Scheme
	res.Host = base.Host
	return res.String(), nil
}

func (slf *Client) getExpireForNextMonth() int64 {
	now := time.Now()
	nextMonth := time.Date(now.Year(), now.Month()+1, 28, 0, 0, 0, 0, time.UTC)
	return nextMonth.Unix() - now.Unix()
}
