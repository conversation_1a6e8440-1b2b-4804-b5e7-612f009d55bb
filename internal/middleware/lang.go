package middleware

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"github.com/liuzl/gocc"
	"io"
	"net/http"
	"strings"
)

type bodyLangWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyLangWriter) Write(b []byte) (int, error) {
	return w.body.Write(b) // 只写入缓冲区，不写入响应
}

func LanguageConvertMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		writer := &bodyLangWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer
		c.Next()
		if c.Writer.Status() != http.StatusOK ||
			c.Writer.Header().Get("Content-Type") != "application/json; charset=utf-8" {
			return
		}
		lang := c.GetHeader("Accept-Language")
		var converter *gocc.OpenCC
		var err error
		if !strings.Contains(lang, "zh-TW") && !strings.Contains(lang, "zh-HK") {
			c.Writer.Write(writer.body.Bytes())
			return
		}
		converter, err = gocc.New("s2t")
		if err != nil {
			c.Writer.Write(writer.body.Bytes())
			return
		}
		converted, err := converter.Convert(writer.body.String())
		if err != nil {
			return
		}
		c.Writer.Header().Set("Content-Length", "")
		c.Writer.WriteHeaderNow()
		io.WriteString(c.Writer, converted)
	}
}
