package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
	"zodiacus/third_party/identity"
)

// VIP 检查会员信息
func VIP(vipRepo repository.VIPRepository, logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		value, exists := ctx.Get("claims")
		if !exists {
			ctx.Next()
			return
		}
		claims, ok := value.(*identity.Claims)
		if !ok {
			ctx.Next()
			return
		}
		// 查询会员
		application := ctx.GetHeader("application")
		//application = "application_property"
		if application == "" {
			ctx.Next()
			return
		}
		//claims.User.Id = "00f75eb1-5438-49e2-9129-2064bd880674"
		duration, err := vipRepo.GetVIPMemberInfo(ctx, application, claims.User.Id)
		if err != nil {
			logger.Error("get vip info error", zap.Error(err))
			ctx.Next()
			return
		}
		if duration == nil {
			ctx.Next()
			return
		}
		ctx.Set("vip", &identity.VIP{
			Application: application,
			UserID:      claims.User.Id,
			ExpireTime:  duration.ExpireTime,
		})
		recoveryLoggerFunc(ctx, logger)
		ctx.Next()
	}
}
