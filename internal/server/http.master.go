package server

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"zodiacus/docs"
	"zodiacus/internal/handler/master"
	"zodiacus/internal/middleware"
	"zodiacus/pkg/log"
	"zodiacus/pkg/server/http"
	"zodiacus/third_party/identity"
)

func NewMasterHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	identity *identity.Client,
	datetimeHandler *master.DateTimeHandler,
	paipanHandler *master.MasterHandler,
	locationHandler *master.LocationHandler,
	enumsHandler *master.EnumsHandler,
	dateHandler *master.DateHandler,
	avHandler *master.AppVersionHandler,
	paipanRecordHandler *master.PaipanRecordHandler,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	host, port := conf.GetString("http.master.host"), conf.GetInt("http.master.port")
	srv := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(host),
		http.WithServerPort(port),
	)

	// swagger doc
	docs.SwaggerInfomaster.BasePath = "/v1"
	srv.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
		ginSwagger.InstanceName("master"),
	))

	srv.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.LanguageConvertMiddleware(),
		middleware.RequestLogMiddleware(logger),
		//middleware.SignMiddleware(log),
	)

	v1Group := srv.Group("/v1")
	{
		// 强制鉴权
		required := v1Group.Group("/")
		required.Use(middleware.StrictAuth(identity, logger))
		// 可选鉴权
		optional := v1Group.Group("/")
		optional.Use(middleware.NoStrictAuth(identity, logger))
		// 无需鉴权
		nameless := v1Group.Group("/")

		// 四柱反查时间
		datetimeHandler.Handle(required, optional, nameless)
		// 枚举
		enumsHandler.Handle(required, optional, nameless)
		// 论财
		paipanHandler.Handle(required, optional, nameless)
		// 地区
		locationHandler.Handle(required, optional, nameless)
		// 日期
		dateHandler.Handle(required, optional, nameless)
		// 应用版本
		avHandler.Handle(required, optional, nameless)
		// 排盘记录
		paipanRecordHandler.Handle(required, optional, nameless)
	}

	return srv
}
