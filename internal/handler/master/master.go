package master

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type MasterHandler struct {
	*handler.BaseHandler
	masterService service.MasterService
}

func NewMasterHandler(
	baseHandler *handler.BaseHandler,
	masterService service.MasterService,
) *MasterHandler {
	return &MasterHandler{
		BaseHandler:   baseHandler,
		masterService: masterService,
	}
}

func (slf *MasterHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	optional.POST("/master/", slf.Master)
	optional.POST("/geju/biange", slf.GeJuBianGe)
	optional.POST("/xiyongwangshaui", slf.XiyongWangshuai)
	optional.POST("/dayunliunian", slf.DayunLiunian)
	optional.POST("/dayun", slf.DayunAnalysis)
	optional.POST("/liunian", slf.LiunianAnalysis)
	nameless.GET("/ip", slf.GetIP)
}

// GetIP godoc
// @Summary 获取IP
// @Schemes
// @Description 获取IP
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Success 200 {object} map[string]string
// @Router /ip [post]
func (slf *MasterHandler) GetIP(ctx *gin.Context) {
	slf.Reply(ctx, map[string]string{
		"ip":        ctx.ClientIP(),
		"forwarded": ctx.GetHeader("X-Forwarded-For"),
		"real":      ctx.GetHeader("X-Real-IP"),
	})
}

// GeJuBianGe god
// @Summary 格局别格
// @Schemes
// @Description 格局别格
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.GeJuBianGeRequest true "params"
// @Success 200 {object} v1.GeJuBianGeResponse
// @Router /geju/biange [post]
func (slf *MasterHandler) GeJuBianGe(ctx *gin.Context) {
	var req v1.GeJuBianGeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("User-Agent")
	res, err := slf.masterService.GeJuBianGe(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// XiyongWangshuai godoc
// @Summary 喜用旺衰
// @Schemes
// @Description 喜用旺衰
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.MasterXiyongWangshuaiRequest true "params"
// @Success 200 {object} v1.MasterXiyongWangshuaiResponse
// @Router /xiyongwangshaui [post]
func (slf *MasterHandler) XiyongWangshuai(ctx *gin.Context) {
	var req v1.MasterXiyongWangshuaiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.XiyongWangshuai(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Master godoc
// @Summary 排盘专家版
// @Schemes
// @Description 排盘专家版
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.MasterRequest true "params"
// @Success 200 {object} v1.MasterResponse
// @Router /master/ [post]
func (slf *MasterHandler) Master(ctx *gin.Context) {
	var req v1.MasterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("User-Agent")
	req.IP = ctx.GetHeader("X-Forwarded-For")
	res, err := slf.masterService.Master(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DayunLiunian godoc
// @Summary 大运流年
// @Schemes
// @Description 大运流年
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.DayunliunianRequest true "params"
// @Success 200 {object} v1.DayunliunianResponse
// @Router /dayunliunian [post]
func (slf *MasterHandler) DayunLiunian(ctx *gin.Context) {
	var req v1.DayunliunianRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.DayunLiunian(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DayunAnalysis godoc
// @Summary 大运分析
// @Schemes
// @Description 大运分析
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.DayunAnalysisRequest true "params"
// @Success 200 {object} v1.DayunAnalysisResponse
// @Router /dayun [post]
func (slf *MasterHandler) DayunAnalysis(ctx *gin.Context) {
	var req v1.DayunAnalysisRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.DayunAnalysis(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// LiunianAnalysis godoc
// @Summary 流年分析
// @Schemes
// @Description 流年分析
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.LiunianAnalysisRequest true "params"
// @Success 200 {object} v1.LiunianAnalysisResponse
// @Router /liunian [post]
func (slf *MasterHandler) LiunianAnalysis(ctx *gin.Context) {
	var req v1.LiunianAnalysisRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.LiunianAnalysis(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
