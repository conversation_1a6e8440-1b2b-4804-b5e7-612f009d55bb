package fortune

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type EnumsHandler struct {
	*handler.BaseHandler
	enumsService service.EnumsService
}

func NewEnumsHandler(
	baseHandler *handler.BaseHandler,
	enumsService service.EnumsService,
) *EnumsHandler {
	return &EnumsHandler{
		BaseHandler:  baseHandler,
		enumsService: enumsService,
	}
}

func (slf *EnumsHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.
		POST("/enums/lunar", slf.Lunar).
		POST("/enums/location", slf.Location)
}

// Lunar godoc
// @Summary 获取农历列表
// @Schemes
// @Description 获取农历列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLunarRequest true "params"
// @Success 200 {object} v1.EnumsLunarResponse
// @Router /enums/lunar [post]
func (slf *EnumsHandler) Lunar(ctx *gin.Context) {
	var req v1.EnumsLunarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Lunar(ctx, req.Year)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Location godoc
// @Summary 获取地区列表
// @Schemes
// @Description 获取地区列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLocationRequest true "params"
// @Success 200 {object} v1.EnumsLocationResponse
// @Router /enums/location [post]
func (slf *EnumsHandler) Location(ctx *gin.Context) {
	var req v1.EnumsLocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Location(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
