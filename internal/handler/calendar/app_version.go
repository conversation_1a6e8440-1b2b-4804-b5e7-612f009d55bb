package calendar

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AppVersionHandler struct {
	*handler.BaseHandler
	avService service.AppVersionService
}

func NewAppVersionHandler(handler *handler.BaseHandler, avService service.AppVersionService) *AppVersionHandler {
	return &AppVersionHandler{
		BaseHandler: handler,
		avService:   avService,
	}
}

func (slf *AppVersionHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.
		POST("/app/version/newest", slf.NewestAppVersion)
}

// NewestAppVersion godoc
// @Summary 最新版本
// @Description 最新版本
// @Tags 应用版本
// @Accept json
// @Produce json
// @Param request body v1.CheckAppUpdateRequest true "params"
// @Success 200 {object} v1.CheckAppUpdateResponse "最新版本"
// @Router /app/version/newest [post]
func (slf *AppVersionHandler) NewestAppVersion(ctx *gin.Context) {
	var req v1.CheckAppUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Application = slf.Application(ctx)
	resp, err := slf.avService.CheckUpdate(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.NewestAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}
