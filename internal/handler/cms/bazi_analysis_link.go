package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type BaziAnalysisLinkHandler struct {
	*handler.BaseHandler
	balSvc service.BaziAnalysisLinkService
}

func NewBaziAnalysisLinkHandler(baseHandler *handler.BaseHandler, balSvc service.BaziAnalysisLinkService) *BaziAnalysisLinkHandler {
	return &BaziAnalysisLinkHandler{
		BaseHandler: baseHandler,
		balSvc:      balSvc,
	}
}

func (slf *BaziAnalysisLinkHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/bal/create", slf.Create)
}

// Create 创建八字分析链接
// @Summary 创建八字分析链接
// @Description 创建八字分析链接
// @Tags 八字分析链接
// @Accept json
// @Produce json
// @Param param body v1.CreateBaziAnalysisLinkRequest true "创建八字分析链接请求"
// @Success 200 {object} v1.CreateBaziAnalysisLinkResponse
// @Router /bal/create [post]
func (slf *BaziAnalysisLinkHandler) Create(ctx *gin.Context) {
	var req v1.CreateBaziAnalysisLinkRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.UA = ctx.GetHeader("User-Agent")
	link, err := slf.balSvc.CreateBaziAnalysisLink(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("baziAnalysisLinkService.CreateBaziAnalysisLink", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, link)
}
