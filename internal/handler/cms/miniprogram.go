package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type MiniProgramHandler struct {
	*handler.BaseHandler
	mpService service.MiniProgramService
}

func NewMiniProgramHandler(handler *handler.BaseHandler, mpService service.MiniProgramService) *MiniProgramHandler {
	return &MiniProgramHandler{
		BaseHandler: handler,
		mpService:   mpService,
	}
}

func (slf *MiniProgramHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/mp/qrcode/create", slf.CreateQrCode)
	required.
		POST("/mp/qrcode/pageList", slf.PageListQrCode)
}

// PageListQrCode godoc
// @Summary 小程序二维码列表
// @Description 小程序二维码列表
// @Tags 小程序
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.MpQrCodePageListRequest true "params"
// @Success 200 {object} v1.MpQrCodePageListResponse "二维码列表"
// @Router /mp/qrcode/pageList [post]
func (slf *MiniProgramHandler) PageListQrCode(ctx *gin.Context) {
	var req v1.MpQrCodePageListRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.mpService.PageListQrCode(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("miniprogram.PageListQrCode", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// CreateQrCode godoc
// @Summary 小程序生成二维码
// @Description 小程序生成二维码
// @Tags 小程序
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.MpQrCodeRequest true "params"
// @Success 200 {file} image/png "二维码图片"
// @Router /mp/qrcode/create [post]
func (slf *MiniProgramHandler) CreateQrCode(ctx *gin.Context) {
	var req v1.MpQrCodeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	res, err := slf.mpService.CreateQrCode(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("miniprogram.CreateQrCode", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
