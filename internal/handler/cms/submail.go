package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type SubMailHandler struct {
	*handler.BaseHandler
	submailSvc service.SubMailService
}

func NewSubMailHandler(handler *handler.BaseHandler, submailSvc service.SubMailService) *SubMailHandler {
	return &SubMailHandler{
		BaseHandler: handler,
		submailSvc:  submailSvc,
	}
}

func (slf *SubMailHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/submail/notify/108520", slf.Notify108520)
}

func (slf *SubMailHandler) Notify108520(ctx *gin.Context) {
	var req map[string]any
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.submailSvc.Notify108520(ctx, req); err != nil {
		slf.Log(ctx).Error("failed to notify 108520", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
