package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"
)

type (
	LiunianAnalysisPayload struct {
		BaziYear int
		AgeS     int
		AgeX     int

		RetJyDate         string
		Yangli            string
		Gender            int
		GenderStr         string
		Minggong          string
		Sizhu             []string
		DayunGanzhiList   []string
		XiaoyunGanzhiList []string
		Tiangan           []string
		Dizhi             []string
		Ganzhi            []string
		Xingyun           []string
		XiaoyunQishi      int
		XiaoyunJiezhi     int
		DayunQishi        int
		DayunJiezhi       int
		Riyuan            string
		Zhuxing           []string
		BenqiShishen      []string
		ZhongqiShishen    []string
		YuqiShishen       []string
		Yxcjx             []string
		Shensha4          [][]string
		Shensha6          [][]string
		ShenshaDL         [][]string
		ShishenPowerMap   map[string]float64
		ShishenWuxingMap  map[string]string
	}
)

func (slf *masterService) liunianAnalysis(paipanAll *LiunianAnalysisPayload, paipanLn *corona.GetDayunLiunianScoreResponse, comb23 func([]string, ...string) ([][]string, [][]string)) *v1.MasterLiunianAnalysis {
	var (
		result = &v1.MasterLiunianAnalysis{
			Overview: &v1.MasterLiunianAnalysisOverview{},
		} // 结果
		genderNum       = paipanAll.Gender                                                                      // 性别
		xiaoyunList     = slf.CalcXiaoyunList(paipanAll.Sizhu, genderNum, len(paipanAll.XiaoyunGanzhiList)+120) // 小运列表（n+120）
		xiaoyunListB    = xiaoyunList[:len(paipanAll.XiaoyunGanzhiList)]                                        // 小运列表（n）
		xiaoyunListA    = xiaoyunList[len(paipanAll.XiaoyunGanzhiList):]                                        // 小运列表（120）
		tiangan         = paipanAll.Tiangan[:6]                                                                 // 天干
		dizhi           = paipanAll.Dizhi[:6]                                                                   // 地支
		ganzhi          = paipanAll.Ganzhi[:6]                                                                  // 干支
		xingyun         = paipanAll.XiaoyunGanzhiList                                                           // 星运
		dyStartYear     = paipanAll.DayunQishi                                                                  // 大运起始年份
		dyGanzhiList    = paipanAll.DayunGanzhiList                                                             // 大运干支列表
		baziYear        = paipanAll.BaziYear                                                                    // 八字当前年份
		dyGanzhi        = ganzhi[4]                                                                             // 大运干支
		lnGanzhi        = ganzhi[5]                                                                             // 流年干支
		lnTg            = tiangan[5]                                                                            // 大运天干
		lnDz            = dizhi[5]
		dyDz            = dizhi[4]
		lnTgWx          = slf.GetWuxingByTiangan(lnTg)
		lnDzWx          = slf.GetWuxingByDizhi(lnDz)
		ssg             = corona.NewShishenGetter(paipanAll.Zhuxing[:6], paipanAll.BenqiShishen[:6], paipanAll.ZhongqiShishen[:6], paipanAll.YuqiShishen[:6]) // 十神助手
		yxcjx           = paipanAll.Yxcjx                                                                                                                     // 用喜仇忌闲
		xiyong          = yxcjx[:2]
		chouji          = yxcjx[2:4]
		riyuan          = paipanAll.Riyuan
		shishenPowerMap = paipanAll.ShishenPowerMap
		wuxingMapXiyong = map[string]string{
			yxcjx[0]: "用神",
			yxcjx[1]: "喜神",
			yxcjx[2]: "仇神",
			yxcjx[3]: "忌神",
			yxcjx[4]: "闲神",
		} // 五行喜用
		rg   = paipanAll.Tiangan[2]
		rgwx = slf.GetWuxingByTiangan(rg)
	)
	dyIndex := array.Index(dyGanzhiList, dyGanzhi)
	curDyStartYear := dyStartYear + dyIndex*10
	curDyEndYear := curDyStartYear + 9
	// 流年概述
	{
		result.JiaoyunTime = paipanAll.RetJyDate
		if baziYear >= paipanAll.DayunQishi {
			// 已起运
			offset := baziYear - paipanAll.XiaoyunQishi
			xiaoyun := xiaoyunListA[offset]
			result.Overview.OverviewS = fmt.Sprintf("%d岁流年：%s%d年 大运：%s 小运：%s",
				paipanAll.AgeS, lnGanzhi, baziYear, dyGanzhi, xiaoyun,
			)
			result.Overview.OverviewX = fmt.Sprintf("%d岁流年：%s%d年 大运：%s 小运：%s",
				paipanAll.AgeX, lnGanzhi, baziYear, dyGanzhi, xiaoyun,
			)
			result.Overview.Zhushi = fmt.Sprintf("流年%s主事，运行%s之地。流年能量指数：%d；综合能量指数：%d。",
				ssg.Tg[5], xingyun[5],
				paipanLn.Lnian[offset], paipanLn.Zscore[offset],
			)
		} else {
			// 未起运
			offset := baziYear - paipanAll.XiaoyunQishi
			xiaoyun := xiaoyunListB[baziYear-paipanAll.XiaoyunQishi]
			result.Overview.OverviewS = fmt.Sprintf("%d岁流年：%s%d年 小运：%s",
				paipanAll.AgeS, lnGanzhi, baziYear, xiaoyun,
			)
			result.Overview.OverviewX = fmt.Sprintf("%d岁流年：%s%d年 小运：%s",
				paipanAll.AgeX, lnGanzhi, baziYear, xiaoyun,
			)
			// 示例1：流年伤官主事，运行绝之地。小限能量指数:50；流年能量指数:50；综合能量指数:50。
			result.Overview.Zhushi = fmt.Sprintf("流年%s主事，运行%s之地。小限能量指数：%d；流年能量指数：%d；综合能量指数：%d。",
				ssg.Tg[5], xingyun[5], paipanLn.Xxian[offset], paipanLn.Lnian[offset], paipanLn.Zscore[offset],
			)
		}
	}
	// 流年喜用
	{
		result.Xiyong = fmt.Sprintf("天干%s%s%s，地支%s%s%s。",
			lnTg, lnTgWx, wuxingMapXiyong[lnTgWx],
			lnDz, lnDzWx, wuxingMapXiyong[lnDzWx],
		)
	}
	// 流年卦相
	{
		dyNum := array.Index(slf.GanZhiList, dyGanzhi) + 1
		lnNum := array.Index(slf.GanZhiList, lnGanzhi) + 1
		num := (dyNum + lnNum) % 64
		if num == 0 {
			num = 64
		}
		result.Guayun, _ = slf.luncaiRepo.GetGuaxiangByValue(context.TODO(), num)
	}
	// 流年合化
	{
		result.Hehua = &v1.MasterLiunianAnalysisHehua{}
		var fnZhuNames = func(arr []string, target string) []string {
			var res []string
			for i, s := range arr {
				if s == target {
					switch i {
					case 0:
						res = append(res, "年")
					case 1:
						res = append(res, "月")
					case 2:
						res = append(res, "日")
					case 3:
						res = append(res, "时")
					}
				}
			}
			return res
		}
		// 天干五合
		{
			// #流年天干##对应五合天干#流年#发生的柱##五合名称#
			// {流年天干}{对应五合天干}流年{发生的柱}{五合名称}
			two, _ := comb23(tiangan[:4], lnTg)
			var tmp []string
			for _, arr := range two {
				he, ok := slf.IsTianganWuhe(arr[0], arr[1])
				if !ok {
					continue
				}
				switch he {
				case "土":
					he = "中正之合"
				case "金":
					he = "仁义之和"
				case "水":
					he = "威制之合"
				case "木":
					he = "淫匿之合"
				case "火":
					he = "无情之合"
				}
				var (
					anotherTg = array.Remove(arr, lnTg, 1)[0]
					idxes     = fnZhuNames(tiangan[:4], anotherTg)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s%s", lnTg, anotherTg, idx, he))
				}
			}
			result.Hehua.TianganWuhe = strings.Join(tmp, "，")
		}
		// 地支六合
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				_, ok := slf.IsDizhiLiuhe(arr[0], arr[1])
				if !ok {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s六合", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiLiuhe = strings.Join(tmp, "，")
		}
		// 地支三合
		var sanheRecord []string
		{
			_, three := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range three {
				he, ok := slf.IsDizhiSanhe(arr[0], arr[1], arr[2])
				if !ok {
					continue
				}
				sanheRecord = append(sanheRecord, fmt.Sprintf("%s%s%s", arr[0], arr[1], arr[2]))
				var (
					anotherDz = array.Remove(arr, lnDz, 1)
					idxes1    = fnZhuNames(dizhi[:4], anotherDz[0])
					idxes2    = fnZhuNames(dizhi[:4], anotherDz[1])
				)
				for _, idx1 := range idxes1 {
					for _, idx2 := range idxes2 {
						tmp = append(tmp, fmt.Sprintf("%s%s%s流年与%s与%s三合%s局", lnDz, anotherDz[0], anotherDz[1], idx1, idx2, he))
					}
				}
			}
			result.Hehua.DizhiSanhe = strings.Join(tmp, "，")
		}
		// 地支半三合
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				_, ok := slf.IsDizhiBanhe(arr[0], arr[1])
				if !ok {
					continue
				}
				if func() bool {
					for _, s := range sanheRecord {
						if strings.Contains(s, arr[0]) && strings.Contains(s, arr[1]) {
							return true
						}
					}
					return false
				}() {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s半三合", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiBansanhe = strings.Join(tmp, "，")
		}
		// 地支三会
		{
			_, three := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range three {
				sanhui, ok := slf.IsDizhiSanhui(arr[0], arr[1], arr[2])
				if !ok {
					continue
				}
				switch sanhui {
				case "木":
					sanhui = "东方木"
				case "水":
					sanhui = "北方水"
				case "火":
					sanhui = "南方火"
				case "金":
					sanhui = "西方金"
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)
					idxes1    = fnZhuNames(dizhi[:4], anotherDz[0])
					idxes2    = fnZhuNames(dizhi[:4], anotherDz[1])
				)
				for _, idx1 := range idxes1 {
					for _, idx2 := range idxes2 {
						tmp = append(tmp, fmt.Sprintf("%s%s%s流年与%s与%s三会%s", lnDz, anotherDz[0], anotherDz[1], idx1, idx2, sanhui))
					}
				}
			}
			result.Hehua.DizhiSanhui = strings.Join(tmp, "，")
		}
		// 地支相冲
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				if !slf.IsDizhiXiangchong(arr[0], arr[1]) {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s相冲", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiXiangchong = strings.Join(tmp, "，")
		}
		// 地支相害
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				if !slf.IsDizhiXianghai(arr[0], arr[1]) {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s相害", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiXiangchong = strings.Join(tmp, "，")
		}
		// 地支三刑
		{
			_, three := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range three {
				if !slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)
					idxes1    = fnZhuNames(dizhi[:4], anotherDz[0])
					idxes2    = fnZhuNames(dizhi[:4], anotherDz[1])
					xing      string
				)
				// 寅巳申-无恩之刑， 丑戌未-恃势之刑
				if array.SubArrayExist([]string{"寅", "巳", "申"}, arr) {
					xing = "无恩之刑"
				}
				if array.SubArrayExist([]string{"丑", "戌", "未"}, arr) {
					xing = "恃势之刑"
				}
				for _, idx1 := range idxes1 {
					for _, idx2 := range idxes2 {
						tmp = append(tmp, fmt.Sprintf("%s%s%s流年与%s与%s%s", lnDz, anotherDz[0], anotherDz[1], idx1, idx2, xing))
					}
				}
			}
			result.Hehua.DizhiSanxing = strings.Join(tmp, "，")
		}
		// 地支无礼之刑
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				// 子卯刑-无礼之刑
				if !array.Has([]string{"子", "卯"}, arr...) {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s无礼之刑", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiWuliZhixing = strings.Join(tmp, "，")
		}
		// 地支自刑
		{
			two, _ := comb23(dizhi[:4], lnDz)
			var tmp []string
			for _, arr := range two {
				// 辰辰自刑、午午自刑、酉酉自刑、亥亥自刑
				if arr[0] != arr[1] {
					continue
				}
				if !array.Has([]string{"辰", "午", "酉", "亥"}, arr...) {
					continue
				}
				var (
					anotherDz = array.Remove(arr, lnDz, 1)[0]
					idxes     = fnZhuNames(dizhi[:4], anotherDz)
				)
				for _, idx := range idxes {
					tmp = append(tmp, fmt.Sprintf("%s%s流年与%s自刑", lnDz, anotherDz, idx))
				}
			}
			result.Hehua.DizhiZixing = strings.Join(tmp, "，")
		}
	}
	// 流年岁运
	{
		if tiangan[4] == tiangan[5] {
			result.Suiyun = append(result.Suiyun, "运交岁运并临，须防有重大事情发生。")
		}
		if slf.IsTianganXiangke(tiangan[4], tiangan[5]) && slf.IsDizhiXiangchong(dizhi[4], dizhi[5]) {
			result.Suiyun = append(result.Suiyun, "大运与流年天克地冲。")
		}
		if dizhi[5] == dizhi[0] {
			result.Suiyun = append(result.Suiyun, "今年值本命年，凡事须小心。")
		}
	}
	// 流年神煞
	{
		/*
			若当前流年存在以下神煞

			- 包含驿马
			  - 是否有禄神
			    - 无禄神：输出“今年奔走较为频繁。”；
			    - 有禄神：输出“流年行禄地，有进益之喜”
			  - 剪刃齐全：若以下条件均满足
			    - 大运住存在驿马；
			    - 大运地支与年支、月支、日支或时支发生六冲
			    - 大运或发生六冲的地支所在柱存在羊刃神煞，
			  则显示“大运和命局构成箭、刃齐全，此运要小心，易有血光之灾。”

			- 包含羊刃：输出“流年行羊刃、行事须小心，恐有凶灾或破损耗财。”
			- 包含空亡：输出“流年逢空亡，竹篮打水运。”
		*/
		var fnZhuIdxes = func(arr []string, target string) []int {
			var res []int
			for i, s := range arr {
				if s == target {
					res = append(res, i)
				}
			}
			return res
		}
		if array.Has(paipanAll.Shensha6[5], "驿马") {
			if array.Has(paipanAll.Shensha6[5], "禄神") {
				result.Shensha = append(result.Shensha, "流年行禄地，有进益之喜。")
			} else {
				result.Shensha = append(result.Shensha, "今年奔走较为频繁。")
			}
			if func() bool {
				two, _ := comb23(dizhi[:4], dyDz)
				var (
					idxes []int // 六冲发生的柱（不包括大运柱）
				)
				for _, arr := range two {
					if slf.IsDizhiXiangchong(arr[0], arr[1]) {
						another := array.Remove(arr, dyDz, 1)[0]
						idxes = append(idxes, fnZhuIdxes(dizhi[:4], another)...)
					}
				}
				if len(idxes) == 0 {
					return false
				}
				yangren := false
				for _, idx := range append(idxes, 5) {
					if array.Has(paipanAll.Shensha6[idx], "羊刃") {
						yangren = true
						break
					}
				}
				if !yangren {
					return false
				}
				return true
			}() {
				result.Shensha = append(result.Shensha, "大运和命局构成箭、刃齐全，此运要小心，易有血光之灾。")
			}
		}
		if array.Has(paipanAll.Shensha6[5], "羊刃") {
			result.Shensha = append(result.Shensha, "流年行羊刃、行事须小心，恐有凶灾或破损耗财。")
		}
		if array.Has(paipanAll.Shensha6[5], "空亡") {
			result.Shensha = append(result.Shensha, "流年逢空亡，竹篮打水运。")
		}
	}
	// 论断
	{
		var fnZhuIdxes = func(arr []string, target string) []int {
			var res []int
			for i, s := range arr {
				if s == target {
					res = append(res, i)
				}
			}
			return res
		}
		/*
			1. 条件：满足下述每一条条件
			  - 条件1：伤官对应的五行为原局命理的喜用神五行
			  - 条件2：大运或流年的地支十神为官杀
			- 则显示：【LN101】“ 伤官为用神, 大运流年最忌七杀, 也忌官星, 犯之主破缘, 官司, 破财等事。 ”
		*/
		{
			if func() bool {
				if !array.Has(xiyong, paipanAll.ShishenWuxingMap["伤官"]) {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正官", "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN101]")
			}
		}
		/*
			2. 条件：满足下述每一条条件
			  - 条件1：正官对应的五行为原局命理的喜用神五行
			  - 条件2：大运或流年的地支十神为伤官
			- 则显示：【LN102】“  正官为用神, 大运流年最忌伤官, 犯之主破缘, 官司, 破财等事。”
		*/
		{
			if func() bool {
				if !array.Has(xiyong, paipanAll.ShishenWuxingMap["正官"]) {
					return false
				}
				if !array.Has(ssg.DzListByIdx(4, 5), "伤官") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN102]")
			}
		}
		/*
			3. 条件：满足下述每一条条件
			  - 条件1：原局命主旺衰为 身强、偏强或从强
			  - 条件2：原局命理中，天干十神、地支藏干十神食伤的数量大运等于1
			  - 条件3：原局命理中官杀能量小于10
			  - 条件4：大运地支十神或流年地支十神为财才
			- 则显示：【LN103】“ 身旺而四柱伤尽官星, 行财运当发福。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
					return false
				}
				if ssg.NumMap["食伤"] != 1 {
					return false
				}
				if shishenPowerMap["官杀"] >= 10 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN103]")
			}
		}
		/*
			4. 条件：满足下述每一条条件
			  - 条件1：原局命主旺衰为 身强、偏强或从强
			  - 条件2：原局命理中官杀的能量大于等于150
			  - 条件3：原局命理中天干十神、藏干十神不存在食神，且不存在伤官
			  - 条件4：大运地支十神或流年地支十神为官杀
			- 则显示：【LN104】“  身杀俱旺无制服, 又行杀旺运, 虽贵不久。”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
					return false
				}
				if shishenPowerMap["官杀"] < 150 {
					return false
				}
				if array.HasAny(ssg.All(), "食神", "伤官") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正官", "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN104]")
			}
		}
		/*
			5. 条件：满足下述每一条条件
			  - 条件1：原局命理中天干十神、地支藏干十神中劫财的数量大于等于3个
			  - 条件2：大运地支十神或流年地支十神为劫财
			- 则显示：【LN105】“ 多劫又遇劫运, 守穷途凄惶。 ”
		*/
		{
			if func() bool {
				if ssg.NumMap["劫财"] < 3 {
					return false
				}
				if !array.Has(ssg.DzListByIdx(4, 5), "劫财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN105]")
			}
		}
		/*
			6. 条件：满足下述每一条条件
			  - 条件1：当前流年命主年龄不超过18周岁（以当前流年2月10日计算是否满18周岁）
			  - 条件2：原局命理中天干十神、地支藏干十神中不存在比肩，也不存在劫财
			  - 条件3：原局命理中天干十神、地支藏干十神中不存在正印，也不存在偏印
			  - 条件4：大运地支十神或流年地支十神任意一个为比肩、或者任意一个为劫财。
			- 则显示：【LN106】“  柱无比劫又无印, 幼行比劫运, 由堂兄姐或朋友养大成人。”
		*/
		{
			if func() bool {
				age := paipanAll.AgeS
				if age < 18 {
					return false
				}
				if array.HasAny(ssg.All(), "比肩", "劫财") {
					return false
				}
				if array.HasAny(ssg.All(), "正印", "偏印") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "比肩", "劫财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN106]")
			}
		}
		/*
			7. 条件：满足下述每一条条件
			  - 条件1：原局命理中，四柱神煞存在羊刃
			  - 条件2：大运或流年神煞存在羊刃。
			  - 条件3：原局命理中，财才的能量>=40
			- 则显示：【LN107】“ 柱中羊刃, 运行羊刃岁运相逢, 财物耗散。柱有财重, 无财轻。 ”
		*/
		{
			if func() bool {
				if !array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
					return false
				}
				if !array.Has(array.Merge(paipanAll.ShenshaDL...), "羊刃") {
					return false
				}
				if shishenPowerMap["财才"] < 40 {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN107]")
			}
		}
		/*
			8. 条件：满足下述每一条条件
			  - 条件1： 原局命理命主为女性
			  - 条件2：原局命理天干十神、地支藏干十神中，包含正官
			  - 条件3：原局命理中天干十神、地支藏干十神中，包含正财或包含偏财
			  - 条件4：大运地支十神或流年地支十神任意一个为伤官，或者任意一个为劫财
			- 则显示：【LN108】“ 女逢财官两全, 行伤官劫财运克夫。”
		*/
		{
			if func() bool {
				if genderNum != 2 {
					return false
				}
				if !array.Has(ssg.All(), "正官") {
					return false
				}
				if !array.HasAny(ssg.All(), "正财", "偏财") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "伤官", "劫财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN108]")
			}
		}
		/*
			9. 条件：满足下述每一条条件
			  - 条件1： 原局命理命主为女性
			  - 条件2：原局命理中天干十神、地支藏干十神中比肩机劫财的数量之和大于等于4
			  - 条件3：大运地支十神或流年地支十神任意一个为官杀
			- 则显示：【LN109】“  女命柱中比劫成群, 行官运克夫。”
		*/
		{
			if func() bool {
				if genderNum != 2 {
					return false
				}
				if ssg.NumMap["比肩"]+ssg.NumMap["劫财"] < 4 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正官", "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN109]")
			}
		}
		/*
			10. 条件：满足下述每一条条件
			  - 条件1：原局命理命主为女性
			  - 条件2：四柱地支中存在丑
			  - 条件3：天干中存在辛
			  - 条件4：日干为甲或乙
			  - 条件5：大运或者流年的地支为丑
			- 则显示：【LN110】“ 柱中辛为官星, 有丑为夫库, 岁运至丑, 克夫甚重。”
		*/
		{
			if func() bool {
				if genderNum != 2 {
					return false
				}
				if !array.Has(dizhi, "丑") {
					return false
				}
				if !array.Has(tiangan, "辛") {
					return false
				}
				if !array.Has([]string{"甲", "乙"}, rg) {
					return false
				}
				if !array.Has(ssg.DzListByIdx(4, 5), "丑") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN110]")
			}
		}
		/*
			11. 条件：满足下述每一条条件
			  - 条件1：原局命理日柱地支本气十神为财才
			  - 条件2：大运地支十神或十神地支十神为财才
			- 则显示：【LN111】“ 日元座财, 行财运则发。 ”
		*/
		{
			if func() bool {
				if !array.HasAny([]string{"正财", "偏财"}, ssg.Bq[2]) {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN111]")
			}
		}
		/*
			12. 条件：满足下述每一条条件
			  - 条件1：原局命理中，食伤对应的五行为命主的喜用神五行
			  - 条件2：大运地支十神或流年十神地支为偏印
			- 则显示：【LN112】“ 食神逢枭, 财物耗散。 ”
		*/
		{
			if func() bool {
				if !array.Has(xiyong, paipanAll.ShishenWuxingMap["食伤"]) {
					return false
				}
				if !array.Has(ssg.DzListByIdx(4, 5), "偏印") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN112]")
			}
		}
		/*
			13. 条件：满足下述每一条条件
			  - 条件1：原局命理中，天干十神存在正官
			  - 条件2：原局命理四柱神煞中，存在禄神
			  - 条件3：大运或流年柱中，存在禄神
			- 则显示：【LN114】“  命中带官禄, 行运又遇禄, 加官晋职。”
		*/
		{
			if func() bool {
				if !array.Has(ssg.All(), "正官") {
					return false
				}
				if !array.Has(array.Merge(paipanAll.Shensha4...), "禄神") {
					return false
				}
				if !array.Has(array.Merge(paipanAll.ShenshaDL...), "禄神") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN114]")
			}
		}
		/*
			14. 条件：满足下述每一条条件
			  - 条件1：原局命理十神能量中，印枭的能量大于等于150
			  - 条件2：大运或流年柱的星运为帝旺
			- 则显示：【LN115】“  印旺又行帝旺之运, 登科有准。”
		*/
		{
			if func() bool {
				if shishenPowerMap["印枭"] < 150 {
					return false
				}
				if !array.Has(xingyun[4:6], "帝旺") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN115]")
			}
		}
		/*
			15. 条件：满足下述每一条条件
			  - 条件1：原局命理中日主旺衰为身强、偏强或从强
			  - 条件2：原局命理十神能量中，财才的能量不超过50
			  - 条件3：原局命理十神能量中，官杀的能量不超过50
			  - 条件4：大运地支或流年地支十神，为财才或者为官杀
			- 则显示：【LN116】“ 日主旺, 财官弱, 运入财官名利驰。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
					return false
				}
				if shishenPowerMap["财才"] > 50 {
					return false
				}
				if shishenPowerMap["官杀"] > 50 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财", "正官", "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN116]")
			}
		}
		/*
			16. 按照以下规则判断
			  - 第一步：若以下1、2、3均满足，则进行第二步计算；若不均满足，则结束本条规则的计算
			    - 1：原局命理天干十神、地支藏干十神中，存在正官
			    - 2：原局命理天干十神、地支藏干十神中，不存在七杀
			    - 3：大运地支或流年地支十神，为七杀
			  - 第二步：
			    - 若日主为身弱、偏弱、从强
			      - 则显示：【LN117】“ 命有官星, 遇杀运, 注意身体情况及工作, 有官职之人, 防小人陷害, 可能多病痛。”
			    - 反之
			      - 则显示：【LN118】“  命有官星, 遇杀运, 注意身体情况及工作, 有官职之人, 防小人陷害。”
		*/
		{
			if func() bool {
				if !array.Has(ssg.All(), "正官") {
					return false
				}
				if array.Has(ssg.All(), "七杀") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "七杀") {
					return false
				}
				return true
			}() {
				if array.Has([]string{"身弱", "偏弱", "从强"}, riyuan) {
					result.LunduanKeys = append(result.LunduanKeys, "[LN117]")
				} else {
					result.LunduanKeys = append(result.LunduanKeys, "[LN118]")
				}
			}
		}
		/*
			17. 按照以下规则判断
			  - 第一步：若以下1、2、3均满足，则进行第二步计算；若不均满足，则结束本条规则的计算
			    - 1：原局命理天干十神、地支藏干十神中，存在七杀
			    - 2：原局命理天干十神、地支藏干十神中，不存在正官
			    - 3：大运地支或流年地支十神，为正官
			  - 第二步：
			    - 若日主为身弱、偏弱、从强
			      - 则显示：【LN117】“ 命带七杀, 遇官运, 注意身体情况及工作, 有官职之人, 防小人陷害, 可能多病痛。”
			    - 反之
			      - 则显示：【LN118】“ 命带七杀, 遇官运, 注意身体情况及工作, 有官职之人, 防小人陷害。”
		*/
		{
			if func() bool {
				if !array.Has(ssg.All(), "七杀") {
					return false
				}
				if array.Has(ssg.All(), "正官") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正官") {
					return false
				}
				return true
			}() {
				if array.Has([]string{"身弱", "偏弱", "从强"}, riyuan) {
					result.LunduanKeys = append(result.LunduanKeys, "[LN119]")
				} else {
					result.LunduanKeys = append(result.LunduanKeys, "[LN120]")
				}
			}
		}
		/*
			18. 条件：满足下述每一条条件
			  - 条件1：大运神煞存在驿马
			  - 条件2：流年地支与大运地支发生六冲
			- 则显示：【LN119】“ 流年冲大运马星, 多外出走动或搬家之事。 ”
		*/
		{
			if func() bool {
				if !array.Has(paipanAll.ShenshaDL[0], "驿马") {
					return false
				}
				if !slf.IsDizhiXiangchong(dizhi[4], dizhi[5]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN119]")
			}
		}
		/*
			19. 条件：满足下述每一条条件
			  - 条件1：原局命理中，日主旺衰为偏强、身强、从强
			  - 条件2：大运地支或流年地支十神为比劫
			- 则显示：【LN120】“ 命旺, 遇比劫大运流年, 克父克妻, 不利婚姻。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"偏强", "身强", "从强"}, riyuan) {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "比肩", "劫财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN120]")
			}
		}
		/*
			20. 若 流年地支与年柱地支发生六冲，
			- 则显示：【LN121】“  流年冲八字年柱，命造长辈小心行车，逢冲之丑、未月、申寅、甲申月留意。”
		*/
		if func() bool {
			if !slf.IsDizhiXiangchong(dizhi[5], dizhi[0]) {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN121]")
		}
		/*
			21. 若 流年地支与月柱地支发生六冲，
			- 则显示：【LN122】“  流年冲八字月柱，命造同辈小心行车，逢冲之丑、未月、申寅、甲申月留意。”
		*/
		if func() bool {
			if !slf.IsDizhiXiangchong(dizhi[5], dizhi[1]) {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN122]")
		}
		/*
			22. 若 流年地支与日柱地支发生六冲，
			- 则显示：【LN124】“ 流年冲八字日柱，本人与配偶小心行车，逢冲之丑、未月、申寅、甲申月留意。 ”
		*/
		if func() bool {
			if !slf.IsDizhiXiangchong(dizhi[5], dizhi[2]) {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN124]")
		}
		/*
			23. 若 流年地支与时柱地支发生六冲，
			- 则显示：【LN125】“  流年冲八字时柱，子女小心车祸，逢冲之丑、未月、申寅、甲申月留意。”
		*/
		if func() bool {
			if !slf.IsDizhiXiangchong(dizhi[5], dizhi[3]) {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN125]")
		}
		/*
			24. 若大运地支或流年地支，为食伤
			- 则显示：【LN126】“ 人的运逢食伤，因大胆泄秀之故，行事必然粗心大意而不重安全，因而食伤运比较容易发生意外受伤的事。 ”
		*/
		if func() bool {
			if !array.HasAny(ssg.DzListByIdx(4, 5), "食神", "伤官") {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN126]")
		}
		/*
			25. 条件：满足下述每一条条件
			  - 条件1：原局命理中，时柱地支与年柱地支发生六冲
			  - 条件2：大运地支或流年地支，与年柱地支发生六冲
			- 则显示：【LN127】“  时柱冲克年柱, 岁运又加临冲克,此年父母可能有生命之危。”
		*/
		{
			if func() bool {
				if !slf.IsDizhiXiangchong(dizhi[3], dizhi[0]) {
					return false
				}
				if !slf.IsDizhiXiangchong(dizhi[4], dizhi[0]) && !slf.IsDizhiXiangchong(dizhi[5], dizhi[0]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN127]")
			}
		}
		/*
			26. 条件：满足下述每一条条件
			  - 条件1：流年天干对应的五行 克制 年柱天干对应五行
			  - 条件2：流年地支为辰戌丑未中的一种
			  - 条件3：年柱地支的五行，与流年地支对应的墓库五行相同。
			  注：辰为水库，戌为火库或土库，丑为金库，未为木库
			- 则显示：【LN128】“ 年干被流年干所克, 年支又入墓于流年支, 此年可能有丧父之忧。 ”
		*/
		{
			if func() bool {
				if !slf.IsWuxingXiangke(slf.GetWuxingByTiangan(tiangan[5]), slf.GetWuxingByTiangan(tiangan[0])) {
					return false
				}
				if !array.Has([]string{"辰", "戌", "丑", "未"}, dizhi[5]) {
					return false
				}
				if slf.MukuWuxingMapDizhi(slf.GetWuxingByDizhi(dizhi[0])) == dizhi[5] {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN128]")
			}
		}
		/*
			27. 条件：满足下述每一条条件
			  - 条件1：原局命理四柱神煞中，存在羊刃
			  - 条件2：大运地支或流年地支，与原局四柱中任意一柱发生六冲
			  - 条件3：条件2中与大运或牛年发生六冲的那一柱，神煞存在桃花
			- 则显示：【LN129】“桃花带羊刃, 如运冲桃花或流年冲桃花, 注意不要因色犯刑。  ”
		*/
		{
			if func() bool {
				if !array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
					return false
				}
				two1, _ := comb23(dizhi[:4], dyDz)
				two2, _ := comb23(dizhi[:4], lnDz)
				for _, comb := range append(two1, two2...) {
					if !slf.IsDizhiXiangchong(comb[0], comb[1]) {
						continue
					}
					var (
						anotherDz string
					)
					if array.Has(comb, dizhi[4]) {
						anotherDz = array.Remove(comb, dyDz, 1)[0]
					} else {
						anotherDz = array.Remove(comb, lnDz, 1)[0]
					}
					idx := fnZhuIdxes(dizhi[:4], anotherDz)[0]
					if array.Has(paipanAll.Shensha4[idx], "桃花") {
						return true
					}
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN129]")
			}
		}
		/*
			28. 条件：满足下述每一条条件
			  - 条件1：原局命理四柱神煞中，存在驿马
			  - 条件2：大运地支十神为财才
			  - 条件3：流年地支十神为财才
			- 则显示：【LN130】“柱有马星, 遇财运财年, 则大发其财。  ”
		*/
		{
			if func() bool {
				if !array.Has(array.Merge(paipanAll.Shensha4...), "驿马") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN130]")
			}
		}
		/*
			29. 条件：大运地支或流年地支，与原局命理四柱中任意一柱地支发生相刑
			- 则显示：【LN131】“ 大运, 流年与命局相刑, 则此年不利, 多有刑伤或官非。 ”
		*/
		{
			if func() bool {
				two1, _ := comb23(dizhi[:4], dizhi[4])
				two2, _ := comb23(dizhi[:4], dizhi[5])
				for _, comb := range append(two1, two2...) {
					if !slf.IsDizhiXiangxing(comb[0], comb[1]) {
						continue
					}
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN131]")
			}
		}
		/*
			30. 条件：满足下述每一条条件
			  - 条件1：当前流年命主年龄大于等于50
			  - 条件2：大运或流年神煞，存在驿马
			- 则显示：【LN132】“ 老年岁运遇马星, 主气虚, 腰脚之疾。 ”
		*/
		{
			if func() bool {
				age := paipanAll.AgeS
				if age < 50 {
					return false
				}
				if !array.Has(array.Merge(paipanAll.ShenshaDL...), "驿马") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN132]")
			}
		}
		/*
			31. 条件：满足下述每一条条件
			  - 条件1：日柱为戊戌、庚辰、庚戌、壬辰中的任意一种
			  - 条件2：流年为戊戌、庚辰、庚戌、壬辰中的任意一种
			- 则显示：【LN133】“ 柱有魁罡, 再遇魁罡流年, 官人有提升之事，百姓有灾。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"戊戌", "庚辰", "庚戌", "壬辰"}, ganzhi[4], ganzhi[5]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN133]")
			}
		}
		/*
			32. 条件：满足下述每一条条件
			  - 条件1：原局命理日主旺衰为身强、偏强、从弱
			  - 条件2：大运地支，与年柱地支、月柱地支、日柱地支或时柱地支中的任意两个地支，发生三合
			  - 条件3：条件2中三合的五行，为原局日主的财才
			- 则显示：【LN134】“ 大运与四柱构成三合财局, 身旺, 则此运财命极好。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从弱"}, riyuan) {
					return false
				}
				_, three := comb23(dizhi[:4], dizhi[4])
				for _, comb := range three {
					sanhe, ok := slf.IsDizhiSanhe(comb[0], comb[1], dizhi[2])
					if !ok {
						continue
					}
					if paipanAll.ShishenWuxingMap["财才"] != sanhe {
						continue
					}
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN134]")
			}
		}
		/*
			33. 条件：满足下述每一条条件
			  - 条件1：流年天干和月柱天干发生五合
			  - 条件2：流年地支和月柱地支发生六合
			- 则显示：【LN135】“ 流年和月柱天合地合, 主此年家里有喜事。 ”
		*/
		{
			if func() bool {
				_, ok := slf.IsTianganWuhe(slf.GetWuxingByTiangan(tiangan[5]), slf.GetWuxingByTiangan(tiangan[1]))
				if !ok {
					return false
				}
				_, ok = slf.IsDizhiLiuhe(dizhi[5], dizhi[1])
				if !ok {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN135]")
			}
		}
		/*
			34. 条件：满足下述每一条条件
			  - 条件1：原局命理日主旺衰为身强、偏强、从弱
			  - 条件2：日柱天干和流年天干发生五合
			  - 条件3：五合的五行为日主天干的财才
			- 则显示：【LN136】“  身旺, 日干和流年干合财, 则此年财运不错。”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从弱"}, riyuan) {
					return false
				}
				he, ok := slf.IsTianganWuhe(slf.GetWuxingByTiangan(tiangan[2]), slf.GetWuxingByTiangan(tiangan[5]))
				if !ok {
					return false
				}
				if slf.CaiCai(rgwx) != he {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN136]")
			}
		}
		/*
			35. 条件：满足下述每一条条件
			  - 条件1：原局命理四柱神煞中，存在羊刃
			  - 条件2：条件1中的对应一柱的地支，与流年地支发生六冲
			- 则显示：【LN137】“  流年冲克羊刃, 主此年不顺, 多有刑克之灾。”
		*/
		{
			if func() bool {
				for i, shensha := range paipanAll.Shensha4 {
					if !array.Has(shensha, "羊刃") {
						continue
					}
					if !slf.IsDizhiXiangchong(dizhi[i], dizhi[5]) {
						continue
					}
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN137]")
			}
		}
		/*
			36. 条件：满足下述每一条条件
			  - 条件1：原局命理中日柱天干为甲
			  - 条件2：流年天干为庚或辛
			  - 条件3：流年地支与日柱地支发生六冲
			- 则显示：【LN138】“  日干为甲木, 流年与日柱天克地冲, 多有伤头之灾。”
		*/
		{
			if func() bool {
				if rg != "甲" {
					return false
				}
				if !array.Has([]string{"庚", "辛"}, tiangan[5]) {
					return false
				}
				if !slf.IsDizhiXiangchong(dizhi[5], dizhi[2]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN138]")
			}
		}
		/*
			37. 条件：满足下述每一条条件
			  - 条件1：日柱地支与大运地支发生六合
			  - 条件2：条件1中六合合化的五行，为原局命理的仇神或忌神
			- 则显示：【LN139】“日支和大运支合为忌神, 防婚姻有所变化。  ”
		*/
		{
			if func() bool {
				he, ok := slf.IsDizhiLiuhe(dizhi[2], dizhi[4])
				if !ok {
					return false
				}
				if !array.Has(chouji, he) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN139]")
			}
		}
		/*
			38. 条件：满足下述每一条条件
			  - 条件1：原局命理中，食神对应的五行为原局命理的喜用神
			  - 条件2：大运地支十神或流年地支十神为偏印
			- 则显示：【LN140】“  命局喜食神, 行枭神大运流年, 则此运主不吉, 事多驳杂, 可能会于身体,工作, 生活等有不良影响。”
		*/
		{
			if func() bool {
				if !array.Has(xiyong, paipanAll.ShishenWuxingMap["食伤"]) {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "偏印") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN140]")
			}
		}
		/*
			39. 若 大运地支五行为原局命理的喜用神
			- 则显示：【LN141】“  大运用神到位, 则此运多主平安, 兴旺, 发达。”
		*/
		if func() bool {
			if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[4])) {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN141]")
		}
		/*
			40. 条件：满足下述每一条条件
			  - 条件1：原局命理中，日主旺衰为身弱、偏弱或从强
			  - 条件2：大运地支与原局四柱中的两柱地支，发生三合
			  - 条件3：条件2中,三合合化的五行为原局日主的财才
			- 则显示：【LN142】“大运与四柱构成三合财局, 身弱, 则此运钱财多耗损。  ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身弱", "偏弱", "从强"}, riyuan) {
					return false
				}
				_, three := comb23(dizhi[:4], dizhi[4])
				for _, comb := range three {
					sanhe, ok := slf.IsDizhiSanhe(comb[0], comb[1], dizhi[2])
					if !ok {
						continue
					}
					if paipanAll.ShishenWuxingMap["财才"] != sanhe {
						continue
					}
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN142]")
			}
		}
		/*
			41. 条件：满足下述每一条条件
			  - 条件1：原局命理中，日主旺衰为身弱、偏弱或从强
			  - 条件2：大运地支十神或流年地支十神为财才
			- 则显示：【LN143】“ 身弱遇财运, 为钱财奔波劳累。 ”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身弱", "偏弱", "从强"}, riyuan) {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN143]")
			}
		}
		/*
			42. 条件：满足下述每一条条件
			  - 条件1：以下两条一个为真，此条件满足
			    - 日柱天干为庚或辛
			    - 五行金，为原件命理的喜用神
			  - 条件2：流年地支为丑
			- 则显示：【LN144】“ 凡金日主或金为用神，遇丑墓年可能有凶灾。 ”
		*/
		{
			if func() bool {
				if !array.HasAny([]string{"庚", "辛"}, tiangan[2]) && !array.Has(xiyong, "金") {
					return false
				}
				if dizhi[5] != "丑" {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN144]")
			}
		}
		/*
			43. 条件：流年神煞存在丧门或披麻
			- 则显示：【LN145】“  丧门入命共披麻，劝君切莫去丧家，今年若是真吃了丧家饭，终年卧床绝不差。”
		*/
		if func() bool {
			if !array.Has(paipanAll.ShenshaDL[1], "丧门", "披麻") {
				return false
			}
			return true
		}() {
			result.LunduanKeys = append(result.LunduanKeys, "[LN145]")
		}
		/*
			44. 条件：满足下述每一条条件
			  - 条件1：原局命理中，天干十神中存在官杀
			  - 条件2：大运地支十神或流年地支十神为食伤
			- 则显示：【LN146】“ 食伤克官杀的岁运，有时候会离开工作岗位或者离开其主管。 ”
		*/
		{
			if func() bool {
				if !array.HasAny(ssg.Tg[:4], "正官", "七杀") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "食神", "伤官") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN146]")
			}
		}
		/*
			45. 条件：以下条件满足任意一条即可
			  - 条件1：大运地支十神为七杀，且流年地支十神为财才
			  - 条件2：大运地支十神为财才，且流年地支十神为七杀
			- 则显示：【LN147】“ 杀运财年，或财运杀年，无论生护如何健全，还是很可能会倒霉的。 ”
		*/
		{
			if func() bool {
				if array.Has(ssg.DzListByIdx(4), "七杀") && array.Has(ssg.DzListByIdx(5), "正财", "偏财") {
					return true
				}
				if array.Has(ssg.DzListByIdx(4), "正财", "偏财") && array.Has(ssg.DzListByIdx(5), "七杀") {
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN147]")
			}
		}
		/*
			46. 条件：以下条件满足任意一条即可
			  - 条件1：流年天干五行，克制原局命理中命宫天干五行；且流年地支与原局命理中命宫地支发生六冲
			  - 条件2：大运天干五行，克制原局命理中命宫天干五行；且大运地支与原局命理中命宫地支发生六冲
			- 则显示：【LN148】“ 流年、大运与命宫天克地冲者，小心生命有危险。 ”
		*/
		{
			if func() bool {
				if slf.IsWuxingXiangke(slf.GetWuxingByTiangan(tiangan[5]), slf.GetWuxingByTiangan(string([]rune(paipanAll.Minggong)[0]))) && slf.IsDizhiXiangchong(dizhi[5], dizhi[1]) {
					return true
				}
				if slf.IsWuxingXiangke(slf.GetWuxingByTiangan(tiangan[4]), slf.GetWuxingByTiangan(string([]rune(paipanAll.Minggong)[0]))) && slf.IsDizhiXiangchong(dizhi[4], dizhi[1]) {
					return true
				}
				return false
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN148]")
			}

		}
		/*
			47. 根据流年地支十神，对应输出
			  - 比劫：则显示【LN149】“ 岁支是比劫，就发生和朋友，同事，兄弟姐妹有关的事。 ”
			  - 食伤：则显示【LN150】“  岁支是食伤，就发生同伤病灾，官灾，发表作品，演说，言论，演出，跳舞，展示，投资，策划之类事情。”
			  - 财才：则显示【LN151】“ 岁支是财星，就会发生同妻子，父亲，财运，身体（病伤灾）工作以及婚姻，感情方面之事。 ”
			  - 官杀：则显示【LN152】“  岁支是官杀，就可能发生同父亲，工作，职务，职业，名誉，官司，病伤灾等方面有管你之事。”
			  - 印枭：则显示【LN153】“  岁支是印星，就会发生同印星所代表意义范围有关之事，例：学习，工作，单位，名誉，票据，住房，疾病，财运有关之事。”
		*/
		{
			switch ssg.Dz[5] {
			case "比肩", "劫财":
				result.LunduanKeys = append(result.LunduanKeys, "[LN149]")
				break
			case "食神", "伤官":
				result.LunduanKeys = append(result.LunduanKeys, "[LN150]")
				break
			case "正财", "偏财":
				result.LunduanKeys = append(result.LunduanKeys, "[LN151]")
				break
			case "正官", "七杀":
				result.LunduanKeys = append(result.LunduanKeys, "[LN152]")
				break
			case "正印", "偏印":
				result.LunduanKeys = append(result.LunduanKeys, "[LN153]")
				break
			}
		}
		/*
			48. 条件：满足下述每一条条件
			  - 条件1：原局命理中，天干十神、地支藏干十神中，比肩的数量大于等于3
			  - 条件2：原局命理中，财才五行不为喜用神五行
			  - 条件3：流年天干五行为原局命理的财才
			- 则显示：【LN154】“ 太岁忌逢战斗，主凶。 ”
		*/
		{
			if func() bool {
				if ssg.NumMap["比肩"] < 3 {
					return false
				}
				if array.Has(xiyong, paipanAll.ShishenWuxingMap["财才"]) {
					return false
				}
				if slf.GetWuxingByTiangan(tiangan[5]) != paipanAll.ShishenWuxingMap["财才"] {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN154]")
			}
		}
		/*
			49. 条件：满足下述每一条条件
			  - 条件1：原局命理日主旺衰为身强、偏强、从强
			  - 条件2：流年星运值为长生或临官
			- 则显示：【LN155】“日柱旺，再行归禄或长生之地，小心生命安全。”
		*/
		{
			if func() bool {
				if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
					return false
				}
				if !array.HasAny([]string{"长生", "临官"}, xingyun[5]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN155]")
			}
		}
		/*
			50. 条件：满足下述每一条条件
			  - 条件1：原局命理中天干十神存在正官
			  - 条件2：大运地支与原局四柱地支发生相刑或相冲
			  - 条件3：流年地支与原局四柱地支发生相刑或相冲
			  - 条件4：大运地支十神为伤官或七杀
			  - 条件5：流年地支十神为伤官或七杀
			- 则显示：【LN156】“  正官见杀及伤官刑冲破害，岁运相并必生命危险。”
		*/
		{
			if func() bool {
				if !array.Has(ssg.Tg[:4], "正官") {
					return false
				}
				two1, _ := comb23(dizhi[:4], dizhi[4])
				ok1 := false
				for _, arr := range two1 {
					if slf.IsDizhiXiangchong(arr[0], arr[1]) || slf.IsDizhiXiangxing(arr[0], arr[1]) {
						ok1 = true
						break
					}
				}
				if !ok1 {
					return false
				}
				two2, _ := comb23(dizhi[:4], dizhi[5])
				ok2 := false
				for _, arr := range two2 {
					if slf.IsDizhiXiangchong(arr[0], arr[1]) || slf.IsDizhiXiangxing(arr[0], arr[1]) {
						ok2 = true
						break
					}
				}
				if !ok2 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4), "伤官", "七杀") {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(5), "伤官", "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN156]")
			}
		}
		/*
			51. 条件：满足下述每一条条件
			  - 条件1：流年天干十神或大运天干十神，为正官
			  - 条件2：流年天干十神或大运天干十神，为七杀
			- 则显示：【LN157】“ 杀官大忌岁运相并必生命危险。 ”
		*/
		{
			if func() bool {
				if !array.HasAny(ssg.TgListByIdx(4, 5), "正官") {
					return false
				}
				if !array.HasAny(ssg.TgListByIdx(4, 5), "七杀") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN157]")
			}
		}
		/*
			52. 条件：满足下述每一条条件
			  - 条件1：原局天干十神中，存在财才
			  - 条件2：大运地支或流年地支，与原局命理四柱中的地支，发生六冲或者六合
			  - 条件3：大运地支十神、流年地支十神，均为食伤
			  - 条件4：大运神煞、流年神煞，均存在羊刃
			- 则显示：【LN158】“ 正偏财见比肩分夺，劫财羊刃，又见岁运冲合必生命危险。 ”
		*/
		{
			if func() bool {
				if !array.Has(ssg.Tg[:4], "正财", "偏财") {
					return false
				}
				two1, _ := comb23(dizhi[:4], dizhi[4])
				two2, _ := comb23(dizhi[:4], dizhi[5])
				ok := false
				for _, arr := range array.Merge(two1, two2) {
					if ok = slf.IsDizhiXiangchong(arr[0], arr[1]); ok {
						break
					}
					if _, ok = slf.IsDizhiLiuhe(arr[0], arr[1]); ok {
						break
					}
				}
				if !ok {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "食神", "伤官") {
					return false
				}
				if !array.HasAny(paipanAll.ShenshaDL[0], "羊刃") {
					return false
				}
				if !array.HasAny(paipanAll.ShenshaDL[1], "羊刃") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN158]")
			}
		}
		/*
			53. 条件：满足下述每一条条件
			  - 条件1：原局命理日主性别为女性
			  - 条件2：当前流年，日主年龄大于等于18岁，小于等于40岁
			  - 条件3：大运地支十神或流年地支十神，为伤官
			- 则显示：【LN159】“  要知女命难婚，运入伤官之位。”
		*/
		{
			if func() bool {
				if genderNum != 2 {
					return false
				}
				age := paipanAll.AgeS
				if age < 18 || age > 40 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "伤官") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN159]")
			}
		}
		/*
			54. 条件：满足下述每一条条件
			  - 条件1：原局命理日主性别为男性
			  - 条件2：当前流年，日主年龄大于等于18岁，小于等于25岁
			  - 条件3：大运地支十神或流年地支十神，为财才
			- 则显示：【LN160】“欲议男儿早娶，定是运合财乡。  ”
		*/
		{
			if func() bool {
				if genderNum != 1 {
					return false
				}
				age := paipanAll.AgeS
				if age < 18 || age > 25 {
					return false
				}
				if !array.HasAny(ssg.DzListByIdx(4, 5), "正财", "偏财") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN160]")
			}
		}
		/*
			55. 条件：满足下述每一条条件
			  - 条件1：月柱星运或大运星运，为长生
			  - 条件2：月柱星运或大运星运，为死
			- 则显示：【LN161】“ 生月是日元的长生，大运是日元的死；生月是日元的死，大运是日元的生。此谓生死交换，小心生命安全。 ”
		*/
		{
			if func() bool {
				if !array.HasAny([]string{xingyun[1], xingyun[4]}, "长生", "死") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN161]")
			}
		}
		/*
			56. 条件：满足下述每一条条件
			  - 条件1：当前流年，日主年龄大于等于60岁
			  - 条件2：大运星运或流年星运，为长生或帝旺
			- 则显示：【LN162】“ 老年怕走生旺运，注意身体健康。 ”
		*/
		{
			if func() bool {
				age := paipanAll.AgeS
				if age < 60 {
					return false
				}
				if !array.HasAny([]string{xingyun[4], xingyun[5]}, "长生", "帝旺") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN162]")
			}
		}
		/*
			57. 条件：满足下述每一条条件
			  - 条件1：当前流年，日主年龄小于等于30岁
			  - 条件2：流年星运，为墓或绝
			- 则显示：【LN163】“ 少年怕走墓、绝运。 ”
		*/
		{
			if func() bool {
				age := paipanAll.AgeS
				if age > 30 {
					return false
				}
				if !array.Has([]string{"墓", "绝"}, xingyun[5]) {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN163]")
			}
		}
		/*
			58. 条件：满足下述每一条条件
			  - 条件1：当前流年，日主年龄大于等于60岁
			  - 条件2：大运神煞或者流年神煞，存在天乙贵人
			- 则显示：【LN164】“  老年人行贵人运，注意身体安全。”
		*/
		{
			if func() bool {
				age := paipanAll.AgeS
				if age < 60 {
					return false
				}
				if !array.Has(array.Merge(paipanAll.ShenshaDL...), "天乙贵人") {
					return false
				}
				return true
			}() {
				result.LunduanKeys = append(result.LunduanKeys, "[LN164]")
			}
		}
		/*
			59. 若当前流年，为当前大运的最后一年，
			- 则显示：【LN165】“  在大运将交未交的最后一年，往往有人生重大之变动，命主应特别谨慎渡过。”
		*/
		if paipanAll.BaziYear == curDyEndYear {
			result.LunduanKeys = append(result.LunduanKeys, "[LN165]")
		}
	}
	re := regexp.MustCompile(`^\[([A-Z]{2}\d{3})\]`)
	for _, s := range result.LunduanKeys {
		match := re.FindStringSubmatch(s)
		if len(match) > 1 {
			result.Lunduan = append(result.Lunduan, corona.LiunianLunduanCodes[match[1]])
		} else {
			result.Lunduan = append(result.Lunduan, s)
		}
	}
	// 流月运势分
	{
		result.Liuyue, _ = slf.MonthsScoreList(context.TODO(), 0.5, baziYear, paipanAll.Yangli, paipanAll.GenderStr)
	}
	return result
}
