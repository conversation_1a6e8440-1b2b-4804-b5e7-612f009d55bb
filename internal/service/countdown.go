package service

import (
	"context"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type CountdownDay interface {
	CreateUserCountdownDay(ctx context.Context, req *v1.CreateUserCountdownDayRequest) (*v1.CreateUserCountdownDayResponseData, error)
	UpdateUserCountdownDay(ctx context.Context, req *v1.UpdateUserCountdownDayRequest) error
	DeleteUserCountdownDay(ctx context.Context, req *v1.DeleteUserCountdownDayRequest) error
	ListUserCountdownDay(ctx context.Context, req *v1.ListUserCountdownDayRequest) ([]*v1.UserCountdownDay, error)
}

func NewCountdownDayService(
	service *Service,
	userCountdownDayRepo repository.CalendarCountdownDayRepository,
) CountdownDay {
	return &countdownDay{
		userCountdownDayRepo: userCountdownDayRepo,
		Service:              service,
	}
}

type countdownDay struct {
	userCountdownDayRepo repository.CalendarCountdownDayRepository
	*Service
}

func (slf *countdownDay) CreateUserCountdownDay(ctx context.Context, req *v1.CreateUserCountdownDayRequest) (*v1.CreateUserCountdownDayResponseData, error) {
	remindTime, err := time.Parse("2006-01-02 15:04:05", req.RemindTime)
	if err != nil {
		return nil, err
	}
	var id int64
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		id, err = slf.userCountdownDayRepo.CreateUserCountdownDay(ctx, &model.CalendarUserCountdownDay{
			UserID:     req.User.Id,
			Name:       req.Name,
			Type:       req.Type,
			RemindTime: remindTime,
			RemindType: req.RemindType,
			RepeatTime: req.RepeatTime,
		})
		if err != nil {
			return err
		}
		if req.IsTop {
			return slf.userCountdownDayRepo.SetUserCountdownAsTop(ctx, id, req.User.Id)
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &v1.CreateUserCountdownDayResponseData{ID: id}, nil
}

func (slf *countdownDay) UpdateUserCountdownDay(ctx context.Context, req *v1.UpdateUserCountdownDayRequest) error {
	remindTime, err := time.Parse("2006-01-02 15:04:05", req.RemindTime)
	if err != nil {
		return err
	}
	return slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if err = slf.userCountdownDayRepo.UpdateUserCountdownDay(ctx, &model.CalendarUserCountdownDay{
			ID:         req.ID,
			UserID:     req.User.Id,
			Name:       req.Name,
			Type:       req.Type,
			RemindTime: remindTime,
			RemindType: req.RemindType,
			RepeatTime: req.RepeatTime,
		}); err != nil {
			return err
		}
		if req.IsTop {
			return slf.userCountdownDayRepo.SetUserCountdownAsTop(ctx, req.ID, req.User.Id)
		} else {
			return slf.userCountdownDayRepo.CancelUserCountdownAsTop(ctx, req.ID, req.User.Id)
		}
	})
}

func (slf *countdownDay) DeleteUserCountdownDay(ctx context.Context, req *v1.DeleteUserCountdownDayRequest) error {
	return slf.userCountdownDayRepo.DeleteUserCountdownDay(ctx, req.ID)
}

func (slf *countdownDay) ListUserCountdownDay(ctx context.Context, req *v1.ListUserCountdownDayRequest) ([]*v1.UserCountdownDay, error) {
	items, err := slf.userCountdownDayRepo.ListUserCountdownDay(ctx, req.User.Id)
	if err != nil {
		return nil, err
	}
	var res []*v1.UserCountdownDay
	for _, item := range items {
		res = append(res, &v1.UserCountdownDay{
			ID:         item.ID,
			Name:       item.Name,
			Type:       item.Type,
			RemindTime: item.RemindTime.Format("2006-01-02 15:04:05"),
			RemindType: item.RemindType,
			RepeatTime: item.RepeatTime,
			IsTop:      item.IsTop,
		})
	}
	return res, nil
}
