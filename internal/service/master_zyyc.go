package service

import (
	"strings"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"
)

func (slf *masterService) ZuYeYiChan(paipanAll *corona.GetAllResponse, genderNum int, comb23 func([]string, ...string) ([][]string, [][]string)) []string {
	var (
		keys            []string
		tiangan         = paipanAll.Tiangan[:4]         // 天干
		dizhi           = paipanAll.Dizhi[:4]           // 地支
		rigan           = paipanAll.Tiangan[2]          // 日干
		rgwx            = slf.GetWuxingByTiangan(rigan) // 日干五行
		riyuan          = paipanAll.Riyuan
		xycjx           = strings.SplitN(paipanAll.Xiyongjichou, ",", 5)
		xiyong          = xycjx[:2]
		chouji          = xycjx[2:4]
		ssg             = corona.NewShishenGetter(paipanAll.Zhuxing[:4], paipanAll.BenqiShishen[:4], paipanAll.ZhongqiShishen[:4], paipanAll.YuqiShishen[:4])
		shishenPowerMap = paipanAll.ShishenPowerMap
	)
	{
		/*
			1. 条件：若年柱、月柱干十神、藏干十神同时存在正官、正印、正财
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0, 1), "正官", "正印", "正财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY001]")
		}
	}
	{
		/*
			2. 条件：满足下述任一条件
			  - 条件1：年干、月干、时干十神存在财才
			  - 条件2：年干、月干、时干十神存在食神
			  - 条件3：原局四柱干十神、藏干十神不存在偏印
		*/
		if func() bool {
			if array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return true
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "食神") {
				return true
			}
			if !array.Has(ssg.All(), "偏印") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY002]")
		}
	}
	{
		/*
			3. 条件：满足下述任一条件
			  - 条件1：若原局命理中的十神能量中，食伤的能量值大于等于40，
			  - 条件2：年干、月干十神同时存在正官、正财
		*/
		if func() bool {
			if shishenPowerMap["食伤"] >= 40 {
				return true
			}
			if array.Has(ssg.TgListByIdx(0, 1), "正官", "正财") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY003]")
		}
	}
	{
		/*
			4. 条件：满足下述任一条件
			  - 条件1：年干十神为财才、月干十神为正官，且年柱、月柱星运均在{"长生", "沐浴", "冠带", "临官", "帝旺"}集合内
			  - 条件2：年干十神为正官、月干十神为财才，且年柱、月柱星运均在{"长生", "沐浴", "冠带", "临官", "帝旺"}集合内
		*/
		if func() bool {
			if (ssg.Tg[0] == "财才" && ssg.Tg[1] == "正官" && array.Has([]string{"长生", "沐浴", "冠带", "临官", "帝旺"}, paipanAll.GetXingyunList[0], paipanAll.GetXingyunList[1])) ||
				(ssg.Tg[0] == "正官" && ssg.Tg[1] == "财才" && array.Has([]string{"长生", "沐浴", "冠带", "临官", "帝旺"}, paipanAll.GetXingyunList[0], paipanAll.GetXingyunList[1])) {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY004]")
		}
	}
	{
		/*
			5. 条件：若年柱、月柱干十神、藏干十神，同时存在伤官、劫财
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0, 1), "伤官", "劫财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY005]")
		}
	}
	{
		/*
			6. 条件：若年柱、月柱干十神、藏干十神，同时存在财才（正财或偏财至少有一个即可）、正官、正印
		*/
		if func() bool {
			if !array.HasAny(ssg.ByIdx(0, 1), "正财", "偏财") {
				return false
			}
			if !array.Has(ssg.ByIdx(0, 1), "正官", "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY006]")
		}
	}
	{
		/*
			7. 条件：若年柱存在食神，且食神五行为当前命理的喜用五行，且八字原局命理，不存在偏印
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0), "食神") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["食神"]) {
				return false
			}
			if array.Has(ssg.All(), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY007]")
		}
	}
	{
		/*
			8. 条件：若年柱存在伤官，且伤官五行为当前命理的仇忌五行
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0), "伤官") {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY008]")
		}
	}
	{
		/*
			9. 条件：若年柱存在正财，且正财五行为当前命理的喜用五行
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0), "正财") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["正财"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY009]")
		}
	}
	{
		/*
			10. 条件：若年干自坐的十二长生为 临官或者帝旺
		*/
		if func() bool {
			if !array.Has([]string{"临官", "帝旺"}, paipanAll.GetZizuoList[0]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY010]")
		}
	}
	{
		/*
			11. 条件：若日主旺衰为偏弱、身弱、或从弱
		*/
		if func() bool {
			if !array.Has([]string{"偏弱", "身弱", "从弱"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY011]")
		}
	}
	{
		/*
			12. 条件：若年支藏干十神有偏印，且该命理的喜用神五行为偏印的五行
		*/
		if func() bool {
			if !array.Has(ssg.CgByIdx(0), "偏印") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["偏印"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY012]")
		}
	}
	{
		/*
			13. 条件：若年支藏干十神有伤官，且该命理的喜用神五行为伤官的五行
		*/
		if func() bool {
			if !array.Has(ssg.CgByIdx(0), "伤官") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY013]")
		}
	}
	{
		/*
			14. 条件：若年干十神为伤官，且年支藏干十神有伤官，且该命理的仇忌五行为伤官五行
		*/
		if func() bool {
			if ssg.Tg[0] != "伤官" {
				return false
			}
			if !array.Has(ssg.CgByIdx(0), "伤官") {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY014]")
		}
	}
	{
		/*
			15. 条件：若年干十神为正官，且该命理原局中不存在其他正官、七杀及伤官
		*/
		if func() bool {
			if ssg.Tg[0] != "正官" {
				return false
			}
			if array.HasAny(array.Merge(ssg.TgListByIdx(1, 2, 3), ssg.CangGan()), "正官", "七杀", "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY015]")
		}
	}
	{
		/*
			16. 条件：满足下述任一条件
			  - 条件1：若年干十神为正官，月干十神为正印
			  - 条件2：若年干十神为正官，月干十神为正财
		*/
		if func() bool {
			if (ssg.Tg[0] == "正官" && ssg.Tg[1] == "正印") || (ssg.Tg[0] == "正官" && ssg.Tg[1] == "正财") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY016]")
		}
	}
	{
		/*
			17. 条件：若以下条件均满足
			  - 条件1：年柱天干十神为正官
			  - 条件2：年柱地支十神、月柱天干十神、月柱地支十神，均不为伤官
			  - 条件3：年年柱地支十神、月柱天干十神、月柱地支十神，均为帮扶十神（为比劫，或者印枭）
			  - 条件4：年支，不与与月支、日支或时支中任意一个或多个，发生相冲或相刑
			  - 条件:5：年柱神煞不存在空亡
		*/
		if func() bool {
			if ssg.Tg[0] != "正官" {
				return false
			}
			if array.Has([]string{
				ssg.Dz[0],
				ssg.Tg[1],
				ssg.Dz[1],
			}, "伤官") {
				return false
			}
			if !array.Has([]string{"比肩", "劫财", "正印", "偏印"}, ssg.Dz[0], ssg.Tg[1], ssg.Dz[1]) {
				return false
			}
			two, _ := comb23(dizhi, dizhi[0])
			for _, arr := range two {
				if slf.IsDizhiXiangchong(arr[0], arr[1]) {
					return false
				}
				if slf.IsDizhiXiangxing(arr[0], arr[1]) {
					return false
				}
			}
			return true
		}() {
			keys = append(keys, "[ZY017]")
		}
	}
	{
		/*
			18. 条件：若以下条件均满足
			  - 条件1：年柱天干十神，为比肩
			  - 条件2：年月日时四柱的地支藏干十神中，均无无比肩、劫财
			  - 条件3：年柱地支十神、月柱天干十神、月柱地支十神中，官杀的数量大于等于2
			  - 条件4：日主旺衰为从弱、身弱或偏弱
		*/
		if func() bool {
			if ssg.Tg[0] != "比肩" {
				return false
			}
			if array.HasAny(ssg.CangGan(), "比肩", "劫财") {
				return false
			}
			if array.Count([]string{ssg.Dz[0], ssg.Tg[1], ssg.Dz[1]}, "官杀") < 2 {
				return false
			}
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY018]")
		}
	}
	{
		/*
			19. 条件：若以下条件均满足
			  - 条件1：日主旺衰为从强、身强、偏强
			  - 条件2：年柱天干十神为比肩
			  - 条件3：年柱地支藏干十神，存在比肩
			  - 条件4：比肩所属五行，不是原局命理的喜用神五行
		*/
		if func() bool {
			if !array.Has([]string{"从强", "身强", "偏强"}, riyuan) {
				return false
			}
			if ssg.Tg[0] != "比肩" {
				return false
			}
			if !array.Has(ssg.CgByIdx(0), "比肩") {
				return false
			}
			if array.Has(xiyong, paipanAll.ShishenWuxingMap["比肩"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY019]")
		}
	}
	{
		/*
			20. 条件：若以下条件均满足
			  - 条件1：日主旺衰为从强、身强、偏强
			  - 条件2：年柱太能干五行，不为原局命理日主的喜用神五行
			  - 条件3：年柱天干十神、月柱天干十神，均为比劫
			  - 条件4：时柱天干十神，不为比财
		*/
		if func() bool {
			if !array.Has([]string{"从强", "身强", "偏强"}, riyuan) {
				return false
			}
			if array.Has(xiyong, slf.GetWuxingByTiangan(tiangan[0])) {
				return false
			}
			if !array.Has([]string{"比劫"}, ssg.TgListByIdx(0, 1)...) {
				return false
			}
			if ssg.Tg[3] == "比财" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY020]")
		}
	}
	{
		/*
			21. 条件：若以下条件均满足
			  - 条件1：年柱天干十神，为比肩
			  - 条件2：年柱地支十神，为财才
			  - 条件3：月柱天干十神、月柱地支十神，均不为官杀
			  - 条件4：原局四柱天干十神中，存在印枭
			  - 条件5：原局命理十神能量中，印枭能量值大于等于90
		*/
		if func() bool {
			if ssg.Tg[0] != "比肩" {
				return false
			}
			if ssg.Dz[0] != "财才" {
				return false
			}
			if ssg.Tg[1] == "官杀" || ssg.Dz[1] == "官杀" {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正印", "偏印") {
				return false
			}
			if shishenPowerMap["印枭"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY021]")
		}
	}
	{
		/*
			22. 条件：若以下条件均满足
			  - 条件1：月柱天干十神，为比肩
			  - 条件2：原局年月日时四柱地支藏干十神，不存在比劫
			  - 条件3：年干十神或年支十神，存在官杀
			  - 条件4：原局年月日时四柱地支藏干十神，存在官杀
		*/
		if func() bool {
			if ssg.Tg[1] != "比肩" {
				return false
			}
			if array.HasAny(ssg.CangGan(), "比肩", "劫财") {
				return false
			}
			if !array.HasAny([]string{ssg.Tg[0], ssg.Dz[0]}, "正官", "七杀") {
				return false
			}
			if !array.HasAny(ssg.CangGan(), "正官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY022]")
		}
	}
	{
		/*
			23. 条件：若以下条件均满足
			  - 条件1：原局年月日时四柱地支十神，均为正财
			  - 条件2：原局年月日时四柱天干十神,均无财才
		*/
		if func() bool {
			if !array.Has([]string{"正财"}, ssg.Dz...) {
				return false
			}
			if array.HasAny(ssg.Tg, "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY023]")
		}
	}
	{
		/*
			24. 条件：若以下条件均满足
			  - 条件1：年干十神为正印
			  - 条件2：年柱星运为{ "衰", "病", "死", "墓", "绝"}集合的值
			  - 条件3：月干十神为劫财
		*/
		if func() bool {
			if ssg.Tg[0] != "正印" {
				return false
			}
			if !array.Has([]string{"衰", "病", "死", "墓", "绝"}, paipanAll.GetXingyunListLiuAll[0]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY024]")
		}
	}
	{
		/*
			25. 条件：若以下条件均满足
			  - 条件1：年干十神为偏官
			  - 条件2：地支藏干十神中，均无正官、
			  - 条件3：年干五行生、克、或者被克月支五行
			  - 条件4：年干五行不为喜用
		*/
		if func() bool {
			if ssg.Tg[0] != "七杀" {
				return false
			}
			if array.HasAny(ssg.CangGan(), "正官") {
				return false
			}
			var (
				ngWx = slf.GetWuxingByTiangan(ssg.Tg[0])
				yzWx = slf.GetWuxingByDizhi(ssg.Dz[1])
			)
			if !slf.IsWuxingXiangsheng(ngWx, yzWx) &&
				!slf.IsWuxingXiangke(ngWx, yzWx) &&
				!slf.IsWuxingXiangke(yzWx, ngWx) {
				return false
			}
			if array.Has(xiyong, ngWx) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY025]")
		}
	}
	{
		/*
			26. 条件：若以下条件均满足
			  - 条件1：年干为偏财
			  - 条件2：年干五行不为喜用
		*/
		if func() bool {
			if ssg.Tg[0] != "偏财" {
				return false
			}
			if array.Has(xiyong, slf.GetWuxingByTiangan(tiangan[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY026]")
		}
	}
	{
		/*
			27. 条件：若以下条件均满足
			  - 条件1：年干十神为正财或偏财
			  - 条件2：原局八字命理中，年干未发生五合
			  - 条件3：原局天干十神中，无比劫
			  - 条件4：该命理旺衰为身强、偏强、从强
			  - 条件五：命理干十神及藏干十神的比劫数量，大于等于3
		*/
		if func() bool {
			if !array.Has([]string{"正财", "偏财"}, ssg.Tg[0]) {
				return false
			}
			two, _ := comb23(tiangan, tiangan[0])
			for _, arr := range two {
				if _, ok := slf.IsTianganWuhe(arr[0], arr[1]); ok {
					return false
				}
			}
			if array.Has(ssg.Tg, "比劫") {
				return false
			}
			if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
				return false
			}
			if ssg.NumMap["比劫"] < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY027]")
		}
	}
	{
		/*
			28. 条件：若以下条件均满足
			  - 条件1：年干十神为正财
			  - 条件2：该命理的正财对应的五行为喜用神
		*/
		if func() bool {
			if ssg.Tg[0] != "正财" {
				return false
			}
			return array.Has(xiyong, paipanAll.ShishenWuxingMap["正财"])
		}() {
			keys = append(keys, "[ZY028]")
		}
	}
	{
		/*
			29. 条件：若以下条件均满足
			  - 条件1：年干十神为正财
			  - 条件2：月干十神为正财、或者正官
		*/
		if func() bool {
			if ssg.Tg[0] != "正财" {
				return false
			}
			return array.Has([]string{"正财", "正官"}, ssg.Tg[1])
		}() {
			keys = append(keys, "[ZY029]")
		}
	}
	{
		/*
			30. 条件：若以下条件均满足
			  - 条件1：年干十神为正财
			  - 条件2：地支藏干十神中，存在正财
			  - 条件3：月柱、日柱、时柱，均无正财、偏财
			  - 条件4：月柱干十神或藏干十神有食伤
		*/
		if func() bool {
			if ssg.Tg[0] != "正财" {
				return false
			}
			if !array.Has(ssg.CangGan(), "正财") {
				return false
			}
			if array.HasAny(ssg.ByIdx(1, 2, 3), "正财", "偏财") {
				return false
			}
			if !array.HasAny(ssg.ByIdx(1), "食神", "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY030]")
		}
	}
	{
		/*
			31. 条件：若以下条件均满足
			  - 条件1：年干十神为正财
			  - 条件2：地支藏干十神中，存在正财
			  - 条件3：月柱、日柱、时柱，均无正财、偏财
			  - 条件4：月柱干十神或藏干正官或七杀
		*/
		if func() bool {
			if ssg.Tg[0] != "正财" {
				return false
			}
			if !array.Has(ssg.CangGan(), "正财") {
				return false
			}
			if array.HasAny(ssg.ByIdx(1, 2, 3), "正财", "偏财") {
				return false
			}
			if !array.HasAny(ssg.ByIdx(1), "正官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY031]")
		}
	}
	{
		/*
			32. 条件：若以下条件均满足
			  - 条件1：月干十神为正财或偏财
			  - 条件2：月支藏干十神为正财或偏财
			  - 条件3：年柱、日柱、时柱，均无正财或偏财
		*/
		if func() bool {
			if !array.Has([]string{"正财", "偏财"}, ssg.Tg[1]) {
				return false
			}
			if array.HasAny(ssg.CgByIdx(1), "正财", "偏财") {
				return false
			}
			if array.HasAny(ssg.ByIdx(0, 2, 3), "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY032]")
		}
	}
	{
		/*
			33. 条件：若以下条件均满足
			  - 条件1：年干十神为伤官
			  - 条件2：伤官对应五行为原局命理的忌神
		*/
		if func() bool {
			if ssg.Tg[0] != "伤官" {
				return false
			}
			if xycjx[3] != paipanAll.ShishenWuxingMap["伤官"] {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY033]")
		}
	}
	{
		/*
			34. 条件：若以下条件均满足
			  - 条件1：年柱中干十神、藏干十神中，事少存在一个伤官
			  - 条件2：月支藏干十神中，存在伤官
			  - 条件3：原局命理中，印枭的力量小于90
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0), "伤官") {
				return false
			}
			if !array.Has(ssg.CgByIdx(1), "伤官") {
				return false
			}
			if shishenPowerMap["印枭"] >= 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY034]")
		}
	}
	{
		/*
			35. 条件：若以下条件满足任意一个
			  - 条件1：年干与月干十神均为伤官
			  - 条件2：年支的藏干十神、月干的藏干十神，均有伤官
		*/
		if func() bool {
			if ssg.Tg[0] == "伤官" && ssg.Tg[1] == "伤官" {
				return true
			}
			if array.Has(ssg.CgByIdx(0), "伤官") && array.Has(ssg.CgByIdx(1), "伤官") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY035]")
		}
	}
	{
		/*
			36. 条件：若以下条件均满足
			  - 条件1：月干干十神为食神
			  - 条件2：月干藏干十神存在食神
			  - 条件3：原局四柱八字中，同时存在三个及三个以上食伤十神
		*/
		if func() bool {
			if ssg.Tg[1] != "食神" {
				return false
			}
			if !array.Has(ssg.CgByIdx(1), "食神") {
				return false
			}
			if ssg.NumMap["食神"]+ssg.NumMap["伤官"] < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY036]")
		}
	}
	{
		/*
			37. 条件：若以下条件满足任意一个
			  - 条件1：年干、月干十神同时存在正官、正印
			  - 条件2：年干或月干对应的五行为喜用五行
		*/
		if func() bool {
			if array.Has(ssg.TgListByIdx(0, 1), "正官", "正印") {
				return true
			}
			if array.HasAny(xiyong, slf.GetWuxingByTiangan(tiangan[0]), slf.GetWuxingByTiangan(tiangan[1])) {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY037]")
		}
	}
	{
		/*
			38. 条件：若以下条件满足任意一个
			  - 条件1：年干对应五行，为喜用神
			  - 条件2：年支对应五行，为喜用神
		*/
		if func() bool {
			if array.Has(xiyong, slf.GetWuxingByTiangan(tiangan[0])) {
				return true
			}
			if array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[0])) {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY038]")
		}
	}
	{
		/*
			39. 条件：若以下条件均满足
			  - 条件1：年柱神煞存在 禄神
			  - 条件2：年柱干十神或藏干十神，存在正印
			  - 条件3：年柱干十神或藏干十神，存在正财
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[0], "禄神") {
				return false
			}
			if !array.Has(ssg.ByIdx(0), "正印") {
				return false
			}
			if !array.Has(ssg.ByIdx(0), "正财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY039]")
		}
	}
	{
		/*
			40. 条件：若以下条件均满足
			  - 条件1：原局命理十神能量中，财才能量大于等于90
			  - 条件2：日干天干的五行、为月支五行的印枭或比劫
		*/
		if func() bool {
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			yzWx := slf.GetWuxingByDizhi(dizhi[1])
			if slf.YinXiao(rgwx) != yzWx && slf.BiJie(rgwx) != yzWx {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY040]")
		}
	}
	{
		/*
			41. 条件：若以下条件均满足
			  - 条件1：原局命理十神能量中，财才能量大于等于90
			  - 条件2：原局命理中命主旺衰为偏强、身强或从强
		*/
		if func() bool {
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1), "偏强", "身强", "从强") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY041]")
		}
	}
	{
		/*
			42. 条件：若以下条件满足任意一个
			  - 月支发生了六冲
			  - 月支别月干克制
			  - 月支被日支克制
			  - 月支被年支克制
		*/
		if func() bool {
			two, _ := comb23(dizhi, dizhi[1])
			for _, arr := range two {
				if slf.IsDizhiXiangchong(arr[0], arr[1]) {
					return true
				}
			}
			var (
				yzWx = slf.GetWuxingByDizhi(dizhi[1])
				ygWx = slf.GetWuxingByTiangan(tiangan[1])
				rzWx = slf.GetWuxingByDizhi(dizhi[2])
				nzWx = slf.GetWuxingByDizhi(dizhi[0])
			)
			if slf.IsWuxingXiangke(ygWx, yzWx) {
				return true
			}
			if slf.IsWuxingXiangke(rzWx, yzWx) {
				return true
			}
			if slf.IsWuxingXiangke(nzWx, yzWx) {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY042]")
		}
	}
	{
		/*
			43. 条件：若年支与月支相冲
		*/
		if func() bool {
			return slf.IsDizhiXiangchong(dizhi[0], dizhi[1])
		}() {
			keys = append(keys, "[ZY043]")
		}
	}
	{
		/*
			44. 条件：若以下条件均满足
			  - 条件1：日支为戌
			  - 条件2：时支为辰
		*/
		if func() bool {
			if dizhi[2] != "戌" {
				return false
			}
			if dizhi[3] != "辰" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY044]")
		}
	}
	{
		/*
			45. 条件：若年柱、月柱、时柱任一一柱以下条件均满足
			  - 条件1：该柱天干为七杀
			  - 条件2：该柱神煞存在羊刃
			  - 条件3：该柱地支对应的五行为该命理的仇忌神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if tiangan[idx] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY045]")
		}
	}
	{
		/*
			46. 条件：若以下条件均满足
			  - 条件1：年柱、月柱的干十神、地支十神，均无财才、官杀
			  - 条件2：日柱地支十神、时柱天干十神、时柱地支十神，存在财才
			  - 条件3：日柱地支十神、时柱天干十神、时柱地支十神，存在官杀
		*/
		if func() bool {
			if array.HasAny(ssg.ByIdx(0, 1), "正财", "偏财") {
				return false
			}
			if !array.HasAny([]string{ssg.Dz[2], ssg.Tg[3], ssg.Dz[3]}, "正财", "偏财") {
				return false
			}
			if !array.HasAny([]string{ssg.Dz[2], ssg.Tg[3], ssg.Dz[3]}, "正官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY046]")
		}
	}
	{
		/*
			47. 条件：若以下条件均满足
			  - 条件1：年干为偏印
			  - 条件2：年干对应五行为忌神
		*/
		if func() bool {
			if tiangan[0] != "偏印" {
				return false
			}
			if slf.GetWuxingByTiangan(tiangan[0]) != xycjx[3] {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY047]")
		}
	}
	{
		/*
			48. 条件：若以下条件均满足
			  - 条件1：原局命理命主旺衰为身弱、偏弱
			  - 条件2：年柱神煞存在羊刃
		*/
		if func() bool {
			if !array.Has([]string{"身弱", "偏弱"}, riyuan) {
				return false
			}
			if !array.Has(paipanAll.Shensha4[0], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY048]")
		}
	}
	{
		/*
			49. 条件：若以下条件均满足
			  - 条件1：年支与月支相同，是同一个地支
			  - 条件2：年支地支对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			if dizhi[0] != dizhi[1] {
				return false
			}
			if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY049]")
		}
	}
	{
		/*
			50. 条件：若以下条件均满足
			  - 条件1：原局命理中命主旺衰为 从强、身弱、偏弱
			  - 条件2：原局命理中官杀能量大于等于90
			  - 条件3：原局命理中财星能量大于等于90
			  - 条件4：原局命主为女性
		*/
		if func() bool {
			if !array.Has([]string{"从强", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["官杀"] < 90 {
				return false
			}
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if genderNum != 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY050]")
		}
	}
	{
		/*
			51. 条件：若以下条件均满足
			  - 条件1：原局命理中命主旺衰为 从弱、身强、偏强
			  - 条件2：原局命理中无正财或偏财
			  - 条件3：原局命理中无食神或伤官
		*/
		if func() bool {
			if !array.Has([]string{"从弱", "身强", "偏强"}, riyuan) {
				return false
			}
			if array.HasAny(ssg.All(), "正财", "偏财") {
				return false
			}
			if array.HasAny(ssg.All(), "食神", "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY051]")
		}
	}
	{
		/*
			52. 条件：若以下条件均满足
			  - 条件1：原局命理中命主旺衰为 从强、身弱、偏弱
			  - 条件2：原局命理中财才能量大于等于40
		*/
		if func() bool {
			if !array.Has([]string{"从强", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["财才"] < 40 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY052]")
		}
	}
	{
		/*
			53. 条件：若以下条件均满足
			  - 条件1：日支为酉
			  - 条件2：时支为卯
		*/
		if func() bool {
			if dizhi[2] != "酉" {
				return false
			}
			if dizhi[3] != "卯" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY053]")
		}
	}
	{
		/*
			54. 条件：若年支与日支六冲
		*/
		if func() bool {
			return slf.IsDizhiXiangchong(dizhi[0], dizhi[2])
		}() {
			keys = append(keys, "[ZY054]")
		}
	}
	{
		/*
			55. 条件：若以下条件均满足
			  - 条件1：年支本气天干对应的十神为正财或偏财
			  - 条件2：命理中财才的能量小于40
			  - 条件3：命理中干十神及藏干比劫数量大于等于3个
		*/
		if func() bool {
			if !array.Has([]string{"正财", "偏财"}, ssg.Bq[0]) {
				return false
			}
			if shishenPowerMap["财才"] >= 40 {
				return false
			}
			if ssg.NumMap["比劫"] < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY055]")
		}
	}
	{
		/*
			56. 条件：若以下条件均满足
			  - 条件1：年支本气天干对应的十神为正财或偏财
			  - 条件2：年支对应的五行为该局命理的仇忌神
		*/
		if func() bool {
			if !array.Has([]string{"正财", "偏财"}, ssg.Bq[0]) {
				return false
			}
			if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY056]")
		}
	}
	{
		/*
			57. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱天干对应的十神为偏印
			  - 条件2：该柱神煞存在羊刃
			  - 条件3：该柱天干对应的五行为仇忌神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "偏印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if !array.Has(chouji, slf.GetWuxingByTiangan(tiangan[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY057]")
		}
	}
	{
		/*
			58. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱干天干对应的十神为正印
			  - 条件2：该柱原局地支存在六冲（与大运流年无关）
			  - 条件3：该柱地支对应的五行为仇忌神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !slf.IsDizhiXiangchong(dizhi[idx], paipanAll.Dizhi[idx]) {
					continue
				}
				if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY058]")
		}
	}
	{
		/*
			59. 条件：若以下条件均满足
			  - 条件1：该柱神煞存在禄神
			  - 条件2：该柱原局地支存在六冲（与大运流年无关）
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "禄神") {
					continue
				}
				if !slf.IsDizhiXiangchong(dizhi[idx], paipanAll.Dizhi[idx]) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY059]")
		}
	}
	{
		/*
			60. 条件：若年柱天干十神为伤官
		*/
		if func() bool {
			if ssg.Tg[0] != "伤官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY060]")
		}
	}
	{
		/*
			61. 条件：若以下条件均满足
			  - 条件1：年干对应的五行为该原局命理的仇忌神
			  - 条件2：年支对应的五行为该原局命理的仇忌神
		*/
		if func() bool {
			if !array.Has(chouji, slf.GetWuxingByTiangan(tiangan[0])) {
				return false
			}
			if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY061]")
		}
	}
	{
		/*
			62. 条件：若以下条件满足任意一个满足
			  - 条件1：年干十神为偏印、月干十神为食神
			  - 条件2：年干十神为食神、月干十神为偏印
		*/
		if func() bool {
			if ssg.Tg[0] == "偏印" && ssg.Tg[1] == "食神" {
				return true
			}
			if ssg.Tg[0] == "食神" && ssg.Tg[1] == "偏印" {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY062]")
		}
	}
	{
		/*
			63. 条件：若以下条件均满足
			  - 条件1：年干十神为偏印、月干十神为正印
			  - 条件2：年干十神为正印、月干十神为偏印
		*/
		if func() bool {
			if ssg.Tg[0] == "偏印" && ssg.Tg[1] == "正印" {
				return true
			}
			if ssg.Tg[0] == "正印" && ssg.Tg[1] == "偏印" {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY063]")
		}
	}
	{
		/*
			64. 条件：若以下条件均满足
			  - 条件1：年干为七杀
			  - 条件2：原局命理中干十神、支十神比劫数量大于等于3
		*/
		if func() bool {
			if ssg.Tg[0] != "七杀" {
				return false
			}
			if array.Count(array.Merge(ssg.Tg, ssg.Dz), "比肩", "劫财") < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY064]")
		}
	}
	{
		/*
			65. 条件：若年柱神煞存在驿马
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[0], "驿马") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY065]")
		}
	}
	{
		/*
			66. 条件：若以下条件均满足
			  - 条件1：年柱自坐为死、绝或者墓
			  - 条件2：原局年柱地支存在三刑、六冲、或者相刑
		*/
		if func() bool {
			if !array.Has([]string{"死", "绝", "墓"}, paipanAll.GetZizuoList[0]) {
				return false
			}
			two, three := comb23(dizhi, dizhi[0])
			for _, arr := range two {
				if slf.IsDizhiXiangchong(arr[0], arr[1]) {
					return true
				}
				if slf.IsDizhiXiangxing(arr[0], arr[1]) {
					return true
				}
			}
			for _, arr := range three {
				if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[ZY066]")
		}
	}
	{
		/*
			67. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱天干十神为七杀
			  - 条件2：该柱神煞存在亡神
			  - 条件3：该柱地支对应五行为该命理的仇忌五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "亡神") {
					continue
				}
				if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY067]")
		}
	}
	{
		/*
			68. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱天干十神为正官
			  - 条件2：该柱神煞存在桃花
			  - 条件3：该柱地支存在三合、半合、暗合
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "桃花") {
					continue
				}
				two, three := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				for _, arr := range two {
					if _, ok := slf.IsDizhiBanhe(arr[0], arr[1]); ok {
						return true
					}
					if ok := slf.IsDizhiAnhe(arr[0], arr[1]); ok {
						return true
					}
				}
				for _, arr := range three {
					if _, ok := slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); ok {
						return true
					}
				}
			}
			return false
		}() {
			keys = append(keys, "[ZY068]")
		}
	}
	{
		/*
			69. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱神煞存在驿马
			  - 条件2：该柱地支未发生六合、三合、半合、暗合、拱合
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "驿马") {
					continue
				}
				two, three := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				for _, arr := range two {
					if _, ok := slf.IsDizhiLiuhe(arr[0], arr[1]); ok {
						continue
					}
					if _, ok := slf.IsDizhiBanhe(arr[0], arr[1]); ok {
						continue
					}
					if ok := slf.IsDizhiAnhe(arr[0], arr[1]); ok {
						continue
					}
					if ok := slf.IsDizhiGonghe(arr[0], arr[1]); ok {
						continue
					}
				}
				for _, arr := range three {
					if _, ok := slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); ok {
						continue
					}
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY069]")
		}
	}
	{
		/*
			70. 条件：若年柱、月柱、时柱任一一柱对于以下条件均满足
			  - 条件1：该柱神煞存在驿马
			  - 条件2：该柱神煞存在空亡
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "驿马") {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "空亡") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY070]")
		}
	}
	{
		/*
			71. 条件：若以下条件均满足
			  - 条件1：年柱、月柱或时柱中，至少有一柱天干为正财或偏财
			  - 条件2：年柱、月柱或时柱中，至少有一柱天干为偏印
			  - 条件3：年柱、月柱或时柱中，至少有一柱天干为食神
			  - 条件4：该命理食伤对应的五行为该命理的喜用神
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "食神") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["食伤"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY071]")
		}
	}
	{
		/*
			72. 条件：若年支与月支发生相冲，
		*/
		if func() bool {
			return slf.IsDizhiXiangchong(dizhi[0], dizhi[1])
		}() {
			keys = append(keys, "[ZY072]")
		}
	}
	{
		/*
			73. 条件：若年干与月干相同
		*/
		if func() bool {
			return tiangan[0] == tiangan[1]
		}() {
			keys = append(keys, "[ZY073]")
		}
	}
	{
		/*
			74. 条件：若以下条件满足任意一个
			  - 条件1：若年支为阳地支时，时支、日支、月支任意一个地支为年支下一个地支
			  - 条件2：若年支为阴地支时，时支、日支、月支任意一个地支为年支上一个地支
		*/
		if func() bool {
			if slf.TblDizhiYinyang[dizhi[0]] == "阳" && array.Has(dizhi[1:], slf.NextDizhi(dizhi[0])) {
				return true
			}
			if slf.TblDizhiYinyang[dizhi[0]] == "阴" && array.Has(dizhi[1:], slf.PrevDizhi(dizhi[0])) {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY074]")
		}
	}
	{
		/*
			75. 条件：若时支与年支相同
		*/
		if func() bool {
			return dizhi[0] == dizhi[3]
		}() {
			keys = append(keys, "[ZY075]")
		}
	}
	{
		/*
			76. 条件：若原局命理中年干、月干、时干的十神有两个或两个以上为偏印
		*/
		if func() bool {
			if array.Count(ssg.TgListByIdx(0, 1, 3), "偏印") >= 2 {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ZY076]")
		}
	}
	{
		/*
			77. 条件：若以下条件均满足
			  - 条件1：日支与年支相同
			  - 条件2：日支年支的藏干本气十神均为偏财
		*/
		if func() bool {
			if dizhi[0] != dizhi[2] {
				return false
			}
			if ssg.Bq[0] != "偏财" {
				return false
			}
			if ssg.Bq[2] != "偏财" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ZY077]")
		}
	}
	return keys
}
