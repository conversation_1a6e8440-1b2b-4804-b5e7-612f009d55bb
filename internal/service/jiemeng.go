package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type JiemengService interface {
	<PERSON><PERSON><PERSON>(ctx context.Context, req *v1.JiemengRequest) (*v1.JiemengResponseData, error)
}

func NewJiemengService(
	service *Service,
	enumsRepo repository.EnumsRepository,
) JiemengService {
	return &jiemengService{
		Service:   service,
		enumsRepo: enumsRepo,
	}
}

type jiemengService struct {
	enumsRepo repository.EnumsRepository
	*Service
}

func (slf *jiemengService) Jiemeng(ctx context.Context, req *v1.JiemengRequest) (*v1.JiemengResponseData, error) {
	return slf.enumsRepo.GetAllJiemeng(ctx, req)
}
