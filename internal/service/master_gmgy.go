package service

import (
	"fmt"
	"github.com/samber/lo"
	"strings"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"
)

func (slf *masterService) GongMingGuanYun(paipanAll *corona.GetAllResponse, genderNum int, comb23 func([]string, ...string) ([][]string, [][]string)) []string {
	var (
		keys    []string
		tiangan = paipanAll.Tiangan[:4] // 天干
		dizhi   = paipanAll.Dizhi[:4]   // 地支
		ganzhi  = []string{
			paipanAll.Tiangan[0] + paipanAll.Dizhi[0],
			paipanAll.Tiangan[1] + paipanAll.Dizhi[1],
			paipanAll.Tiangan[2] + paipanAll.Dizhi[2],
			paipanAll.Tiangan[3] + paipanAll.Dizhi[3],
		}
		rigan           = paipanAll.Tiangan[2]          // 日干
		rgwx            = slf.GetWuxingByTiangan(rigan) // 日干五行
		riyuan          = paipanAll.Riyuan
		xycjx           = strings.SplitN(paipanAll.Xiyongjichou, ",", 5)
		xiyong          = xycjx[:2]
		chouji          = xycjx[2:4]
		ssg             = corona.NewShishenGetter(paipanAll.Zhuxing[:4], paipanAll.BenqiShishen[:4], paipanAll.ZhongqiShishen[:4], paipanAll.YuqiShishen[:4])
		shishenPowerMap = paipanAll.ShishenPowerMap
	)
	//	年柱干十神、藏干十神，存在官杀
	if slf.HasGuanSha(ssg.ByIdx(0)) {
		keys = append(keys, "[GG001]")
	}
	// 年柱无官杀，月柱干十神、藏干十神， 存在官杀
	if !slf.HasGuanSha(ssg.ByIdx(0)) && slf.HasGuanSha(ssg.ByIdx(1)) {
		keys = append(keys, "[GG002]")
	}
	// 年柱、月柱无官杀，日支藏干十神，存在官杀
	if !slf.HasGuanSha(ssg.ByIdx(0, 1)) && slf.HasGuanSha(ssg.CgByIdx(2)) {
		keys = append(keys, "[GG003]")
	}
	// 年柱、月柱、日柱无官杀，时支干十神、藏干十神，存在官杀
	if !slf.HasGuanSha(ssg.ByIdx(0, 1, 2)) && slf.HasGuanSha(ssg.ByIdx(3)) {
		keys = append(keys, "[GG004]")
	}
	// 官杀对应的五行为喜用神
	if array.Has(xiyong, slf.GuanSha(rgwx)) {
		keys = append(keys, "[GG005]")
	}
	// 官杀对应的五行为仇忌神
	if array.Has(chouji, slf.GuanSha(rgwx)) {
		keys = append(keys, "[GG006]")
	}
	// 3
	keys = append(keys, func() string {
		return fmt.Sprintf("正官信息：天干%s。地支%s",
			lo.Ternary(ssg.NumMap4Tg["正官"] > 0, fmt.Sprintf("%d透，得时而旺", ssg.NumMap4Tg["正官"]), "不透"),
			lo.Ternary(ssg.NumMap4Cg["正官"] > 0, fmt.Sprintf("%d藏，得时而旺。暗藏入库。", ssg.NumMap4Cg["正官"]), "不藏"),
		)
	}())
	// 4
	keys = append(keys, func() string {
		return fmt.Sprintf("七杀信息：天干%s。地支%s",
			lo.Ternary(ssg.NumMap4Tg["七杀"] > 0, fmt.Sprintf("%d透，得时而旺", ssg.NumMap4Tg["七杀"]), "不透"),
			lo.Ternary(ssg.NumMap4Cg["七杀"] > 0, fmt.Sprintf("%d藏，得时而旺。暗藏入库。", ssg.NumMap4Cg["七杀"]), "不藏"),
		)
	}())
	// 5
	{
		/*
			年柱天干、月柱天干、时柱天干，若有任意一个为七杀
		*/
		if array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
			keys = append(keys, "[GG007]")
		}
	}
	// 6
	{
		/*
			年柱天干、月柱天干、时柱天干，若有任意一个为正官
		*/
		if array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
			keys = append(keys, "[GG008]")
		}
	}
	// 7
	{
		/*
		  - 条件1：年柱、月柱、时柱干十神中存在正官或偏官（七杀）
		  - 条件2：年柱、月柱、日柱、时柱藏干十神中存在正官或偏官（七杀）
		  - 条件3：官杀对应五行为命主的喜用神五行
		*/
		if slf.HasGuanSha(ssg.TgListByIdx(0, 1, 3)) &&
			slf.HasGuanSha(ssg.CgListByIdx(0, 1, 3)) &&
			array.Has(xiyong, slf.GuanSha(rgwx)) {
			keys = append(keys, "[GG009]")
		}
	}
	// 8
	{
		/*
		  - 条件1：能量在[9,40),
		    - 则显示：【GG010】财为官值原神，财星弱，高级职位难求。
		  - 条件2：能量在[40,90）
		    - 则显示：【GG011】财为官值原神，财星不旺，升迁较慢。
		  - 条件3：能量在[90，+∞）
		    - 则显示：【GG012】财为官值原神，财星健旺，利于升迁，仕途较顺利
		*/
		power := paipanAll.ShishenPowerMap["财才"]
		if power >= 9 && power < 40 {
			keys = append(keys, "[GG010]")
		} else if power >= 40 && power < 90 {
			keys = append(keys, "[GG011]")
		} else if power >= 90 {
			keys = append(keys, "[GG012]")
		}
	}
	// 9 若年柱天干为为七杀
	if ssg.Tg[0] == "七杀" {
		keys = append(keys, "[GG013]")
	}
	// 10
	{
		/*
		  - 条件1：年支为辰
		  - 条件2：月支为戌
		  - 条件3：日支为丑
		  - 条件4：时支为未
		*/
		if dizhi[0] == "辰" && dizhi[1] == "戌" && dizhi[2] == "丑" && dizhi[3] == "未" {
			keys = append(keys, "[GG014]")
		}
	}
	// 11
	{
		/*
		  - 条件1：存在一柱，干十神为正官
		  - 条件2：条件1中该柱自坐的值为{"长生", "沐浴", "冠带", "临官", "帝旺"}集合中的一个
		  - 条件3：原局四柱中无伤官
		*/
		var (
			idx = -1
		)
		for i, str := range ssg.Tg {
			if str == "正官" {
				idx = i
				break
			}
		}
		if idx >= 0 &&
			array.Has([]string{"长生", "沐浴", "冠带", "临官", "帝旺"}, paipanAll.GetZizuoList[idx]) &&
			!array.Has(ssg.Tg, "伤官") &&
			!array.Has(ssg.Bq, "伤官") &&
			!array.Has(ssg.Zq, "伤官") &&
			!array.Has(ssg.Yq, "伤官") {
			keys = append(keys, "[GG015]")
		}
	}
	// 12
	{
		/*
		  - 条件1：年干、月干、时干干十神存在一个正印
		  - 条件2：年干、月干、时干干十神不存在伤官
		  - 条件3：原局四柱干十神 或藏干十神中存在正官或七杀
		  - 条件4：年支、月支、时支、日支藏干十神，至少存在一个正印
		*/
		if array.Has(ssg.TgListByIdx(0, 1, 3), "正印") &&
			!array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") &&
			(array.Has(ssg.All(), "正官") || array.Has(ssg.All(), "七杀")) &&
			array.Has(ssg.CangGan(), "正印") {
			keys = append(keys, "[GG016]")
		}
	}
	// 13
	{
		/*
		  - 条件1：日干为丙
		  - 条件2：月柱干支为辛酉
		*/
		if rigan == "丙" && tiangan[1] == "辛" && dizhi[1] == "酉" {
			keys = append(keys, "[GG017]")
		}
	}
	// 14
	{
		/*
		  - 条件1：日干为辛
		  - 条件2：月柱干支为丙申
		*/
		if rigan == "辛" && tiangan[1] == "丙" && dizhi[1] == "申" {
			keys = append(keys, "[GG018]")
		}
	}
	// 15
	{
		/*
		  - 条件1：年柱、月柱、时柱任一一柱满足以下条件即可
		    - 天干十神为七杀
		    - 该柱神煞存在羊刃
		    - 该柱地支未发生相冲
		  - 条件2：年干、月干、时干十神存在食神
		  - 条件3：原局命例旺衰为 偏弱、身弱、平和、身强、偏强的一种。
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				other := array.Remove([]int{0, 1, 2, 3}, idx)
				isXiangchong := false
				for _, o := range other {
					if slf.IsDizhiXiangchong(dizhi[o], dizhi[idx]) || slf.IsDizhiXiangchong(dizhi[idx], dizhi[o]) {
						isXiangchong = true
						break
					}
				}
				if isXiangchong {
					return false
				}
			}
			return true
		}() &&
			array.Has(ssg.TgListByIdx(0, 1, 3), "食神") &&
			array.Has([]string{}, riyuan) {
			keys = append(keys, "[GG019]")
		}
	}
	// 16
	{
		/*
		  - 条件1：原局四柱中十神能量中，正印的能量小于50
		  - 条件2：原局四柱中师生能量中，七杀的能量大于90
		  - 条件3：原局命例中，喜用神为官杀对应的五行
		*/
		if paipanAll.ShishenPowerMap["正印"] < 50 &&
			paipanAll.ShishenPowerMap["七杀"] > 90 &&
			array.Has(xiyong[:2], paipanAll.ShishenWuxingMap["官杀"]) {
			keys = append(keys, "[GG020]")
		}
	}
	// 17
	{
		/*
		  - 条件1：原局四柱中十神能量，财才能量大于等于90
		  - 条件2：原句四柱中官杀能量小于等于50
		  - 条件3：原局年干、月干或时干的十神存在正印
		*/
		if paipanAll.ShishenPowerMap["财才"] >= 90 &&
			paipanAll.ShishenPowerMap["官杀"] <= 50 &&
			array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
			keys = append(keys, "[GG021]")
		}
	}
	// 18
	{
		/*
		  - 条件1：原局天干十神，仅存在一个正官
		  - 条件2：原局天干十神，不存在七杀
		  - 条件3：原局地支藏干十神，不存在正官、七杀
		  - 条件4：原局天干十神、藏干十神，不存在伤官
		*/
		if ssg.NumMap4Tg["正官"] == 1 && ssg.NumMap4Tg["七杀"] == 0 && (ssg.NumMap4Cg["正官"] == 0 && ssg.NumMap4Cg["七杀"] == 0) && ssg.NumMap4Tg["伤官"] == 0 {
			keys = append(keys, "[GG022]")
		}
	}
	// 19
	{
		// 原局命理中天干十神、藏干十神中，正官的数量大于等于4个
		if ssg.NumMap["正官"] >= 4 {
			keys = append(keys, "[GG023]")
		}
	}
	// 20
	{
		/*
		  - 条件1：年干十神为正官
		  - 条件2：年干对应五行为原局命理的喜用五行
		  - 条件3：年干、月干、时干十神不存在伤官
		*/
		if ssg.Tg[0] == "正官" && array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[0])) && !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
			keys = append(keys, "[GG024]")
		}
	}
	// 21
	{
		/*
		  - 条件1：年干十神为正官
		  - 条件2：年支藏干十神有正官
		  - 条件3：原局八字干十神及藏干十神无伤官
		*/
		if ssg.Tg[0] == "正官" && array.Has(ssg.CgByIdx(0), "正官") && ssg.NumMap["伤官"] == 0 {
			keys = append(keys, "[GG025]")
		}
	}
	// 22
	{
		/*
		  - 条件1：月干十神为正官
		  - 条件2：月干对应的五行为原局命理喜用神
		  - 条件3：原局八字命理中干十神、藏干十神无伤官
		  - 条件4：原句八字命理干十神、藏干十神中，除月干十神外，无其他正官或七杀
		*/
		if ssg.Tg[1] == "正官" && array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[1])) && ssg.NumMap["伤官"] == 0 &&
			!array.Exist(
				array.Merge(ssg.TgListByIdx(0, 2, 3), ssg.CangGan()),
				func(s string) bool {
					return s == "正官" || s == "七杀"
				}) {
			keys = append(keys, "[GG026]")
		}
	}
	// 23
	{
		/*
		  - 条件1：原局命理中命主旺衰为 身强、偏强或从强
		  - 条件2：年干、月干、日干的干十神存在偏官
		  - 条件3：干十神为偏官的天干，与原局四柱天干发生天干五和的
		*/
		if array.Has([]string{"身强", "偏强", "从强"}, riyuan) &&
			array.Has(ssg.TgListByIdx(0, 1, 2), "七杀") &&
			func() bool {
				for i, s := range ssg.Tg {
					if s != "七杀" {
						continue
					}
					gan1 := paipanAll.Tiangan[i]
					for _, idx := range array.Remove([]int{0, 1, 2, 3}, i) {
						gan2 := paipanAll.Tiangan[idx]
						_, ok := slf.IsTianganWuhe(gan1, gan2)
						if ok {
							return true
						}
					}
				}
				return false
			}() {
			keys = append(keys, "[GG027]")
		}
	}
	// 24
	{
		/*
			24. 若原局命理中存在一柱，满足以下条件
			  - 条件1：天干十神为偏官
			  - 条件2：该柱神煞存在空亡
			  - 条件3：该柱地支，未发生三合、相冲、半合、六合、三刑、相刑
		*/
		if func() bool {
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[i], "空亡") {
					continue
				}
				combos2, combos3 := comb23(array.Remove(dizhi, dizhi[i], 1), dizhi[i])
				isFound := false
				for _, arr := range combos2 {
					if isFound = slf.IsDizhiXiangchong(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiBanhe(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiLiuhe(arr[0], arr[1]); isFound {
						break
					}
					if isFound = slf.IsDizhiXiangxing(arr[0], arr[1]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				for _, arr := range combos3 {
					if _, isFound = slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if isFound = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG028]")
		}
	}
	{
		/*
			  - 条件1：天干十神为正印
			- 条件2：该柱的星运为冠带
				- 条件3：天干五行为原局命理的喜用五行
		*/
		if func() bool {
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] != "正印" {
					continue
				}
				if paipanAll.GetXingyunListLiuAll[i] != "冠带" {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[i])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG029]")
		}
	}
	{
		/*
		  - 条件1：天干十神为正印
		  - 条件2：该柱的星运为帝旺
		  - 条件3：天干五行为原局命理的喜用五行
		*/
		if func() bool {
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] != "正印" {
					continue
				}
				if paipanAll.GetXingyunListLiuAll[i] != "帝旺" {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[i])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG030]")
		}
	}
	{
		/*
		  - 条件1：天干十神为正印
		  - 条件2：地支十神存在正财
		  - 条件3：天干的五行为原局命理的喜用五行
		*/
		if func() bool {
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] != "正印" {
					continue
				}
				if ssg.Dz[i] == "正财" {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[i])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG031]")
		}
	}
	{
		/*
		  - 条件1：天干十神为偏印
		  - 条件2：地支藏干十神为正财
		  - 条件3：地支五行为原局命理的喜用五行
		  - 条件4：天干五行不为原局命理的仇忌五行
		*/
		if func() bool {
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] != "偏印" {
					continue
				}
				if !array.Has(ssg.CgByIdx(i), "正财") {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(dizhi[i])) {
					continue
				}
				if array.Has(chouji[2:], slf.GetWuxingByTiangan(tiangan[i])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG032]")
		}
	}
	{
		/*
		  - 条件1：地支存在三刑
		  - 条件2：年干、月干、时干存在比劫
		  - 条件3：原局命理天干十神、藏干十神中，比肩、劫财的数量之和超过3个
		  - 条件4：原局命理的旺衰为偏强、身强、从强
		*/
		if func() bool {
			for _, arr := range array.Combinations(dizhi, 3) {
				if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					return true
				}
			}
			return false
		}() &&
			array.Has(ssg.TgDzListByIdx(0, 1, 3), "比劫") &&
			ssg.NumMap["比肩"]+ssg.NumMap["劫财"] > 3 &&
			array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
			keys = append(keys, "[GG033]")
		}
	}
	{
		/*
		  - 条件1：年干、月干、日干的天干十神均为比肩
		  - 条件2：地支存在三会
		  - 条件3：三会合化的五行为日干五行的 印枭或者
		*/
		if func() bool {
			if ssg.Tg[0] != "比肩" || ssg.Tg[1] != "比肩" || ssg.Tg[2] != "比肩" {
				return false
			}
			combinations := array.Combinations(dizhi, 3)
			for _, arr := range combinations {
				sanhui, ok := slf.IsDizhiSanhui(arr[0], arr[1], arr[2])
				if !ok {
					continue
				}
				if sanhui == slf.YinXiao(rgwx) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG034]")
		}
	}
	{
		/*
		  - 条件1：年柱干十神、藏干十神存在 正官
		  - 条件2：月柱干十神、藏干十神存在 正官
		  - 条件3：日支藏干十神存在 正官
		  - 条件4：时柱干十神、藏干十神存在 正官
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(0), "正官") {
				return false
			}
			if !array.Has(ssg.ByIdx(1), "正官") {
				return false
			}
			if !array.Has(ssg.CgByIdx(2), "正官") {
				return false
			}
			if !array.Has(ssg.ByIdx(3), "正官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG035]")
		}
	}
	{
		/*
		  - 条件1：年柱干十神为正官
		  - 条件2：年柱藏干十神存在正官
		  - 条件3：年支对应的五行为生 日干对应的五行；或者，原局命理十神能量中，比劫的能量大于等于90
		*/
		if func() bool {
			if ssg.Tg[0] != "正官" {
				return false
			}
			if !array.Has(ssg.CgByIdx(0), "正官") {
				return false
			}
			if !slf.IsWuxingXiangsheng(slf.GetWuxingByDizhi(dizhi[0]), rgwx) &&
				shishenPowerMap["比劫"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG036]")
		}
	}
	{
		/*
		  - 条件1：月柱天干为正官
		  - 条件2：年柱天干、时柱天干的干十神至少存在一个七杀
		  - 条件3：原局命理中，天干十神、地支藏干十神，不存在正印、偏印
		  - 条件4：原局命理中，天干十神、地支藏干十神，不存在正财、偏财
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 3), "七杀") {
				return false
			}
			if array.Has(ssg.All(), "正印") || array.Has(ssg.All(), "偏印") {
				return false
			}
			if array.Has(ssg.All(), "正财") || array.Has(ssg.All(), "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG037]")
		}
	}
	{
		/*
		  - 条件1：月干十神为七杀
		  - 条件2：年干十神为正印
		  - 条件3：原局年支、月支、日支、时支的藏干十神存在正印或偏印
		  - 条件4：原句命理的旺衰不为从强。
		*/
		if func() bool {
			if ssg.Tg[1] != "七杀" {
				return false
			}
			if ssg.Tg[0] != "正印" {
				return false
			}
			if !array.Has(ssg.CangGan(), "正印") && !array.Has(ssg.CangGan(), "偏印") {
				return false
			}
			if riyuan == "从强" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG038]")
		}
	}
	{
		/*
		  - 条件1：年干十神为偏财
		  - 条件2：月干十神为正印
		*/
		if func() bool {
			if ssg.Tg[0] != "偏财" {
				return false
			}
			if ssg.Tg[1] != "正印" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG039]")
		}
	}
	{
		/*
		  - 条件1：日柱地支对应的十神为偏印
		  - 条件2：日柱地支与年柱、月柱或时柱地支发生六冲
		  - 条件3：日柱地支与年柱、月柱或时柱地支发生三刑或相刑
		  - 条件4：日柱地支对应五行不为仇忌神
		  - 条件5：性别为男
		*/
		if func() bool {
			if genderNum != 1 {
				return false
			}
			if ssg.Dz[3] != "偏印" {
				return false
			}
			var isXiangchong bool
			for _, s := range []string{dizhi[0], dizhi[1], dizhi[3]} {
				if slf.IsDizhiXiangchong(dizhi[3], s) {
					isXiangchong = true
					break
				}
			}
			if !isXiangchong {
				return false
			}
			var (
				isXiangxing bool
				isSanxing   bool
			)
			towComb, threeComb := comb23([]string{dizhi[0], dizhi[1], dizhi[3]}, dizhi[3])
			for _, arr := range towComb {
				if isXiangxing = slf.IsDizhiXiangxing(arr[0], arr[1]); isXiangxing {
					break
				}
			}
			for _, arr := range threeComb {
				if isSanxing = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isSanxing {
					break
				}
			}
			if !isXiangxing && !isSanxing {
				return false
			}
			if array.Has(chouji[2:], slf.GetWuxingByDizhi(dizhi[3])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG040]")
		}
	}
	{
		/*
		  - 条件1：日柱地支对应的十神为偏印
		  - 条件2：日柱地支与年柱、月柱或时柱地支发生六冲
		  - 条件3：日柱地支与年柱、月柱或时柱地支发生三刑或相刑
		  - 条件4：日柱地支对应五行不为仇忌神
		  - 条件5：性别为女
		*/
		if func() bool {
			if genderNum != 2 {
				return false
			}
			if ssg.Dz[3] != "偏印" {
				return false
			}
			var isXiangchong bool
			for _, s := range []string{dizhi[0], dizhi[1], dizhi[3]} {
				if slf.IsDizhiXiangchong(dizhi[3], s) {
					isXiangchong = true
					break
				}
			}
			if !isXiangchong {
				return false
			}
			var (
				isXiangxing bool
				isSanxing   bool
			)
			towComb, threeComb := comb23([]string{dizhi[0], dizhi[1], dizhi[3]}, dizhi[3])
			for _, arr := range towComb {
				if isXiangxing = slf.IsDizhiXiangxing(arr[0], arr[1]); isXiangxing {
					break
				}
			}
			for _, arr := range threeComb {
				if isSanxing = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isSanxing {
					break
				}
			}
			if !isXiangxing && !isSanxing {
				return false
			}
			if array.Has(chouji[2:], slf.GetWuxingByDizhi(dizhi[3])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG041]")
		}
	}
	{
		/*
		  - 条件1：月干十神为食神
		  - 条件2：时柱天干十神为正官
		  - 条件3：年柱天干不为偏印
		  - 条件4：时柱天干对应五行为原局命理的喜用神
		*/
		if func() bool {
			if ssg.Tg[1] != "食神" {
				return false
			}
			if ssg.Tg[3] != "正官" {
				return false
			}
			if ssg.Tg[0] == "偏印" {
				return false
			}
			if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[3])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG042]")
		}
	}
	{
		/*
		  - 条件1：原局命理中，年柱干天干十神七杀
		  - 条件2：与原局命理中，月柱天干十神为食神或伤官
		  - 条件3：原局命理中，天干十神、地支藏干十神不存在正官
		  - 条件4：原局命理中，天干十神不存在偏印
		*/
		if func() bool {
			if ssg.Tg[0] != "七杀" {
				return false
			}
			if ssg.Tg[1] != "食神" && ssg.Tg[1] != "伤官" {
				return false
			}
			if array.Has(ssg.All(), "正官") {
				return false
			}
			if array.Has(ssg.Tg, "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG043]")
		}
	}
	{
		/*
		  - 条件1：原局命理中，月柱干天干十神七杀
		  - 条件2：与原局命理中，日柱天干十神为食神或伤官
		  - 条件3：原局命理中，天干十神、地支藏干十神不存在正官
		  - 条件4：原局命理中，天干十神不存在偏印
		*/
		if func() bool {
			if ssg.Tg[1] != "七杀" {
				return false
			}
			if ssg.Tg[2] != "食神" && ssg.Tg[2] != "伤官" {
				return false
			}
			if array.Has(ssg.All(), "正官") {
				return false
			}
			if array.Has(ssg.Tg, "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG044]")
		}
	}
	{
		/*
		  - 条件1：原局命理的命主旺衰为从强、身弱或偏弱
		  - 条件2：原局命理中，年干、月干、时干十神同时存在为七杀、正官
		  - 条件3：原局命理中，天干十神、地支藏干十神不存在正印、偏印
		*/
		if func() bool {
			if !array.Has([]string{"从强", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if array.Has(ssg.All(), "正印") || array.Has(ssg.All(), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG045]")
		}
	}
	{
		/*
			42. 若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：地支十神为正官
			  - 条件3：地支对应五行为原局命理的喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] == "正印" &&
					ssg.Dz[idx] == "正官" &&
					array.Has(xiyong[:2], slf.GetWuxingByDizhi(dizhi[idx])) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG046]")
		}
	}
	{
		/*
		  43. 原局命理中，年柱、月柱、时柱任意一柱满足以下条件
		  - 条件1：天干十神为正印
		  - 条件2：地支藏干十神存在正印
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] == "正印" &&
					array.Has(ssg.CgByIdx(idx), "正印") {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG047]")
		}
	}
	{
		/*
			44. 原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：地支藏干十神存在食神
			  - 条件3：天干五行对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] == "正印" &&
					array.Has(ssg.CgByIdx(idx), "食神") &&
					array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[idx])) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG048]")
		}
	}
	{
		/*
				45. 条件：若以下条件均满足
			        - 条件1：年干、月干、时柱十神中，存在偏印
			        - 条件2：原局命理中，干十神、地支十神中偏印的数量大于等于3
			        - 条件3：年干、月干、时干，无正财、偏财
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if ssg.NumMap["偏印"] < 3 {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "正财") || array.Has(ssg.TgListByIdx(0, 1, 3), "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG049]")
		}
	}
	{
		/*
			46. 条件：若以下条件均满足
			  - 条件1：年柱天干十神为正印
			  - 条件2：年柱天干对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			if ssg.Tg[0] != "正印" {
				return false
			}
			if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG050]")
		}
	}
	{
		/*
			47. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正印
			  - 条件2：原局命理干十神、藏干十神中不存在正财、偏财
			  - 条件3：月柱天干对应的五行为原局命理的喜用
		*/
		if func() bool {
			if ssg.Tg[1] != "正印" {
				return false
			}
			if array.Has(ssg.All(), "正财") || array.Has(ssg.All(), "偏财") {
				return false
			}
			if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[1])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG051]")
		}
	}
	{
		/*
			48. 条件：若以下条件均满足
			  - 条件1：印枭对应的五行为原局命理的喜用神
			  - 条件2：原局命理中干十神、藏干十神中至少存在正官、七杀、正财、偏财中的任何一个
		*/
		if func() bool {
			if !array.Has(xiyong[:2], slf.YinXiao(rgwx)) {
				return false
			}
			if !array.HasAny(ssg.All(), "正官", "七杀", "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG052]")
		}
	}
	{
		/*
			49. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神之一为偏印
			  - 条件2：年柱、月柱、时柱天干十神之一为正官
			  - 条件3：年柱、月柱、时柱天干十神之一为七杀
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG053]")
		}
	}
	{
		/*
			50. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为食神
			  - 条件2：地支藏干十神存在七杀
			  - 条件3：天干对应五行为原局命理的喜用五行
			  - 条件4：原局四柱干十神、藏干十神不存在偏印
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] == "食神" &&
					array.Has(ssg.CgByIdx(idx), "七杀") &&
					array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[idx])) &&
					!array.Has(ssg.All(), "偏印") {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG054]")
		}
	}
	{
		/*
			51. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官
			  - 条件2：同柱神煞存在空亡
			  - 条件3：地支无三合、无冲、无半合、无六合、无三刑、无相刑、无三会的情况
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				if !array.Has(ssg.ByIdx(idx), "空亡") {
					continue
				}
				comb2, comb3 := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				isFound := false
				for _, arr := range comb2 {
					if isFound = slf.IsDizhiXiangchong(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiBanhe(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiLiuhe(arr[0], arr[1]); isFound {
						break
					}
					if isFound = slf.IsDizhiXiangxing(arr[0], arr[1]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				for _, arr := range comb3 {
					if isFound = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhui(arr[0], arr[1], arr[2]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG055]")
		}
	}
	{
		/*
			52. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在食神
			  - 条件2：年干、月干、时干十神无偏印
			  - 条件3：年干、月干、时干十神有七杀
			  - 条件4：年柱、月柱、日柱、时柱神煞存在羊刃
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "食神") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if !array.HasAny(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG056]")
		}
	}
	{
		/*
			53. 条件：若以下条件均满足
			  - 条件1：月干十神为食神
			  - 条件2：月支地支藏干十神存在正官
			  - 条件3：克制日干对应五行的五行（官杀五行）为原局命理的喜用五行
			  - 条件4：原局命理不存在偏印
		*/
		if func() bool {
			if ssg.Tg[1] != "食神" {
				return false
			}
			if !array.Has(ssg.CgByIdx(1), "正官") {
				return false
			}
			if !array.Has(xiyong[:2], slf.GuanSha(rgwx)) {
				return false
			}
			if array.Has(ssg.All(), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG057]")
		}
	}
	{
		/*
			54. 条件：若以下条件均满足
			  - 条件1：原局命理的命主旺衰为从弱、身弱、偏弱
			  - 条件2：原局命理十神能量中，印枭的力量小于40
			  - 条件3：原局命理干十神、藏干十神中正财及偏财的数量之和大于等于三个
			  - 条件4：年干、月干、时干十神存在正印或偏印
		*/
		if func() bool {
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["印枭"] >= 40 {
				return false
			}
			if ssg.NumMap["正财"]+ssg.NumMap["偏财"] < 3 {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印") && !array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG058]")
		}
	}
	{

		/*
			55. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为偏财
			  - 条件2：同柱神煞存在空亡
			  - 条件3：地支无三合、无冲、无半合、无六合、无三刑、无相刑、无三会的情况
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "偏财" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "空亡") {
					continue
				}
				comb2, comb3 := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				isFound := false
				for _, arr := range comb2 {
					if isFound = slf.IsDizhiXiangchong(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiBanhe(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiLiuhe(arr[0], arr[1]); isFound {
						break
					}
					if isFound = slf.IsDizhiXiangxing(arr[0], arr[1]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				for _, arr := range comb3 {
					if isFound = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhui(arr[0], arr[1], arr[2]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG059]")
		}
	}
	{
		/*
			56. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：同柱神煞存在空亡
			  - 条件3：地支无三合、无冲、无半合、无六合、无三刑、无相刑、无三会的情况
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "空亡") {
					continue
				}
				comb2, comb3 := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				isFound := false
				for _, arr := range comb2 {
					if isFound = slf.IsDizhiXiangchong(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiBanhe(arr[0], arr[1]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiLiuhe(arr[0], arr[1]); isFound {
						break
					}
					if isFound = slf.IsDizhiXiangxing(arr[0], arr[1]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				for _, arr := range comb3 {
					if isFound = slf.IsDizhiSanxing(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); isFound {
						break
					}
					if _, isFound = slf.IsDizhiSanhui(arr[0], arr[1], arr[2]); isFound {
						break
					}
				}
				if isFound {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG060]")
		}
	}
	{
		/*
			57. 条件：若以下条件均满足
			  - 条件1：原局命理中命主的旺衰为身强、偏强、从强
			  - 条件2：年干、月干、时干十神，不存在正官
			  - 条件3：年干、月干、时干十神、不存在七杀
			  - 条件4：年支、月支、日支、时支藏干十神存在七杀
		*/
		if func() bool {
			if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if !array.HasAny(ssg.CangGan(), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG061]")
		}
	}
	{
		/*
			58. 条件：若以下条件均满足
			  - 条件1：天干十神为伤官
			  - 条件2：同柱神煞存在羊刃
			  - 条件3：年干、月干、时干天干十神存在印枭，或不存在财星
		*/
		if func() bool {
			idx := ssg.TgSearch("伤官")
			if idx < 0 {
				return false
			}
			if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "印枭") &&
				(array.Has(ssg.TgListByIdx(0, 1, 3), "正财") || array.Has(ssg.TgListByIdx(0, 1, 3), "偏财")) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG062]")
		}
	}
	{
		/*
			59. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神，存在劫财
			  - 条件2：条件1中劫财所在柱的神煞存在羊刃
			  - 条件3：年干、月干、时干十神，存在正官或七杀
		*/
		if func() bool {
			idx := ssg.TgSearch("劫财", 0, 1, 3)
			if idx < 0 {
				return false
			}
			if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") && !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG063]")
		}
	}
	{
		/*
			60. 条件：若以下条件均满足
			  - 条件1：年干、月干十神同时存在正官、正印
			  - 条件3：时干十神不为伤官
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1), "正官", "正印") {
				return false
			}
			if ssg.Tg[3] == "伤官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG064]")
		}
	}
	{
		/*
			61. 条件：若以下条件均满足
			  - 条件1：月干十神为正官或者正印
			  - 条件2：年干、时干十神不为伤官
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" && ssg.Tg[1] != "正印" {
				return false
			}
			if ssg.Tg[0] == "伤官" || ssg.Tg[3] == "伤官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG065]")
		}
	}
	{
		/*
			62. 条件：若以下条件均满足
			  - 条件1：月干十神为七杀
			  - 条件2：时干十神为食神
			  - 条件3：原局命理命主旺衰为偏强、身强或从强
			  - 条件4：年干十神不为伤官
		*/
		if func() bool {
			if ssg.Tg[1] != "七杀" {
				return false
			}
			if ssg.Tg[3] != "食神" {
				return false
			}
			if !array.Has([]string{"偏强", "身强", "从强"}, riyuan) {
				return false
			}
			if ssg.Tg[0] == "伤官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG066]")
		}
	}
	{
		/*
			63. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：存在金神神煞
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "金神") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG067]")
		}
	}
	{
		/*
			64. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：神煞中存在华盖
			  - 条件2：天干十神为正印
			  - 条件3：地支对应五行为原局命理的喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "华盖") {
					continue
				}
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG068]")
		}
	}
	{
		/*
			65. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干天干十神存在食神
			  - 条件2：条件1中食神同柱神煞存在羊刃
			  - 条件3：日支藏干十神为正官
			  - 条件4：年干、月干、时干不存在偏印
			  - 条件5：日支对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			idx := ssg.TgSearch("食神", 0, 1, 3)
			if idx < 0 {
				return false
			}
			if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
				return false
			}
			if !array.Has(ssg.CgByIdx(2), "正官") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if !array.Has(xiyong[:2], slf.GetWuxingByDizhi(dizhi[2])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG069]")
		}
	}
	{
		/*
			66. 条件：若以下条件均满足
			  - 条件1：原局命理中，干十神、藏干十神中的正印、偏印数量之后大于等于3个
			  - 条件2：原局命理中，干十神、藏干十神中不存在七杀
		*/
		if func() bool {
			if ssg.NumMap["正印"]+ssg.NumMap["偏印"] < 3 {
				return false
			}
			if array.Has(ssg.All(), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG070]")
		}
	}
	{
		/*
			67. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞存在禄神
			  - 条件2：该柱神煞存在驿马
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "禄神", "驿马") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG071]")
		}
	}
	{
		/*
			68. 条件：若以下条件均满足
			  - 条件1：天干十神为正印
			  - 条件2：该柱神煞存在天乙贵人
			  - 条件3：天干对应五行为原局命理喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "天乙贵人") {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByTiangan(tiangan[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG072]")
		}
	}
	{
		/*
			69. 条件：若以下条件均满足
			  - 条件1：原局命理中，存在天德贵人或月德贵人
			  - 条件2：年干、月干、时干十神存在正财或偏财
			  - 条件3：年干、月干、时干十神不存正官
			  - 条件4：年支、月支、日支、时支藏干十神存在
		*/
		if func() bool {
			if !array.HasAny(paipanAll.Shensha4[0], "天德贵人", "月德贵人") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正财") && !array.Has(ssg.TgListByIdx(0, 1, 3), "偏财") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if !array.HasAny(ssg.CangGan(), "正官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG073]")
		}
	}
	{
		/*
			70. 条件：若以下条件均满足
			  - 条件1：日主神煞存在拱禄
			  - 条件2：根据以下规则判断禄未被填实：
			    - 若日干为癸，年支、月支均不为子
			    - 若日干为丁，年支、月支均不为午
			    - 若日支为午，年支、月支均不为巳
			  - 条件3：日柱神煞不存在空亡
			  - 条件4：时柱神煞不存在空亡
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[2], "拱禄") {
				return false
			}
			if (tiangan[2] == "癸" && (dizhi[0] == "子" || dizhi[1] == "子")) ||
				(tiangan[2] == "丁" && (dizhi[0] == "午" || dizhi[1] == "午")) ||
				(dizhi[2] == "午" && (dizhi[0] == "巳" || dizhi[1] == "巳")) {
				return false
			}
			if array.Has(paipanAll.Shensha4[2], "空亡") {
				return false
			}
			if array.Has(paipanAll.Shensha4[3], "空亡") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG074]")
		}
	}
	{
		/*
			71. 条件：若以下条件均满足
			  - 条件1：该柱神煞存在将星
			  - 条件2：该柱神煞存在亡神
			  - 条件3：该柱地支对应的五行为原局命理的命主喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "将星", "亡神") {
					continue
				}
				if !array.Has(xiyong[:2], slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG075]")
		}
	}
	{
		/*
			72. 条件：若以下条件均满足
			  - 条件1：三奇贵人，判断以下两条任一满足即可
			    - 年、月、日干依次为以下三个的任意一组即可：
			      - 甲、戊、庚
			      - 乙、丙、丁
			      - 壬、癸、辛
			    - 月、日、时干依次为以下三个的任意一组即可：
			      - 甲、戊、庚
			      - 乙、丙、丁
			      - 壬、癸、辛
			  - 条件2：原局地支存在以下任意一组地支
			    - 申、子、辰
			    - 亥、卯、未
			    - 寅、午、戌
			    - 巳、酉、丑
		*/
		if func() bool {
			isFound1 := false
			for _, v := range [][]string{
				{"甲", "戊", "庚"},
				{"乙", "丙", "丁"},
				{"壬", "癸", "辛"},
			} {
				if tiangan[0] == v[0] && tiangan[1] == v[1] && tiangan[2] == v[2] {
					isFound1 = true
					break
				}
			}
			isFound2 := false
			for _, v := range [][]string{
				{"甲", "戊", "庚"},
				{"乙", "丙", "丁"},
				{"壬", "癸", "辛"},
			} {
				if tiangan[1] == v[0] && tiangan[2] == v[1] && tiangan[3] == v[2] {
					isFound2 = true
					break
				}
			}
			isFound3 := false
			for _, v := range [][]string{
				{"申", "子", "辰"},
				{"亥", "卯", "未"},
				{"寅", "午", "戌"},
				{"巳", "酉", "丑"},
			} {
				if array.Has(dizhi, v...) {
					isFound3 = true
					break
				}
			}
			return (isFound1 || isFound2) && isFound3
		}() {
			keys = append(keys, "[GG076]")
		}
	}
	{
		/*
			73. 条件：若以下条件均满足
			  - 条件1：年干、月干、日干干十神存在伤官
			  - 条件2：年柱、月柱、日主、时柱神煞存在德秀
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 2), "伤官") {
				return false
			}
			if !array.Has(array.Merge(paipanAll.Shensha4...), "德秀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG077]")
		}
	}
	{
		/*
			74. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为食神
			  - 条件2：时柱天干十神为正官
			  - 条件3：年柱食神无偏印
		*/
		if func() bool {
			if ssg.Tg[1] != "食神" {
				return false
			}
			if ssg.Tg[3] != "正官" {
				return false
			}
			if array.Has(ssg.TgListByIdx(0), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG078]")
		}
	}
	{
		/*
			75. 条件：若以下条件均满足
			  - 条件1：原局命理四柱神煞不存在羊刃
			  - 条件2：年柱、月柱、时柱干支同时存在正印、正官，,且其中一个在月干
		*/
		if func() bool {
			if array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印", "正官") {
				return false
			}
			if ssg.Tg[1] != "正印" && ssg.Tg[1] != "正官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG079]")
		}
	}
	{
		/*
			76. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、藏干十神不存在偏印
			  - 条件2：原局四柱十神能量中，官杀能量大于等于90
			  - 条件3：年柱、月柱时柱干十神中同时存在食神、七杀，,且其中一个在月干
			  - 条件4：原局日主性别为男
		*/
		if func() bool {
			if array.Has(ssg.All(), "偏印") {
				return false
			}
			if shishenPowerMap["官杀"] < 90 {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "食神", "七杀") {
				return false
			}
			if ssg.Tg[1] != "食神" && ssg.Tg[1] != "七杀" {
				return false
			}
			if genderNum != 1 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG080]")
		}
	}
	{
		/*
			77. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞存在羊刃
			  - 条件2：原局年干、月干、时干十神同时存在正官、七杀,且其中一个在月干
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "七杀") {
				return false
			}
			if ssg.Tg[1] != "正官" && ssg.Tg[1] != "七杀" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG081]")
		}
	}
	{
		/*
			78. 条件：若以下条件均满足
			  - 条件1：原局年干、月干、时干十神有两个以上正官
			  - 条件2：月干十神为正官
			  - 条件3：原局命理中，官杀的能量大于等于90
		*/
		if func() bool {
			if array.Count(array.Merge(ssg.Tg[:2], ssg.Tg[3:]), "正官") < 2 {
				return false
			}
			if ssg.Tg[1] != "正官" {
				return false
			}
			if shishenPowerMap["官杀"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG082]")
		}
	}
	{
		/*
			79. 条件：若原局命理中地支为 子午卯酉（不限制顺序）
		*/
		if array.Has(dizhi, "子", "午", "卯", "酉") {
			keys = append(keys, "[GG083]")
		}
	}
	{
		/*
			80. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞存在羊刃
			  - 条件2：日柱、时柱神煞存在金神
			  - 条件3：原局年柱、月柱、日柱、时柱地支，不存在子、亥两个地支
			  - 条件4：原局地支发生三合、半三合，合化结果为火
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if !array.Has(array.Merge(paipanAll.Shensha4[2], paipanAll.Shensha4[3]), "金神") {
				return false
			}
			if array.Has(dizhi, "子", "亥") {
				return false
			}
			twoCombs, threeCombs := comb23(dizhi)
			isFound1 := false
			for _, comb := range threeCombs {
				sanhe, ok := slf.IsDizhiSanhe(comb[0], comb[1], comb[2])
				if !ok {
					continue
				}
				if sanhe != "火" {
					continue
				}
				isFound1 = true
				break
			}
			isFound2 := false
			for _, comb := range twoCombs {
				sanhe, ok := slf.IsDizhiBanhe(comb[0], comb[1])
				if !ok {
					continue
				}
				if sanhe != "火" {
					continue
				}
				isFound2 = true
				break
			}
			if !isFound1 && !isFound2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG084]")
		}
	}
	{
		/*
			81. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神均不为伤官
			  - 条件2：年柱、月柱、日柱、时柱干十神、藏干十神仅有且有一个正官
			  - 条件3：年柱、月柱、日柱、时柱干十神、藏干十神均无七杀
		*/
		if func() bool {
			if array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			if ssg.NumMap["正官"] != 1 {
				return false
			}
			if ssg.NumMap["七杀"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG085]")
		}
	}
	{
		/*
			82. 条件：若以下条件均满足
			  - 条件1：月柱神煞存在天乙贵人
			  - 条件2：日柱神煞存在天乙贵人
			  - 条件3：时柱天干或地址的五行为原局命理命主的仇忌神
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[1], "天乙贵人") {
				return false
			}
			if !array.Has(paipanAll.Shensha4[2], "天乙贵人") {
				return false
			}
			if array.Has(chouji, slf.GetWuxingByTiangan(tiangan[3])) || array.Has(chouji, slf.GetWuxingByDizhi(dizhi[3])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG086]")
		}
	}
	{
		/*
			83. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、藏干十神不存在正官
			  - 条件2：原局四柱干十神、藏干十神不存在七杀
			  - 条件3：原局四柱干十神、藏干十神不存在正财
			  - 条件4：原局四柱干十神、藏干十神不存在偏财
		*/
		if func() bool {
			if ssg.NumMap["正官"] > 0 {
				return false
			}
			if ssg.NumMap["七杀"] > 0 {
				return false
			}
			if ssg.NumMap["正财"] > 0 {
				return false
			}
			if ssg.NumMap["偏财"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG087]")
		}
	}
	{
		/*
			84. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官
			  - 条件2：地支藏干十神存在七杀
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				if !array.Has(ssg.CgByIdx(idx), "七杀") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG088]")
		}
	}
	{
		/*
			85. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官或七杀
			  - 条件2：地支发生六冲
			  - 条件3：地支发生三刑或相刑
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" && ssg.Tg[idx] != "七杀" {
					continue
				}
				twoCombs, threeCombs := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				isFound1 := false
				for _, comb := range threeCombs {
					if slf.IsDizhiSanxing(comb[0], comb[1], comb[2]) {
						isFound1 = true
						break
					}
				}
				isFound2 := false
				for _, comb := range twoCombs {
					if slf.IsDizhiXiangxing(comb[0], comb[1]) {
						isFound2 = true
						break
					}
				}
				isFound3 := false
				for _, comb := range twoCombs {
					if slf.IsDizhiXiangchong(comb[0], comb[1]) {
						isFound3 = true
						break
					}
				}
				if (!isFound1 && !isFound2) || !isFound3 {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG089]")
		}
	}
	{
		/*
			86. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干存在正官
			  - 条件2：日柱干支为壬辰
			  - 条件3：时柱天干为壬
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if tiangan[2] != "壬" || dizhi[2] != "辰" {
				return false
			}
			if ssg.Tg[3] != "壬" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG090]")
		}
	}
	{
		/*
			87. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干存在正官
			  - 条件2：原局四柱干十神、藏干十神中正官数量大于等于4
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if ssg.NumMap["正官"] < 4 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG091]")
		}
	}
	{
		/*
			88. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干存在伤官
			  - 条件2：原局命理十神能量中，官杀的能量小于等于15
			  - 条件3：原句命理中食伤的能量大于等于90
			  - 条件4：原局四柱干十神、藏干十神中存在正官与偏官的数量
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			if shishenPowerMap["官杀"] > 15 {
				return false
			}
			if shishenPowerMap["食伤"] < 90 {
				return false
			}
			if ssg.NumMap["正官"]+ssg.NumMap["七杀"] == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG092]")
		}
	}
	{
		/*
			89. 条件：若以下条件均满足
			  - 条件1：原局命理中官杀对应的五行为仇忌五行
			  - 条件2：存在一柱干十神、藏干十神均存在七杀（即一柱有两个七杀）
			  - 条件3：条件2中的柱相邻的一柱中，干十神、藏干十神存在七杀
		*/
		if func() bool {
			if !array.Has(chouji, slf.GuanSha(rgwx)) {
				return false
			}
			idx := -1
			for i := 0; i < 4; i++ {
				if ssg.NumMap4TgList[i]["七杀"] >= 1 && ssg.NumMap4CgList[i]["七杀"] >= 1 {
					idx = i
					break
				}
			}
			if idx == -1 {
				return false
			}
			others := []int{idx - 1, idx + 1}
			others = array.Filter(others, func(i int) bool {
				return i >= 0 && i < 4
			})
			for _, other := range others {
				if ssg.NumMapList[other]["七杀"] == 0 {
					return false
				}
			}
			return true
		}() {
			keys = append(keys, "[GG093]")
		}
	}
	{
		/*
			90. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神不存在伤官
			  - 条件2：存在一柱，满足以下条件
			    - 天干十神为正官
			    - 该地支未在原局四柱中发生六冲
			    - 该柱神煞不存在空亡
			  - 条件3：存在另一柱，满足以下条件
			    - 天干十神为正印
			    - 该地支未在原局四柱中发生六冲
			    - 该柱神煞不存在空亡
		*/
		if func() bool {
			if array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			idx1 := -1
			for i := 0; i < 4; i++ {
				if ssg.Tg[i] == "正官" {
					continue
				}
				twoCombs, _ := comb23(array.Remove(dizhi, dizhi[i], 1), dizhi[i])
				isFound1 := false
				for _, comb := range twoCombs {
					if slf.IsDizhiXiangchong(comb[0], comb[1]) {
						isFound1 = true
						break
					}
				}
				if isFound1 {
					continue
				}
				if array.Has(paipanAll.Shensha4[i], "空亡") {
					continue
				}
				idx1 = i
				break
			}
			if idx1 == -1 {
				return false
			}
			idx2 := -1
			for _, i := range array.Remove([]int{0, 1, 2, 3}, idx1) {
				if ssg.Tg[i] == "正印" {
					continue
				}
				twoCombs, _ := comb23(array.Remove(dizhi, dizhi[i], 1), dizhi[i])
				isFound1 := false
				for _, comb := range twoCombs {
					if slf.IsDizhiXiangchong(comb[0], comb[1]) {
						isFound1 = true
						break
					}
				}
				if isFound1 {
					continue
				}
				if array.Has(paipanAll.Shensha4[i], "空亡") {
					continue
				}
				idx2 = i
				break
			}
			if idx2 == -1 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG094]")
		}
	}
	{
		/*
			91. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞存在羊刃
			  - 条件2：原局四柱地支发生三刑
			  - 条件3：原局四柱地支发生相刑
			  - 条件4：原局四柱神煞存在魁罡
			  - 条件5：原局命理命主的旺衰为从弱、身弱、偏弱
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if !array.Has(array.Merge(paipanAll.Shensha4...), "魁罡") {
				return false
			}
			twoCombs, threeCombs := comb23(dizhi)
			isFound1 := false
			for _, comb := range threeCombs {
				if slf.IsDizhiSanxing(comb[0], comb[1], comb[2]) {
					isFound1 = true
					break
				}
			}
			if !isFound1 {
				return false
			}
			isFound2 := false
			for _, comb := range twoCombs {
				if slf.IsDizhiXiangxing(comb[0], comb[1]) {
					isFound2 = true
					break
				}
			}
			if !isFound2 {
				return false
			}
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG095]")
		}
	}
	{
		/*
			92. 条件：若以下任意一条件满足
			  - 条件1：日柱神煞存在禄神，且时柱十神存在驿马
			  - 条件2：日柱神煞存在驿马，且时柱十神存在禄神
			  - 条件3：日柱、时柱神煞均存在驿马
		*/
		if func() bool {
			if array.Has(paipanAll.Shensha4[2], "禄神") && array.Has(paipanAll.Shensha4[3], "驿马") {
				return true
			}
			if array.Has(paipanAll.Shensha4[2], "驿马") && array.Has(paipanAll.Shensha4[3], "禄神") {
				return true
			}
			if array.Has(paipanAll.Shensha4[2], "驿马") && array.Has(paipanAll.Shensha4[3], "驿马") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG096]")
		}
	}
	{
		/*
			93. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、藏干十神至少有一个伤官
			  - 条件2：原局四柱干十神、藏干十神不存在正官或七杀
			  - 条件3：原局四柱干十神、藏干十神存在财才或印枭
		*/
		if func() bool {
			if ssg.NumMap["伤官"] < 1 {
				return false
			}
			if ssg.NumMap["正官"] > 0 || ssg.NumMap["七杀"] > 0 {
				return false
			}
			if ssg.NumMap["财才"] == 0 && ssg.NumMap["印枭"] == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG097]")
		}
	}
	{
		/*
			94. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官或七杀
			  - 条件2：该柱神煞存在空亡
			  - 条件3：该柱地支未在原局四柱中发生六冲
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" && ssg.Tg[idx] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "空亡") {
					continue
				}
				twoCombs, _ := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				isFound1 := false
				for _, comb := range twoCombs {
					if slf.IsDizhiXiangchong(comb[0], comb[1]) {
						isFound1 = true
						break
					}
				}
				if isFound1 {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG098]")
		}
	}
	{
		/*
			95. 条件：若以下条件均满足
			  - 条件1：日干为壬或癸
			  - 条件2：以下条件满足几个即可
			    - 原局四柱地支存在：申、子、辰
			    - 原句四柱地支存在: 亥、子、丑
			  - 条件3：四柱天干、地支 不存在戊、己、未、戌
		*/
		if func() bool {
			if tiangan[2] != "壬" && tiangan[2] != "癸" {
				return false
			}
			if !array.Has(dizhi, "申", "子", "辰") && !array.Has(dizhi, "亥", "子", "丑") {
				return false
			}
			if array.HasAny(array.Merge(tiangan, dizhi), "戊", "己", "未", "戌") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG099]")
		}
	}
	{
		/*
			96. 条件：若以下条件均满足
			  - 条件1：日干为庚或辛
			  - 条件2：原局四柱地支存在 巳、酉和丑
		*/
		if func() bool {
			if tiangan[2] != "庚" && tiangan[2] != "辛" {
				return false
			}
			if !array.Has(dizhi, "巳", "酉", "丑") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG100]")
		}
	}
	{
		/*
			97. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官
			  - 条件2：该柱神煞存在桃花
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "桃花") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG101]")
		}
	}
	{
		/*
			98. 条件：若若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在七杀
			  - 条件2：日柱神煞存在魁罡
			  - 条件3：日干地支与原局四柱发生六冲
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if !array.Has(paipanAll.Shensha4[2], "魁罡") {
				return false
			}
			twoCombs, _ := comb23(array.Remove(dizhi, dizhi[2], 1), dizhi[2])
			isFound1 := false
			for _, comb := range twoCombs {
				if slf.IsDizhiXiangchong(comb[0], comb[1]) {
					isFound1 = true
					break
				}
			}
			if !isFound1 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG102]")
		}
	}
	{
		/*
			99. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞中，存在天乙、天德或月德
			  - 条件2：原局四柱地支中，发生三刑，即存在以下组合之一
			    - 寅、巳、申
			    - 丑、戌、未
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "天乙", "天德", "月德") {
				return false
			}
			if !array.Has(dizhi, "寅", "巳", "申") && !array.Has(dizhi, "丑", "戌", "未") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG103]")
		}
	}
	{
		/*
			100. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正印
			  - 条件2：日干的正印对应的五行，为原局命理的喜用五行
		*/
		if func() bool {
			if ssg.Tg[1] != "正印" {
				return false
			}
			if !array.Has(xiyong, slf.YinXiao(slf.GetWuxingByTiangan(tiangan[2]))) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG104]")
		}
	}
	{
		/*
			101. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干同时存在七杀和十神
			  - 条件2：四柱干十神、藏干十神没有偏印
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀", "十神") {
				return false
			}
			if ssg.NumMap["偏印"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG105]")
		}
	}
	{
		/*
			102. 条件：若以下任意一条满足
			  - 条件1：年干十神为正财或偏财，月干十神为正印
			  - 条件2：月干十神为正财或偏财，时干十神为正印
		*/
		if func() bool {
			if ssg.Tg[0] == "正财" || ssg.Tg[0] == "偏财" {
				if ssg.Tg[1] == "正印" {
					return true
				}
			}
			if ssg.Tg[1] == "正财" || ssg.Tg[1] == "偏财" {
				if ssg.Tg[3] == "正印" {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG106]")
		}
	}
	{
		/*
			103. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞不存在羊刃
			  - 条件2：原局四柱干十神、藏干十神不存在七杀
			  - 条件3：年干、月干、时干十神同时正官、正印
		*/
		if func() bool {
			if array.Has(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if ssg.NumMap["七杀"] > 0 {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG107]")
		}
	}
	{
		/*
			104. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件之一
			  - 条件1：以下均满足即可
			    - 天干十神为正印
			    - 地支藏干十神为七杀
			  - 条件2：以下均满足即可
			    - 天干十神为七杀
			    - 地支藏干十神为正印
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] == "正印" && array.Has(ssg.CgByIdx(idx), "七杀") {
					return true
				}
				if ssg.Tg[idx] == "七杀" && array.Has(ssg.CgByIdx(idx), "正印") {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG108]")
		}
	}
	{
		/*
			105. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为食神
			  - 条件2：食神对应的五行，为月支对应五行的比劫五行（相同的）或食伤五行（我生的）
			  - 条件3：该柱神煞存在驿马
			  - 条件4：该柱神煞存在禄神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "食神" {
					continue
				}
				if paipanAll.ShishenWuxingMap["食神"] != slf.BiJie(slf.GetWuxingByDizhi(dizhi[1])) && paipanAll.ShishenWuxingMap["食神"] != slf.ShiShang(slf.GetWuxingByDizhi(dizhi[1])) {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "驿马") {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "禄神") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG109]")
		}
	}
	{
		/*
			106. 条件：若原局四柱干支均为壬辰、庚戌、庚辰、戊戌集合中的值
		*/
		if func() bool {
			for _, s := range ganzhi {
				if !array.Has([]string{"壬辰", "庚戌", "庚辰", "戊戌"}, s) {
					return false
				}
			}
			return true
		}() {
			keys = append(keys, "[GG110]")
		}
	}
	{
		/*
			107. 条件：若原局四柱至少有两组干支为壬辰、庚戌、庚辰、戊戌集合中的值
		*/
		if func() bool {
			count := 0
			for _, s := range ganzhi {
				if array.Has([]string{"壬辰", "庚戌", "庚辰", "戊戌"}, s) {
					count++
				}
			}
			if count < 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG111]")
		}
	}
	{
		/*
			108. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞，存在两个及以上华盖
			  - 条件2：华盖所在柱地支中，至少有两个地支对应的五行与月支对应的五行相同，或为月支五行所生五行
		*/
		if func() bool {
			if array.Count(array.Merge(paipanAll.Shensha4...), "华盖") < 2 {
				return false
			}
			count := 0
			yzWx := slf.GetWuxingByDizhi(dizhi[1])
			for i, s := range paipanAll.Shensha4 {
				if array.Has(s, "华盖") {
					if slf.GetWuxingByDizhi(dizhi[i]) == yzWx || slf.GetWuxingByDizhi(dizhi[i]) == slf.ShiShang(yzWx) {
						count++
					}
				}
			}
			if count < 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG112]")
		}
	}
	{
		/*
			109. 条件：若以下条件均满足
			  - 条件1：原局四柱天干，均为阳属性或均为阴属性
			  - 条件2：原句四柱命理中，的十神能量大于等于90
			  - 条件3：原局四柱干十神、藏干十神不存在食神
		*/
		if func() bool {
			if !array.Has([]string{"甲", "丙", "戊", "庚", "壬"}, tiangan...) && !array.Has([]string{"乙", "丁", "己", "辛", "癸"}, tiangan...) {
				return false
			}
			if shishenPowerMap["十神"] < 90 {
				return false
			}
			if ssg.NumMap["食神"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG113]")
		}
	}
	{
		/*
			110. 条件：若以下条件均满足
			  - 条件1：年柱天干十神为正印
			  - 条件2：月柱天干十神为正官
		*/
		if func() bool {
			if ssg.Tg[0] != "正印" {
				return false
			}
			if ssg.Tg[1] != "正官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG114]")
		}
	}
	{
		/*
			111. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为七杀
			  - 条件2： 该柱神煞存在金神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "七杀" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "金神") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG115]")
		}
	}
	{
		/*
			112. 条件：若若原局四柱地支为“寅申巳亥”四个地支（无关顺序）
		*/
		if func() bool {
			if !array.Has([]string{
				"寅", "申", "巳", "亥",
			}, array.Merge(tiangan, dizhi)...) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG116]")
		}
	}
	{
		/*
			113. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神，存在伤官
			  - 条件2：年干、月干、时干十神，存在正财或偏财
			  - 条件4：财才对应对应的五行为原局命理的喜用神
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["财才"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG117]")
		}
	}
	{
		/*
			114. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在正印
			  - 条件2：年干、月干、时干十神，存在正财或偏财
			  - 条件4：以下条件满足1个即可
			    - 日柱干支为壬午，月支的值为午
			    - 日柱干支为癸巳，月支的值为巳
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if (tiangan[2] == "壬" && dizhi[2] == "午" && dizhi[1] == "午") || (tiangan[2] == "癸" && dizhi[2] == "巳" && dizhi[1] == "巳") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG118]")
		}
	}
	{
		/*
			115. 条件：若年干、月干、时干十神同时存在伤官、正印
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官", "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG119]")
		}
	}
	{
		/*
			116. 条件：若以下任意一条件满足
			  - 条件1：日干为甲，原局四柱天干有丙和辛
			  - 条件2：日干为乙，原局四柱天干有丁和壬
			  - 条件3：日干为丙，原局四柱天干有戊和癸
			  - 条件4：日干为庚，原局四柱天干有壬和丁
			  - 条件5：日干为己，原局四柱天干有丙和辛
			  - 条件6：日干为辛，原局四柱天干有戊和癸
		*/
		if func() bool {
			switch rigan {
			case "甲":
				if array.Has(tiangan, "丙", "辛") {
					return true
				}
			case "乙":
				if array.Has(tiangan, "丁", "壬") {
					return true
				}
			case "丙":
				if array.Has(tiangan, "戊", "癸") {
					return true
				}
			case "庚":
				if array.Has(tiangan, "壬", "丁") {
					return true
				}
			case "己":
				if array.Has(tiangan, "丙", "辛") {
					return true
				}
			case "辛":
				if array.Has(tiangan, "戊", "癸") {
					return true
				}
			default:
				return false
			}
			return false
		}() {
			keys = append(keys, "[GG120]")
		}
	}
	{
		/*
			117. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞至少存在两个羊刃
			  - 条件2：原局年柱、月柱、时柱天干十神存在正印
		*/
		if func() bool {
			if array.Count(array.Merge(paipanAll.Shensha4...), "羊刃") < 2 {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG121]")
		}
	}
	{
		/*
			118. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正官
			  - 条件2：月柱神煞存在羊刃
			  - 条件3：时柱天干为正官
			  - 条件4：时柱神煞存在羊刃
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[1], "羊刃") {
				return false
			}
			if ssg.Tg[3] != "正官" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[3], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG122]")
		}
	}
	{
		/*
			119. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正官
			  - 条件2：月柱神煞存在天乙贵人
			  - 条件3：年干、时干十神不为伤官
			  - 条件4：时柱神煞存在天乙贵人
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[1], "天乙贵人") {
				return false
			}
			if ssg.Tg[0] == "伤官" || ssg.Tg[3] == "伤官" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[3], "天乙贵人") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG123]")
		}
	}
	{
		/*
			120. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为七杀
			  - 条件2：月柱地支藏干十神存在正印
			  - 条件3：时柱天干十神为伤官
		*/
		if func() bool {
			if ssg.Tg[1] != "七杀" {
				return false
			}
			if !array.Has(ssg.CgByIdx(1), "正印") {
				return false
			}
			if ssg.Tg[3] != "伤官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG124]")
		}
	}
	{
		/*
			121. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、藏干十神不存在正财
			  - 条件2：原局四柱干十神、藏干十神不存在偏财
			  - 条件3：原局四柱干十神、藏干十神不存在正官
			  - 条件4：原局四柱干十神、藏干十神不存在七杀
		*/
		if func() bool {
			if ssg.NumMap["正财"] > 0 {
				return false
			}
			if ssg.NumMap["偏财"] > 0 {
				return false
			}
			if ssg.NumMap["正官"] > 0 {
				return false
			}
			if ssg.NumMap["七杀"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG125]")
		}
	}
	{
		/*
			122. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神同时存在正官、伤官
			  - 条件2：年干、月干、时干食神不存在偏财
			  - 条件3：年干、月干、时干食神不存在正财
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "伤官") {
				return false
			}
			if array.HasAny(ssg.TgListByIdx(0, 1, 3), "偏财", "正财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG126]")
		}
	}
	{
		/*
			123. 条件：若以下条件均满足
			  - 条件1：月干十神为正官
			  - 条件2：年干或时干十神为正官
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if ssg.Tg[0] != "正官" && ssg.Tg[3] != "正官" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG127]")
		}
	}
	{
		/*
			124. 条件：若：年干、月干、时干十神同时存在正官、伤官
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG128]")
		}
	}
	{
		/*
			125. 条件：若命局年干、月干、时干十神，同时存在正官与七杀，且
			  - 条件1：且干十神为七杀所对应的天干，在原局四柱中存在天干五合
			    - 则显示【GG129】“官杀混杂,合杀为贵。”
			    条件1满足，本条规则结束
			  - 条件2：且干十神为正官所对应的天干，在原局四柱中存在天干五合
			    - 则显示【GG130】“官杀混杂,合去官星，难为官贵。”
			  - 条件3:若命局干十神为正官的天干、干十神为七杀的天干，均未在原局四柱发生天干五合
			    - 则显示【GG131】“官杀混杂无官贵。”
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "七杀") {
				return false
			}
			return true
		}() {
			idx1 := array.Index(ssg.Tg, "七杀", 0, 1, 3)
			ok1 := false
			two, _ := comb23(array.Remove(tiangan, tiangan[idx1], 1), tiangan[idx1])
			for _, arr := range two {
				if _, ok1 = slf.IsTianganWuhe(arr[0], arr[1]); ok1 {
					break
				}
			}
			idx2 := array.Index(ssg.Tg, "正官", 0, 1, 3)
			ok2 := false
			two, _ = comb23(array.Remove(tiangan, tiangan[idx2], 1), tiangan[idx2])
			for _, arr := range two {
				if _, ok2 = slf.IsTianganWuhe(arr[0], arr[1]); ok2 {
					break
				}
			}
			if ok1 {
				keys = append(keys, "[GG129]")
			} else if ok2 {
				keys = append(keys, "[GG130]")
			}
			if !ok1 && !ok2 {
				keys = append(keys, "[GG131]")
			}
		}
	}
	{
		/*
			126. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：干十神存在正官
			  - 条件2：以下条件任意一条发生即可
			    - 地支与原局四柱地支发生六冲
			    - 地支与原局四柱地支发生相害
			    - 地支与原局四柱地支发生相刑
			    - 地支与原局四柱地支发生三刑
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				two, three := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				for _, arr := range two {
					if slf.IsDizhiXiangchong(arr[0], arr[1]) {
						return true
					}
					if slf.IsDizhiXianghai(arr[0], arr[1]) {
						return true
					}
					if slf.IsDizhiXiangxing(arr[0], arr[1]) {
						return true
					}
				}
				for _, arr := range three {
					if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
						return true
					}
				}
			}
			return false
		}() {
			keys = append(keys, "[GG132]")
		}
	}
	{
		/*
			127. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正官
			  - 条件2：该柱神煞存在羊刃
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正官" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG133]")
		}
	}
	{
		/*
			128. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在正官
			  - 条件2：原局四柱干十神、藏干十神不存在正印
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if ssg.NumMap["正印"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG134]")
		}
	}
	{
		/*
			129. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在正官
			  - 条件2：日柱干支为庚戌或庚辰
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if ganzhi[2] != "庚戌" && ganzhi[2] != "庚辰" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG135]")
		}
	}
	{
		/*
			130. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、藏干十神的正官数量存在三个及三个以上
			  - 条件2：原句命理的命主旺衰为身弱
		*/
		if func() bool {
			if ssg.NumMap["正官"] < 3 {
				return false
			}
			if riyuan != "身弱" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG136]")
		}
	}
	{
		/*
			131. 条件：若：原局命理十神能量中，财才的能量值小于40
		*/
		if shishenPowerMap["财才"] < 40 {
			keys = append(keys, "[GG137]")
		}
	}
	{
		/*
			132. 条件：若以下条件均满足
			  - 条件1：年干十神为七杀
			  - 条件2：年干对应的五行为原局命理的仇忌五行
		*/
		if func() bool {
			if ssg.Tg[0] != "七杀" {
				return false
			}
			if !array.Has(chouji, slf.GetWuxingByTiangan(tiangan[0])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG138]")
		}
	}
	{
		/*
			133. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干食神存在正官
			  - 条件2：年干、月干、时干食神存在正印
			  - 条件3：年干、月干、时干食神存在正财或偏财
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官", "正印") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG139]")
		}
	}
	{
		/*
			134. 条件：若以下条件均满足
			  - 条件1：原局命理中命主旺衰为偏强、身强、从弱
			  - 条件2：原局十神能量中，财才的能量大于等于40
			  - 条件3：年干、月干、时干十神，存在正财或偏财的同时，还存在正官
		*/
		if func() bool {
			if !array.Has([]string{"偏强", "身强", "从弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["财才"] < 40 {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG140]")
		}
	}
	{
		/*
			135. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神同时存在伤官、正官
			  - 条件2：伤官对应的天干与正官对应的天干发生天干五合
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官", "正官") {
				return false
			}
			idx1 := array.Index(ssg.Tg, "正官")
			idx2 := array.Index(ssg.Tg, "伤官")
			tg1, tg2 := tiangan[idx1], tiangan[idx2]
			if _, ok := slf.IsTianganWuhe(tg1, tg2); !ok {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG141]")
		}
	}
	{
		/*
			136. 条件：若年干、月干、时干十神同时存在七杀、正印
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀", "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG142]")
		}
	}
	{
		/*
			137. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神同时存在伤官、正印
			  - 条件2：财才对应的五行，为原局命理的喜用五行
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官", "正印") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["财才"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG143]")
		}
	}
	{
		/*
			138. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干食神为正官
			  - 条件2：原局命理命主旺衰为偏强、身强、从弱
			  - 条件3：正官七杀对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			if ssg.Tg[0] != "正官" || ssg.Tg[1] != "正官" || ssg.Tg[3] != "正官" {
				return false
			}
			if !array.Has([]string{"偏强", "身强", "从弱"}, riyuan) {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["官杀"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG144]")
		}
	}
	{
		/*
			139. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：地支对应的五行为原局命理的喜用神
			  - 条件2：该柱神煞存在华盖
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "华盖") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG145]")
		}
	}
	{
		/*
			140. 条件：若以下条件均满足
			  - 条件1：日柱天干为癸
			  - 条件2：时柱地支为寅
			  - 条件3：年支、月支、日支至少存在一个地支为巳
		*/
		if func() bool {
			if tiangan[2] != "癸" {
				return false
			}
			if dizhi[3] != "寅" {
				return false
			}
			if !array.Has(dizhi[0:3], "巳") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG146]")
		}
	}
	{
		/*
			141. 条件：若以下条件均满足
			  - 条件1：年干十神为七杀
			  - 条件2：月干、时干十神，存在正财或偏财
			  - 条件3：月干、时干十神不存在正官或七杀
		*/
		if func() bool {
			if ssg.Tg[0] != "七杀" {
				return false
			}
			if !array.Has(ssg.TgListByIdx(1, 3), "正财", "偏财") {
				return false
			}
			if array.HasAny(ssg.TgListByIdx(1, 3), "正官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG147]")
		}
	}
	{
		/*
			142. 条件：若以下条件均满足
			  - 条件1：原局命理十神能量中，财才的能量值小于40
			  - 条件2：月干十神为正印
		*/
		if func() bool {
			if shishenPowerMap["财才"] >= 40 {
				return false
			}
			if ssg.Tg[1] != "正印" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG148]")
		}
	}
	{
		/*
			143. 条件：若以下条件均满足
			  - 条件1：日柱星运为沐浴
			  - 条件2：日柱地支对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			if paipanAll.GetXingyunListLiuAll[2] != "沐浴" {
				return false
			}
			if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[2])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG149]")
		}
	}
	{
		/*
			144. 条件：若以下条件均满足
			  - 条件1：原局命理中的喜用五行为火
			  - 条件2：原局命理中五行能量，火的能量大于等于150
		*/
		if func() bool {
			if !array.Has(xiyong, "火") {
				return false
			}
			if paipanAll.WuxingPowerMap["火"] < 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG150]")
		}
	}
	{
		/*
			145. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞存在将星
			  - 条件2：以下条件满足一条即可
			    - 该柱神煞存在天德贵人
			    - 该柱神煞存在月德贵人
			  - 条件3：地支对应的五行，为原局命理的喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "将星") {
					continue
				}
				if !array.HasAny(paipanAll.Shensha4[idx], "天德贵人", "月德贵人") {
					continue
				}
				if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG151]")
		}
	}
	{
		/*
			146. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在正印
			  - 条件2：日干为壬或癸
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
				return false
			}
			if !array.Has([]string{"壬", "癸"}, tiangan[2]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG152]")
		}
	}
	{
		/*
			147. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞存在驿马
			  - 条件2：该柱神煞存在学堂
			  - 条件3：地支对应的五行，为原局命理的喜用五行
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "驿马") {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "学堂") {
					continue
				}
				if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG153]")
		}
	}
	{
		/*
			148. 条件：若原局四柱神煞存在词馆
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "词馆") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG154]")
		}
	}
	{
		/*
			149. 条件：若以下条件均满足
			  - 条件1：时干十神为食神
			  - 条件2：原句四柱干十神、藏干十神，不存在偏印
			  - 条件3：以下条件满足一条即可
			    - 时柱神煞存在禄神
			    - 时柱神煞存在驿马
		*/
		if func() bool {
			if ssg.Tg[3] != "食神" {
				return false
			}
			if array.Has(ssg.All(), "偏印") {
				return false
			}
			if !array.HasAny(paipanAll.Shensha4[3], "禄神", "驿马") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG155]")
		}
	}
	{
		/*
			150. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在正财或存在偏财
			  - 条件2：年柱、月柱、时柱天干十神存在食神
			  - 条件3：年柱、月柱、时柱天干十神存在正官
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "食神") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG156]")
		}
	}
	{
		/*
			151. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：该柱神煞存在禄神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "禄神") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG157]")
		}
	}
	{
		/*
			152. 条件：若年柱空亡、月柱空亡、日柱空亡。时柱空亡的值一致
		*/
		if array.Equal(paipanAll.GetKongwangListLiuAll[:4]...) {
			keys = append(keys, "[GG158]")
		}
	}
	{
		/*
			153. 条件：若以下条件均满足
			  - 条件1：年柱天干、月柱天干、时柱天干十神存在正财或偏财
			  - 条件2：年柱天干、月柱天干、时柱天干十神存在正印
			  - 条件3：月柱天干为正财、偏财或正印
			  - 条件4：年柱、月柱、日柱、时柱地支藏干生死恨存在正印
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
				return false
			}
			if !array.HasAny([]string{"正财", "偏财", "正印"}, ssg.Tg[1]) {
				return false
			}
			if !array.Has(ssg.CangGan(), "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG159]")
		}
	}
	{
		/*
			154. 条件：若以下条件均满足
			  - 条件1：原局命理中，命主旺衰为身强
			  - 条件2：原局命理十神能量中，印枭的能量大于等于90
			  - 条件3：年干、月干、时干十神存在正印和正官
		*/
		if func() bool {
			if riyuan != "身强" {
				return false
			}
			if shishenPowerMap["印枭"] < 90 {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正印", "正官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG160]")
		}
	}
	{
		/*
			155. 条件：若年干、月干、时干十神存在伤官和正印
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官", "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG161]")
		}
	}
	{
		/*
				156. 条件：若以下条件均满足
			  - 条件1：年干十神或月干十神，为食神
			  - 条件2：月干十神或时干十神，为七杀
			  - 条件3：原局天干十神，不存在偏印
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1), "食神") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(1, 3), "七杀") {
				return false
			}
			if array.Has(ssg.Tg, "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG162]")
		}
	}
	{
		/*
			157. 条件：若年柱天干十神为正印
		*/
		if func() bool {
			if ssg.Tg[0] != "正印" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG163]")
		}
	}
	{
		/*
			158. 条件：若以下条件均满足
			  - 条件1：日干为丁
			  - 条件2：时柱干支为壬寅
		*/
		if func() bool {
			if rigan != "丁" {
				return false
			}
			if ganzhi[3] != "壬寅" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG164]")
		}
	}
	{
		/*
			159. 条件：若以下条件均满足
			  - 条件1：原局四柱干十神、支十神至少存在一个伤官
			  - 条件2：原局十神能量中，财才的能量大于等于90
			  - 条件3：原局十神能量中，官杀的能量小于10
			  - 条件4：原局命理中命主旺衰为偏强、身强或从强
		*/
		if func() bool {
			if !array.Has(ssg.All(), "伤官") {
				return false
			}
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if shishenPowerMap["官杀"] >= 10 {
				return false
			}
			if !array.Has([]string{"偏强", "身强", "从强"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG165]")
		}
	}
	{
		/*
			160. 条件：若以下条件均满足
			  - 条件1：月干天干十神为七杀
			  - 条件2：月干地支对应的五行，为原局命理的喜用五行
			  - 条件3：月柱神煞存在羊刃
		*/
		if func() bool {
			if ssg.Tg[1] != "七杀" {
				return false
			}
			if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[1])) {
				return false
			}
			if !array.Has(paipanAll.Shensha4[1], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG166]")
		}
	}
	{
		/*
			161. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干干十神存在伤官
			  - 条件2：条件1中伤官所在柱神煞存在羊刃
			  - 条件3：年干、月干、时干干十神存在七杀
		*/
		if func() bool {
			idx := ssg.TgSearch("伤官", 0, 1, 3)
			if idx < 0 {
				return false
			}
			if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG167]")
		}
	}
	{
		/*
			162. 条件：若以下条件均满足
			  - 条件1：日干为乙
			  - 条件2：时柱位丙子
			  - 条件3：时支子，未与原局年支、月支、日支发生三刑、相刑、相冲
		*/
		if func() bool {
			if rigan != "乙" {
				return false
			}
			if ganzhi[3] != "丙子" {
				return false
			}
			if slf.IsDizhiSanxing(dizhi[3], dizhi[0], dizhi[1]) || slf.IsDizhiSanxing(dizhi[3], dizhi[0], dizhi[2]) || slf.IsDizhiSanxing(dizhi[3], dizhi[1], dizhi[2]) {
				return false
			}
			if slf.IsDizhiXiangxing(dizhi[3], dizhi[0]) || slf.IsDizhiXiangxing(dizhi[3], dizhi[1]) || slf.IsDizhiXiangxing(dizhi[3], dizhi[2]) {
				return false
			}
			if slf.IsDizhiXiangchong(dizhi[3], dizhi[0]) || slf.IsDizhiXiangchong(dizhi[3], dizhi[1]) || slf.IsDizhiXiangchong(dizhi[3], dizhi[2]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG168]")
		}
	}
	{
		/*
			163. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干存在天干甲
			  - 条件2：甲对应的干十神为伤官
			  - 条件3：四柱地支存在寅、午、戌三个地支
		*/
		if func() bool {
			if !array.Has(array.SubArray(tiangan, 0, 1, 3), "甲") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			if !array.Has(dizhi, "寅", "午", "戌") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG169]")
		}
	}
	{
		/*
			164. 条件：若以下任意一条条件满足
			  - 条件1：日支藏干十神为正官
			  - 条件2：时支藏干十神为正官
		*/
		if func() bool {
			if array.Has(ssg.CgListByIdx(2, 3), "正官") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG170]")
		}
	}
	{
		/*
			165. 条件：若以下条件均满足
			  - 条件1：时干十神为七杀
			  - 条件2：原局四柱干十神、藏干十神中，七杀的数量为一个
		*/
		if func() bool {
			if ssg.Tg[3] != "七杀" {
				return false
			}
			if ssg.NumMap["七杀"] != 1 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG171]")
		}
	}
	{
		/*
			166. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神，存在正官
			  - 条件2：年干、月干、时干十神，不存在七杀
			  - 条件3：原局四柱地支藏干十神，存在七杀
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if !array.Has(ssg.CangGan(), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG172]")
		}
	}
	{
		/*
			167. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在正财或偏财
			  - 条件2：年干、月干、时干十神不存在正印
			  - 条件3：原局四柱地支藏干十神，存在正印
			  - 条件4：原局命理的命主旺衰为偏强、身强或从强
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "正印") {
				return false
			}
			if !array.Has(ssg.CangGan(), "正印") {
				return false
			}
			if !array.Has([]string{"偏强", "身强", "从强"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG173]")
		}
	}
	{
		/*
			168. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干十神为正印
			  - 条件2：该柱神煞存在禄神
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "禄神") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG174]")
		}
	}
	{
		/*
			169. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞存在学堂
			  - 条件2：地支发生三合或半合
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "学堂") {
					continue
				}
				two, three := comb23(array.Remove(dizhi, dizhi[idx], 1), dizhi[idx])
				for _, arr := range two {
					if _, ok := slf.IsDizhiBanhe(arr[0], arr[1]); ok {
						return true
					}
				}
				for _, arr := range three {
					if _, ok := slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); ok {
						return true
					}
				}
			}
			return false
		}() {
			keys = append(keys, "[GG175]")
		}
	}
	{
		/*
			170. 条件：若以下条件均满足
			  - 条件1：日柱神煞存在天乙贵人
			  - 条件2：日柱神煞存在驿马
			  - 条件3：日柱神煞存在禄神
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[2], "天乙贵人", "驿马", "禄神") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG176]")
		}
	}
	{
		/*
			171. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在食神或伤官
			  - 条件2：原局命理中命主旺衰为偏强、身强、从强
			  - 条件3：年柱、月柱、时柱天干十神不存在偏印
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "食神", "伤官") {
				return false
			}
			if !array.Has([]string{"偏强", "身强", "从强"}, riyuan) {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG177]")
		}
	}
	{
		/*
			172. 条件：若以下条件均满足
			  - 条件1：日柱为癸亥
			  - 条件2：时柱为癸丑
		*/
		if func() bool {
			if ganzhi[2] != "癸亥" {
				return false
			}
			if ganzhi[3] != "癸丑" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG178]")
		}
	}
	{
		/*
			173. 条件：若以下条件均满足
			  - 条件1：月柱星运为"长生", "沐浴", "冠带", "临官"或"帝旺"
			  - 条件2：时柱星运为"死", "墓"或 "绝"
		*/
		if func() bool {
			if !array.Has([]string{"长生", "沐浴", "冠带", "临官", "帝旺"}, paipanAll.GetXingyunList[1]) {
				return false
			}
			if !array.Has([]string{"死", "墓", "绝"}, paipanAll.GetXingyunList[3]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG179]")
		}
	}
	{
		/*
			174. 条件：若以下条件均满足
			  - 条件1：原局命理十神能量中，财才的能量值大于等于90
			  - 条件2：原局四柱干十神、藏干十神中正财和偏财的数量大于等于3个
			  - 条件3：原局四柱干十神、藏干十神中无比肩
			  - 条件4：原局四柱干十神、藏干十神中无劫财
		*/
		if func() bool {
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if ssg.NumMap["正财"]+ssg.NumMap["偏财"] < 3 {
				return false
			}
			if ssg.NumMap["比肩"] > 0 {
				return false
			}
			if ssg.NumMap["劫财"] > 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG180]")
		}
	}
	{
		/*
			175. 条件：若原局年柱、月柱、时柱天干十神同时存在七杀、十神
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀", "十神") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG181]")
		}
	}
	{
		/*
			176. 条件：若以下条件均满足
			  - 条件1：年干、月干十神存在正印
			  - 条件2：月干、时干食神存在正财或偏财
			  - 条件3：财才对应的五行，为原局命理的喜用五行
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1), "正印") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(1, 3), "正财", "偏财") {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["财才"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG182]")
		}
	}
	{
		/*
			177. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神在伤官
			  - 条件2：年干、月干、时干十神在正财或偏财
			  - 条件3：条件1的天干，与条件2的天干，存在天干五合
		*/
		if func() bool {
			idx1 := ssg.TgSearch("伤官", 0, 1, 3) // 十神相同，则天干必定相同，故多个索引可任取其一
			if idx1 < 0 {
				return false
			}
			idx2 := ssg.TgSearch("正财", 0, 1, 3)
			if idx2 < 0 {
				return false
			}
			tg1, tg2 := tiangan[idx1], tiangan[idx2]
			if _, ok := slf.IsTianganWuhe(tg1, tg2); ok {
				return true
			}
			idx3 := ssg.TgSearch("偏财", 0, 1, 3)
			if idx3 < 0 {
				return false
			}
			tg3 := tiangan[idx3]
			if _, ok := slf.IsTianganWuhe(tg1, tg3); !ok {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG183]")
		}
	}
	{
		/*
			178. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神在七杀
			  - 条件2：年干、月干、时干十神在伤官
			  - 条件3：条件1的天干，与条件2的天干，存在天干五合
		*/
		if func() bool {
			idx1 := ssg.TgSearch("七杀", 0, 1, 3)
			if idx1 < 0 {
				return false
			}
			idx2 := ssg.TgSearch("伤官", 0, 1, 3)
			if idx2 < 0 {
				return false
			}
			tg1, tg2 := tiangan[idx1], tiangan[idx2]
			if _, ok := slf.IsTianganWuhe(tg1, tg2); !ok {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG184]")
		}
	}
	{
		/*
			179. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞存在禄神
			  - 条件2：天干十神为七杀
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "禄神") {
					continue
				}
				if ssg.Tg[idx] != "七杀" {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG185]")
		}
	}
	{
		/*
			180. 条件：若以下条件均满足
			- 条件1：原局命理十神能量中，财才等能量大于等于90
			- 条件2：原局命理十神能量中，官杀等能量大于等于90
			- 条件3：原局命理命主旺衰为身强、偏强、从强
		*/
		if func() bool {
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if shishenPowerMap["官杀"] < 90 {
				return false
			}
			if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG186]")
		}
	}
	{
		/*
			181. 条件：若原局命理中，年柱、月柱、时柱任意两柱或两柱满足以下条件
			  - 条件1：地支对应的五行与月支对应的五行相同（比劫），或者为月支对应的五行所生五行（食伤）
			  - 条件2：该柱神煞存在空亡
		*/
		if func() bool {
			for _, idx1 := range []int{0, 1, 3} {
				for _, idx2 := range []int{0, 1, 3} {
					if idx1 == idx2 {
						continue
					}
					if !array.Has(paipanAll.Shensha4[idx1], "空亡") {
						continue
					}
					if !array.Has(paipanAll.Shensha4[idx2], "空亡") {
						continue
					}
					if slf.GetWuxingByDizhi(dizhi[idx1]) != slf.GetWuxingByDizhi(dizhi[1]) &&
						!slf.IsWuxingXiangsheng(dizhi[1], slf.GetWuxingByDizhi(dizhi[idx1])) {
						continue
					}
					if slf.GetWuxingByDizhi(dizhi[idx2]) != slf.GetWuxingByDizhi(dizhi[1]) &&
						!slf.IsWuxingXiangsheng(dizhi[1], slf.GetWuxingByDizhi(dizhi[idx2])) {
						continue
					}
				}
			}
			return false
		}() {
			keys = append(keys, "[GG187]")
		}
	}
	{
		/*
			182. 条件：若以下条件均满足
			  - 条件1：年柱天干十神为正官
			  - 条件2：月柱、时柱天干十神不存在伤官
		*/
		if func() bool {
			if ssg.Tg[0] != "正官" {
				return false
			}
			if array.Has(ssg.TgListByIdx(1, 3), "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG188]")
		}
	}
	{
		/*
			183. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在正官或七杀
			  - 条件2：月柱神煞存在羊刃
			  - 条件3：日柱神煞存在羊刃
			  - 条件4：时柱神煞存在羊刃
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正官", "七杀") {
				return false
			}
			if !array.Has(paipanAll.Shensha4[1], "羊刃") {
				return false
			}
			if !array.Has(paipanAll.Shensha4[2], "羊刃") {
				return false
			}
			if !array.Has(paipanAll.Shensha4[3], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG189]")
		}
	}
	{
		/*
			184. 条件：若以下条件均满足
			  - 条件1：月柱神煞存在禄神
			  - 条件2：月柱地支对应的五行为原局命理的喜用五行
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[1], "禄神") {
				return false
			}
			if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[1])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG190]")
		}
	}
	{
		/*
			185. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正官
			  - 条件2：时柱天干十神为偏财
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if ssg.Tg[3] != "偏财" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG191]")
		}
	}
	{
		/*
			186. 条件：若以下条件均满足
			  - 条件1：月柱天干十神为正官
			  - 条件2：时柱天干十神为偏财
			  - 条件3：时柱地支为食神
		*/
		if func() bool {
			if ssg.Tg[1] != "正官" {
				return false
			}
			if ssg.Tg[3] != "偏财" {
				return false
			}
			if ssg.Dz[3] != "食神" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG192]")
		}
	}
	{
		/*
			187. 条件：若以下条件均满足
			  - 条件1：年柱神煞存在羊刃
			  - 条件2：年柱神煞存在天德贵人或月德贵人
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[0], "羊刃") {
				return false
			}
			if !array.HasAny(paipanAll.Shensha4[0], "天德贵人", "月德贵人") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG193]")
		}
	}
	{
		/*
			188. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞存在禄神
			  - 条件2：原局四柱神煞存在驿马
			  - 条件3：原局天干十神不存在偏印
			  - 条件4：年柱、月柱、时柱十神存在食神
		*/
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4...), "禄神") {
				return false
			}
			if !array.Has(array.Merge(paipanAll.Shensha4...), "驿马") {
				return false
			}
			if array.Has(ssg.Tg, "偏印") {
				return false
			}
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "食神") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG194]")
		}
	}
	{
		/*
			189. 条件：若以下条件均满足
			  - 条件1：时柱神煞存在禄神
			  - 条件2：原局四柱神煞存在文昌贵人
			  - 条件3：原局年柱、月柱、时柱十神存在正财或偏财
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[3], "禄神") {
				return false
			}
			if !array.Has(array.Merge(paipanAll.Shensha4...), "文昌贵人") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG195]")
		}
	}
	{
		/*
			190. 条件：若以下条件均满足
			  - 条件1：原局四柱神煞存在学堂或词馆
			  - 条件2：年干、月干、时干十神不存在正官
			  - 条件3：年干、月干、时干十神不存在qisha
		*/
		if func() bool {
			if !array.HasAny(array.Merge(paipanAll.Shensha4...), "学堂", "词馆") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "正官") {
				return false
			}
			if array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG196]")
		}
	}
	{
		/*
			191. 条件：若：原局干十神、藏干十神中比肩、劫财数量之和大于等于3
		*/
		if ssg.NumMap["比肩"]+ssg.NumMap["劫财"] >= 3 {
			keys = append(keys, "[GG197]")
		}
	}
	{
		/*
			192. 条件：若以下条件均满足
			  - 条件1：原局命理的命主旺衰为身弱、从弱
			  - 条件2：印枭对应的五行为原局命理的喜用五行
			  - 条件3：原局四柱十神能量中，印枭的能量
		*/
		if func() bool {
			if !array.Has([]string{"身弱", "从弱"}, riyuan) {
				return false
			}
			if !array.Has(xiyong, paipanAll.ShishenWuxingMap["印枭"]) {
				return false
			}
			if shishenPowerMap["印枭"] >= 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG198]")
		}
	}
	{
		/*
			193. 条件：若以下条件均满足
			  - 条件1：年干、月干、时干十神存在七杀
			  - 条件2：原局十神能量中，食伤能量 - 官杀能量 >=100
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "七杀") {
				return false
			}
			if shishenPowerMap["食伤"]-shishenPowerMap["官杀"] < 100 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG199]")
		}
	}
	{
		/*
			194. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：该柱神煞为空亡
			  - 条件2：该柱地支为乙、丙、丁之一
			  - 条件3：该地支的五行，与月支五行的相同，或为月支五行所生五行
			  - 条件4：原局四柱地支中，存在乙、丙、丁三个地支
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "空亡") {
					continue
				}
				if !array.Has([]string{"乙", "丙", "丁"}, dizhi[idx]) {
					continue
				}
				if slf.GetWuxingByDizhi(dizhi[idx]) != slf.GetWuxingByDizhi(dizhi[1]) &&
					!slf.IsWuxingXiangsheng(dizhi[1], dizhi[idx]) {
					continue
				}
				if !array.Has(dizhi, "乙", "丙", "丁") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG200]")
		}
	}
	{
		/*
			195. 条件：若以下条件均满足
			  - 条件1：日柱神煞存在魁罡
			  - 条件2：日支发生相刑、三刑或六冲
		*/
		if func() bool {
			if !array.Has(paipanAll.Shensha4[2], "魁罡") {
				return false
			}
			two, three := comb23(dizhi, dizhi[2])
			for _, arr := range two {
				if slf.IsDizhiXiangxing(arr[0], arr[1]) {
					return true
				}
				if slf.IsDizhiXiangchong(arr[0], arr[1]) {
					return true
				}
			}
			for _, arr := range three {
				if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[GG201]")
		}
	}
	{
		/*
			196. 条件：若以下条件均满足
			  - 条件1：原局命理命主旺衰为偏弱、身弱、从强
			  - 条件2：原局命理干十神、藏干十神存在正财和偏财的数量大于等于三个
			  - 条件3：原局十神命理印枭的能量小于40
		*/
		if func() bool {
			if !array.Has([]string{"偏弱", "身弱", "从强"}, riyuan) {
				return false
			}
			if ssg.NumMap["正财"]+ssg.NumMap["偏财"] < 3 {
				return false
			}
			if shishenPowerMap["印枭"] >= 40 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG202]")
		}
	}
	{
		/*
			197. 条件：若以下条件均满足
			  - 条件1：原局命理干十神、藏干十神存在七杀
			  - 条件2：原局命理干十神、藏干十神不存在正印
		*/
		if func() bool {
			if !array.Has(ssg.All(), "七杀") {
				return false
			}
			if array.Has(ssg.All(), "正印") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG203]")
		}
	}
	{
		/*
			198. 条件：若以下条件均满足
			  - 条件1：原局命理干十神、藏干十神存在正印
			  - 条件2：原局命理干十神、藏干十神不存在七杀
		*/
		if func() bool {
			if !array.Has(ssg.All(), "正印") {
				return false
			}
			if array.Has(ssg.All(), "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG204]")
		}
	}
	{
		/*
			199. 条件：若原局命理中，年柱、月柱、时柱任意一柱满足以下条件
			  - 条件1：天干为偏印
			  - 条件2：地支对应的五行与月支对应的五行相同（比劫），或者为月支对应的五行所生五行（食伤）
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if tiangan[idx] != "偏印" {
					continue
				}
				if slf.GetWuxingByDizhi(dizhi[idx]) != slf.GetWuxingByDizhi(dizhi[1]) &&
					!slf.IsWuxingXiangsheng(dizhi[1], dizhi[idx]) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG205]")
		}
	}
	{
		/*
			200. 条件：若以下条件均满足
			  - 条件1：天干十神为食神或伤官
			  - 条件2：地支藏干本气十神为食神或伤官
			  - 条件3：原局四柱天干十神、藏干十神中食神、伤官数量大于等于3个
			  - 条件4：原局命理中日柱旺衰为从强、身弱、偏弱
		*/
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has([]string{"食神", "伤官"}, ssg.Tg[idx]) {
					continue
				}
				if !array.Has([]string{"食神", "伤官"}, ssg.Bq[idx]) {
					continue
				}
				if ssg.NumMap["食神"]+ssg.NumMap["伤官"] < 3 {
					continue
				}
				if !array.Has([]string{"从强", "身弱", "偏弱"}, riyuan) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[GG206]")
		}
	}
	{
		/*
			201. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在正财或偏财
			  - 条件2：原局四柱天干十神、藏干十神中正财、偏财的数量大于等于3个
			  - 条件3：原局四柱十神能量，财才的能量大于等于90
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "正财", "偏财") {
				return false
			}
			if ssg.NumMap["正财"]+ssg.NumMap["偏财"] < 3 {
				return false
			}
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG207]")
		}
	}
	{
		/*
			202. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在比肩或劫财
			  - 条件2：年柱、月柱、时柱天干十神存在存在伤官或偏印
			  - 条件3：原局四柱天干十神、藏干十神中，比肩、劫财、伤官、正印的数量之和大于4
		*/
		if func() bool {
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "比肩", "劫财") {
				return false
			}
			if !array.HasAny(ssg.TgListByIdx(0, 1, 3), "伤官", "偏印") {
				return false
			}
			if ssg.NumMap["比肩"]+ssg.NumMap["劫财"]+ssg.NumMap["伤官"]+ssg.NumMap["偏印"] <= 4 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG208]")
		}
	}
	{
		/*
			203. 条件：若以下条件均满足
			  - 条件1：年柱、月柱、时柱天干十神存在伤官
			  - 条件2：原局四柱天干十神、藏干十神中，均无正财、偏财
		*/
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "伤官") {
				return false
			}
			if array.HasAny(ssg.All(), "正财", "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG209]")
		}
	}
	{
		/*
			204. 条件：若以下条件均满足
			  - 条件1：日干为壬、癸
			  - 条件2：年柱、月柱、日柱、时柱纳音为天河水
		*/
		if func() bool {
			if !array.Has([]string{"壬", "癸"}, tiangan[2]) {
				return false
			}
			if !array.Has(paipanAll.Nayin[:4], "天河水") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG210]")
		}
	}
	{
		/*
			205. 条件：若以下条件均满足
			  - 条件1：月柱干十神、藏干十神存在正官
			  - 条件2：正官对应的五行，与月支的五行一致
		*/
		if func() bool {
			if !array.Has(ssg.ByIdx(1), "正官") {
				return false
			}
			if paipanAll.ShishenWuxingMap["正官"] != slf.GetWuxingByDizhi(dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG211]")
		}
	}
	{
		/*
			206. 条件：若以下条件均满足
			  - 条件1：原局命理命主旺衰为偏强、身强、从弱
			  - 条件2：原局四柱天干十神、藏干十神中，正印和偏印的数量大于等于3
			  - 条件3：原局四柱天干十神、藏干十神中，不存在食神
			  - 条件4：原局四柱天干十神、藏干十神中，不存在伤官
		*/
		if func() bool {
			if !array.Has([]string{"偏强", "身强", "从弱"}, riyuan) {
				return false
			}
			if ssg.NumMap["正印"]+ssg.NumMap["偏印"] < 3 {
				return false
			}
			if array.Has(ssg.All(), "食神") {
				return false
			}
			if array.Has(ssg.All(), "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG212]")
		}
	}
	{
		/*
			207. 条件：若以下条件均满足
			  - 条件1：原局命理命主旺衰为偏弱、身弱、从强
			  - 条件2：原局十神能量中，食伤的能量大于等于150
			  - 条件3：原局四柱天干十神、藏干十神中，不存在正财
			  - 条件4：原局四柱天干十神、藏干十神中，不存在偏财
		*/
		if func() bool {
			if !array.Has([]string{"偏弱", "身弱", "从强"}, riyuan) {
				return false
			}
			if shishenPowerMap["食伤"] < 150 {
				return false
			}
			if array.Has(ssg.All(), "正财") {
				return false
			}
			if array.Has(ssg.All(), "偏财") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG213]")
		}
	}
	{
		/*
			208. 条件：若以下条件均满足
			  - 条件1：原局四柱天干十神、藏干十神中，印枭能量-食伤能量 > =150
			  - 条件2：原局四柱天干十神、藏干十神中，存在食伤
		*/
		if func() bool {
			if shishenPowerMap["印枭"]-shishenPowerMap["食伤"] < 150 {
				return false
			}
			if !array.Has(ssg.All(), "食神") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[GG214]")
		}
	}
	return keys
}
