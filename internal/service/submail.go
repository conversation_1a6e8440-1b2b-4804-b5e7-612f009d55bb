package service

import (
	"context"
	"github.com/pkg/errors"
	"strconv"
	"zodiacus/internal/repository"
	"zodiacus/third_party/submail"
)

type SubMailService interface {
	Notify108520(ctx context.Context, req map[string]any) error
}

func NewSubMailService(service *Service, submailRepo repository.SubMailRepository, submailer *submail.Client,
) SubMailService {
	return &subMailService{
		submailer:   submailer,
		submailRepo: submailRepo,
		Service:     service,
	}
}

type subMailService struct {
	submailRepo repository.SubMailRepository
	submailer   *submail.Client
	*Service
}

func (slf *subMailService) Notify108520(ctx context.Context, req map[string]any) error {
	sendIdVal, exist := req["send_id"]
	if !exist {
		return nil
	}
	sendId := sendIdVal.(string)
	record, err := slf.submailRepo.FetchOnyBySendID(ctx, sendId)
	if err != nil {
		return err
	}
	if record == nil {
		return nil
	}
	switch req["events"] {
	case "request":
		record.Content = req["content"].(string)
	case "delivered":
		record.Status = 1
		record.Operator = req["operator"].(string)
		record.Location = req["location"].(string)
		deliveredAtStr := req["timestamp"].(string)
		record.DeliveredAt, err = strconv.ParseInt(deliveredAtStr, 10, 64)
		if err != nil {
			return err
		}
	case "dropped":
		record.Status = 2
		record.Report = req["report"].(string)
		record.ReportDesc = req["report_desc"].(string)
		droppedAtStr := req["timestamp"].(string) // API文档是错的，mad
		record.DeliveredAt, err = strconv.ParseInt(droppedAtStr, 10, 64)
		if err != nil {
			return err
		}
	default:
		return nil
	}
	if err = slf.submailRepo.UpdateSubMailSMS(ctx, record); err != nil {
		return err
	}
	if record.Status == 2 {
		resp, err := slf.submailer.SMS().XSend(ctx, &submail.SMSXSendRequest{
			To:      record.Address,
			Project: record.TemplateID,
			Vars:    record.Vars,
		})
		if err != nil {
			return errors.Errorf("failed to resend sms, err=%v", err)
		}
		if resp.Msg != "success" {
			return errors.Errorf("failed to resend sms, code=%s, msg=%s", resp.Code, resp.Msg)
		}
	}
	return nil
}
