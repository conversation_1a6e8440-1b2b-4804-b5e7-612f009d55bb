package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type UserOrderService interface {
	PageListOrder(ctx context.Context, req *v1.PageListUserOrderRequest) (*v1.PageListUserOrderResponseData, error)
	OrderDetail(ctx context.Context, req *v1.OrderDetailRequest) (*v1.OrderDetailResponseData, error)
}

func NewUserOrderService(service *Service, userOrderRepo repository.UserOrderRepository) UserOrderService {
	return &userOrderService{
		userOrderRepo: userOrderRepo,
		Service:       service,
	}
}

type userOrderService struct {
	userOrderRepo repository.UserOrderRepository
	*Service
}

func (slf *userOrderService) PageListOrder(ctx context.Context, req *v1.PageListUserOrderRequest) (*v1.PageListUserOrderResponseData, error) {
	return slf.userOrderRepo.PageListUserOrder(ctx, req)
}

func (slf *userOrderService) OrderDetail(ctx context.Context, req *v1.OrderDetailRequest) (*v1.OrderDetailResponseData, error) {
	order, err := slf.userOrderRepo.GetOrderByOrderNo(ctx, req.OrderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, v1.ErrBadRequest
	}
	return &v1.OrderDetailResponseData{Order: *order}, nil
}
