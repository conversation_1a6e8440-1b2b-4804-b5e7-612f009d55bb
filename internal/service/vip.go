package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type VIPService interface {
	PageListVip(ctx context.Context, req *v1.PageListVipMemberRequest) (*v1.PageListVipMemberResponseData, error)
}

func NewVIPService(
	service *Service,
	vipRepo repository.VIPRepository,
	roleRepo repository.RoleRepository,
) VIPService {
	return &vipService{
		Service:  service,
		vipRepo:  vipRepo,
		roleRepo: roleRepo,
	}
}

type vipService struct {
	*Service
	vipRepo  repository.VIPRepository
	roleRepo repository.RoleRepository
}

func (slf *vipService) PageListVip(ctx context.Context, req *v1.PageListVipMemberRequest) (*v1.PageListVipMemberResponseData, error) {
	if len(req.Param.AppIDs) > 0 {
		roles, err := slf.roleRepo.GetRolesByAppIDs(ctx, req.Param.AppIDs)
		if err != nil {
			return nil, err
		}
		for _, role := range roles {
			req.Param.RoleIDs = append(req.Param.RoleIDs, role.ID)
		}
	}
	return slf.vipRepo.PageListVipMember(ctx, req)
}
