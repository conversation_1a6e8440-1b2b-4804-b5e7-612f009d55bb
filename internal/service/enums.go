package service

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/strs"
)

type EnumsService interface {
	App(ctx context.Context, req *v1.EnumsAppRequest) (v1.EnumsAppResponseData, error)
	Module(ctx context.Context) (v1.EnumsModuleResponseData, error)
	Tiangan(ctx context.Context) (v1.EnumsTianganResponseData, error)
	<PERSON>zhi(ctx context.Context) (v1.EnumsDizhiResponseData, error)
	Zuodui(ctx context.Context) (v1.EnumsZuoduiResponseData, error)
	ZuoduiPlus(ctx context.Context) (v1.EnumsZuoduiPlusResponseData, error)
	Nayin(ctx context.Context) (v1.EnumsNayinResponseData, error)
	Ganzhi(ctx context.Context) (v1.EnumsGanzhiResponseData, error)
	<PERSON><PERSON>n(ctx context.Context) (v1.EnumsShishenResponseData, error)
	Wuxing(ctx context.Context) (v1.EnumsWuxingResponseData, error)
	Xiji(ctx context.Context) (v1.EnumsXijiResponseData, error)
	Shensha(ctx context.Context) (v1.EnumsShenshaResponseData, error)
	Shierchangsheng(ctx context.Context) (v1.EnumsShierchangshengResponseData, error)
	Yiji(ctx context.Context) (v1.EnumsYijiResponseData, error)
	Jieqi(ctx context.Context) (v1.EnumsJieqiResponseData, error)
	Time(ctx context.Context) (v1.EnumsTimeResponseData, error)
	Zhishen(ctx context.Context) (v1.EnumsZhishenResponseData, error)
	Shierjianri(ctx context.Context) (v1.EnumsShierjianriResponseData, error)
	JiXiong(ctx context.Context) (v1.EnumsJiXiongResponseData, error)
	Pengzubaiji(ctx context.Context) (v1.EnumsPengzubaijiResponseData, error)
	Lunar(ctx context.Context, year string) (v1.EnumsLunarResponseData, error)
	Location(ctx context.Context, req *v1.EnumsLocationRequest) ([]*v1.LocationTree, error)
}

func NewEnumsService(
	service *Service,
	enumsRepo repository.EnumsRepository,
) EnumsService {
	return &enumsService{
		enumsRepo: enumsRepo,
		Service:   service,
	}
}

type enumsService struct {
	*Service
	enumsRepo repository.EnumsRepository
}

func (slf *enumsService) App(ctx context.Context, req *v1.EnumsAppRequest) (v1.EnumsAppResponseData, error) {
	list, err := slf.enumsRepo.GetAllApp(ctx, req)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsAppResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsAppResponseDataItem{
			ID:        item.ID,
			Name:      item.Name,
			Code:      item.Code,
			IsSelf:    item.IsSelf,
			Org:       item.Org,
			App:       item.App,
			AppID:     item.AppID,
			ClientID:  item.ClientID,
			Role:      item.Role,
			WxMpAppID: item.WxMpAppID,
		})
	}
	return data, nil
}

func (slf *enumsService) Xiji(ctx context.Context) (v1.EnumsXijiResponseData, error) {
	list, err := slf.enumsRepo.GetAllXiji(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsXijiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsXijiResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Shensha(ctx context.Context) (v1.EnumsShenshaResponseData, error) {
	list, err := slf.enumsRepo.GetAllShensha(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsShenshaResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsShenshaResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Shierchangsheng(ctx context.Context) (v1.EnumsShierchangshengResponseData, error) {
	list, err := slf.enumsRepo.GetAllShierchangsheng(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsShierchangshengResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsShierchangshengResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Module(ctx context.Context) (v1.EnumsModuleResponseData, error) {
	list, err := slf.enumsRepo.GetAllModule(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsModuleResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsModuleResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Tiangan(ctx context.Context) (v1.EnumsTianganResponseData, error) {
	list, err := slf.enumsRepo.GetAllTiangan(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsTianganResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsTianganResponseItem{
			ID:   item.ID,
			Name: item.Tiangan,
		})
	}
	return data, nil
}

func (slf *enumsService) Dizhi(ctx context.Context) (v1.EnumsDizhiResponseData, error) {
	list, err := slf.enumsRepo.GetAllDizhi(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsDizhiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsDizhiResponseItem{
			ID:   item.ID,
			Name: item.Dizhi,
		})
	}
	return data, nil
}

func (slf *enumsService) Zuodui(ctx context.Context) (v1.EnumsZuoduiResponseData, error) {
	list, err := slf.enumsRepo.GetAllZuodui(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsZuoduiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsZuoduiResponseItem{
			ID:   item.ID,
			Type: item.Type,
			Zuo:  item.Zuo,
			Dui:  item.Dui,
		})
	}
	return data, nil
}

func (slf *enumsService) ZuoduiPlus(ctx context.Context) (v1.EnumsZuoduiPlusResponseData, error) {
	list, err := slf.enumsRepo.GetAllZuodui(ctx)
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	keys := make(map[string]struct{})
	temp := make(map[string]*model.Zuodui)
	type Combination struct {
		ID  int64  // 记录的 ID
		Dui string // 记录的 Dui
	}
	for _, record := range list {
		if record.Type == 1 {
			keys[record.Zuo] = struct{}{}
			temp[fmt.Sprintf("%s-%s", record.Zuo, record.Dui)] = record
		} else {
			result[record.Zuo] = record.ID
		}
	}
	for zuo := range keys {
		for dui := range keys {
			zuodui := temp[fmt.Sprintf("%s-%s", zuo, dui)]
			if zuodui != nil {
				if result[zuo] == nil {
					result[zuo] = make([]*Combination, 0)
				}
				result[zuo] = append(result[zuo].([]*Combination), &Combination{
					ID:  zuodui.ID,
					Dui: dui,
				})
			}
		}
	}
	return result, nil
}

func (slf *enumsService) JiXiong(ctx context.Context) (v1.EnumsJiXiongResponseData, error) {
	list, err := slf.enumsRepo.GetJiXiong(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsJiXiongResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsJiXiongResponseItem{
			ID:     item.ID,
			Name:   item.Name,
			Detail: item.Detail,
		})
	}
	return data, nil
}

func (slf *enumsService) Pengzubaiji(ctx context.Context) (v1.EnumsPengzubaijiResponseData, error) {
	list, err := slf.enumsRepo.GetAllPengzubaiji(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsPengzubaijiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsPengzubaijiResponseItem{
			ID:     item.ID,
			Name:   item.Name,
			Detail: item.Detail,
		})
	}
	return data, nil
}

func (slf *enumsService) Nayin(ctx context.Context) (v1.EnumsNayinResponseData, error) {
	list, err := slf.enumsRepo.GetAllNayin(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsNayinResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsNayinResponseItem{
			ID:     item.ID,
			Name:   item.Nayin,
			Zhu1:   item.Zhu,
			Zhu2:   item.ZhuTwo,
			Wuxing: item.Wuxing,
			Hanyi:  item.Hanyi,
		})
	}
	return data, nil
}

func (slf *enumsService) Ganzhi(ctx context.Context) (v1.EnumsGanzhiResponseData, error) {
	list, err := slf.enumsRepo.GetAllGanzhi(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsGanzhiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsGanzhiResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Shishen(ctx context.Context) (v1.EnumsShishenResponseData, error) {
	list, err := slf.enumsRepo.GetAllShishen(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsShishenResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsShishenResponseItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}
	return data, nil
}

func (slf *enumsService) Shierjianri(ctx context.Context) (v1.EnumsShierjianriResponseData, error) {
	list, err := slf.enumsRepo.GetAllShierjianri(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsShierjianriResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsShierjianriResponseItem{
			ID:     item.ID,
			Name:   item.Name,
			Alias:  item.Alias,
			Detail: item.Detail,
		})
	}
	return data, nil
}

func (slf *enumsService) Zhishen(ctx context.Context) (v1.EnumsZhishenResponseData, error) {
	list, err := slf.enumsRepo.GetAllZhishen(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsZhishenResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsZhishenResponseItem{
			ID:     item.ID,
			Name:   item.Name,
			Type:   item.Type,
			Detail: item.Detail,
			Symbol: item.Symbol,
			Daily:  item.Daily,
		})
	}
	return data, nil
}

func (slf *enumsService) Wuxing(ctx context.Context) (v1.EnumsWuxingResponseData, error) {
	list, err := slf.enumsRepo.GetAllWuxing(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsWuxingResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsWuxingResponseItem{
			ID:   item.ID,
			Name: item.Wuxing,
		})
	}
	return data, nil
}

func (slf *enumsService) Lunar(ctx context.Context, year string) (v1.EnumsLunarResponseData, error) {
	lunarYear := strs.Digit2ZhUpper(year)
	days, err := slf.enumsRepo.GetAllDaysByLunarYear(ctx, lunarYear)
	if err != nil {
		return nil, err
	}
	regex := regexp.MustCompile(`(?P<Month>\p{Han}+月)(?P<Day>\p{Han}+)`)
	var (
		res     v1.EnumsLunarResponseData
		current *v1.EnumsLunarResponseItem
	)
	for _, day := range days {
		matches := regex.FindStringSubmatch(day.LunarDate)
		if len(matches) == 0 {
			continue
		}
		if current == nil || current.Month != matches[1] {
			current = &v1.EnumsLunarResponseItem{
				Month: matches[1],
			}
			res = append(res, current)
		}
		current.Days = append(current.Days, &v1.LunarDay{
			Date:      day.Date,
			LunarDate: day.LunarYear + "年" + day.LunarDate,
			Name:      matches[2],
		})
	}
	return res, nil
}

func (slf *enumsService) Yiji(ctx context.Context) (v1.EnumsYijiResponseData, error) {
	list, err := slf.enumsRepo.GetAllYiji(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsYijiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsYijiResponseItem{
			ID:     item.ID,
			Name:   item.Name,
			Detail: item.Detail,
		})
	}
	return data, nil
}

func (slf *enumsService) Jieqi(ctx context.Context) (v1.EnumsJieqiResponseData, error) {
	list, err := slf.enumsRepo.GetAllJieqi(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsJieqiResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsJieqiResponseDateItem{
			ID:     item.ID,
			Name:   item.Name,
			Season: item.Season,
			Health: item.Health,
			Url:    item.Url,
		})
	}
	return data, nil
}

func (slf *enumsService) Time(ctx context.Context) (v1.EnumsTimeResponseData, error) {
	list, err := slf.enumsRepo.GetAllTime(ctx)
	if err != nil {
		return nil, err
	}
	var data v1.EnumsTimeResponseData
	for _, item := range list {
		data = append(data, &v1.EnumsTimeResponseItem{
			ID:       item.ID,
			Name:     item.Name + "时",
			Alias:    item.Alias,
			Time:     fmt.Sprintf("%s-%s", item.Kaishi, item.Jieshu),
			Dangling: item.Dangling,
			Jingmai:  item.Jingmai,
			Yi:       item.Zhongdian,
			Ji:       item.Tishi,
		})
	}
	return data, nil
}

func (slf *enumsService) Location(ctx context.Context, req *v1.EnumsLocationRequest) ([]*v1.LocationTree, error) {
	if !req.Overseas {
		return slf.location(ctx)
	}
	return slf.locationOverseas(ctx)
}

func (slf *enumsService) locationOverseas(ctx context.Context) ([]*v1.LocationTree, error) {
	countries, err := slf.enumsRepo.FetchAllCountry(ctx)
	if err != nil {
		return nil, err
	}
	provinces, err := slf.enumsRepo.FetchAllCountryProvince(ctx)
	if err != nil {
		return nil, err
	}
	return slf.buildLocationOverseasTree(ctx, countries, provinces), nil
}

func (slf *enumsService) buildLocationOverseasTree(_ context.Context, countries []*model.AddrCountry, provinces []*model.AddrCountryProvince) []*v1.LocationTree {
	countryMap := make(map[int64]*v1.LocationTree)
	for _, country := range countries {
		countryMap[country.Code] = &v1.LocationTree{
			Code: country.Code,
			Name: country.Name,
		}
	}
	provinceMap := make(map[int64]*v1.LocationTree)
	for _, province := range provinces {
		provinceNode := &v1.LocationTree{
			Code: province.Code,
			Name: province.Name,
		}
		countryNode, ok := countryMap[province.ParentsID]
		if ok {
			countryNode.Children = append(countryNode.Children, provinceNode)
			provinceMap[province.Code] = provinceNode
		}
	}
	var result []*v1.LocationTree
	for _, country := range countryMap {
		result = append(result, country)
	}
	slf.sortLocationTree(result)
	return result
}

func (slf *enumsService) location(ctx context.Context) ([]*v1.LocationTree, error) {
	provinces, err := slf.enumsRepo.FetchAllAddrProvince(ctx)
	if err != nil {
		return nil, err
	}
	cities, err := slf.enumsRepo.FetchAllAddrCity(ctx)
	if err != nil {
		return nil, err
	}
	areas, err := slf.enumsRepo.FetchAllArea(ctx)
	if err != nil {
		return nil, err
	}
	return slf.buildLocationTree(ctx, provinces, cities, areas), nil
}

func (slf *enumsService) buildLocationTree(_ context.Context, provinces []*model.AddrProvince, cities []*model.AddrCity, areas []*model.AddrArea) []*v1.LocationTree {
	provinceMap := make(map[int64]*v1.LocationTree)
	for _, province := range provinces {
		provinceMap[province.Code] = &v1.LocationTree{
			Code: province.Code,
			Name: province.Name,
		}
	}
	cityMap := make(map[int64]*v1.LocationTree)
	for _, city := range cities {
		cityNode := &v1.LocationTree{
			Code: city.Code,
			Name: city.Name,
		}
		provinceNode, ok := provinceMap[city.ProvinceCode]
		if ok {
			provinceNode.Children = append(provinceNode.Children, cityNode)
			cityMap[city.Code] = cityNode
		}
	}
	for _, area := range areas {
		areaNode := &v1.LocationTree{
			Code: area.Code,
			Name: area.Name,
		}
		cityNode, ok := cityMap[area.CityCode]
		if ok {
			cityNode.Children = append(cityNode.Children, areaNode)
		}
	}
	var result []*v1.LocationTree
	for _, province := range provinceMap {
		result = append(result, province)
	}
	slf.sortLocationTree(result)
	return result
}

func (slf *enumsService) sortLocationTree(trees []*v1.LocationTree) {
	sort.Slice(trees, func(i, j int) bool {
		return trees[i].Code < trees[j].Code
	})
	for _, tree := range trees {
		if len(tree.Children) > 0 {
			slf.sortLocationTree(tree.Children)
		}
	}
}
