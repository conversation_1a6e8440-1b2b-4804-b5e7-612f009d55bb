package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type PortalService interface {
	CreateWebsite(ctx context.Context, req *v1.CreatePortalWebsiteRequest) (*v1.CreatePortalWebsiteResponseData, error)
	UpdateWebsite(ctx context.Context, req *v1.UpdatePortalWebsiteRequest) error
	DeleteWebsite(ctx context.Context, req *v1.DeletePortalWebsiteRequest) error
	PageListWebsite(ctx context.Context, req *v1.PageListPortalWebsiteRequest) (*v1.PageListPortalWebsiteResponseData, error)
	CreateSection(ctx context.Context, req *v1.CreatePortalSectionRequest) (*v1.CreatePortalSectionResponseData, error)
	UpdateSection(ctx context.Context, req *v1.UpdatePortalSectionRequest) error
	DeleteSection(ctx context.Context, req *v1.DeletePortalSectionRequest) error
	PageListSection(ctx context.Context, req *v1.PageListPortalSectionRequest) (*v1.PageListPortalSectionResponseData, error)
	CreateArticle(ctx context.Context, req *v1.CreatePortalArticleRequest) (*v1.CreatePortalArticleResponseData, error)
	UpdateArticle(ctx context.Context, req *v1.UpdatePortalArticleRequest) error
	DeleteArticle(ctx context.Context, req *v1.DeletePortalArticleRequest) error
	PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error)
}

func NewPortalService(
	service *Service,
	portalRepo repository.PortalRepository,
) PortalService {
	return &portalService{
		Service:    service,
		portalRepo: portalRepo,
	}
}

type portalService struct {
	*Service
	portalRepo repository.PortalRepository
}

func (slf *portalService) CreateWebsite(ctx context.Context, req *v1.CreatePortalWebsiteRequest) (*v1.CreatePortalWebsiteResponseData, error) {
	id, err := slf.portalRepo.CreateWebsite(ctx, &model.PortalWebsite{
		Name:   req.Name,
		Domain: req.Domain,
		Remark: req.Remark,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreatePortalWebsiteResponseData{
		ID: id,
	}, nil
}

func (slf *portalService) UpdateWebsite(ctx context.Context, req *v1.UpdatePortalWebsiteRequest) error {
	website, err := slf.portalRepo.FetchWebsiteByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if website == nil {
		return v1.ErrNotFound
	}
	website.Name = req.Name
	website.Domain = req.Domain
	website.Remark = req.Remark
	return slf.portalRepo.UpdateWebsite(ctx, website)
}

func (slf *portalService) DeleteWebsite(ctx context.Context, req *v1.DeletePortalWebsiteRequest) error {
	return slf.portalRepo.DeleteWebsite(ctx, req.ID)
}

func (slf *portalService) PageListWebsite(ctx context.Context, req *v1.PageListPortalWebsiteRequest) (*v1.PageListPortalWebsiteResponseData, error) {
	return slf.portalRepo.PageListWebsite(ctx, req)
}

func (slf *portalService) CreateSection(ctx context.Context, req *v1.CreatePortalSectionRequest) (*v1.CreatePortalSectionResponseData, error) {
	id, err := slf.portalRepo.CreateSection(ctx, &model.PortalSection{
		WebsiteID: req.WebsiteID,
		Name:      req.Name,
		Code:      slf.sid.String(),
		Remark:    req.Remark,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreatePortalSectionResponseData{
		ID: id,
	}, nil
}

func (slf *portalService) UpdateSection(ctx context.Context, req *v1.UpdatePortalSectionRequest) error {
	section, err := slf.portalRepo.FetchSectionByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if section == nil {
		return v1.ErrNotFound
	}
	section.Name = req.Name
	section.Remark = req.Remark
	return slf.portalRepo.UpdateSection(ctx, section)
}

func (slf *portalService) DeleteSection(ctx context.Context, req *v1.DeletePortalSectionRequest) error {
	return slf.portalRepo.DeleteSection(ctx, req.ID)
}

func (slf *portalService) PageListSection(ctx context.Context, req *v1.PageListPortalSectionRequest) (*v1.PageListPortalSectionResponseData, error) {
	return slf.portalRepo.PageListSection(ctx, req)
}

func (slf *portalService) CreateArticle(ctx context.Context, req *v1.CreatePortalArticleRequest) (*v1.CreatePortalArticleResponseData, error) {
	id, err := slf.portalRepo.CreateArticle(ctx, &model.PortalArticle{
		SectionID: req.SectionID,
		Title:     req.Title,
		Author:    req.Author,
		Source:    req.Source,
		Content:   req.Content,
		Remark:    req.Remark,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreatePortalArticleResponseData{
		ID: id,
	}, nil
}

func (slf *portalService) UpdateArticle(ctx context.Context, req *v1.UpdatePortalArticleRequest) error {
	article, err := slf.portalRepo.FetchArticleByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if article == nil {
		return v1.ErrNotFound
	}
	article.Title = req.Title
	article.Author = req.Author
	article.Source = req.Source
	article.Content = req.Content
	article.Remark = req.Remark
	return slf.portalRepo.UpdateArticle(ctx, article)
}

func (slf *portalService) DeleteArticle(ctx context.Context, req *v1.DeletePortalArticleRequest) error {
	return slf.portalRepo.DeleteArticle(ctx, req.ID)
}

func (slf *portalService) PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error) {
	return slf.portalRepo.PageListArticle(ctx, req)
}
