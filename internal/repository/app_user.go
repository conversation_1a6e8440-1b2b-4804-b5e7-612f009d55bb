package repository

import (
	"context"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type AppUserRepository interface {
	PageListAppUser(ctx context.Context, req *v1.PageListAppUserRequest) (*v1.PageListAppUserResponseData, error)
	FetchMemberDurationByUserID(ctx context.Context, userID string) ([]*v1.AppUserMemberDuration, error)
	PageListAppUserVip(ctx context.Context, req *v1.PageListAppUserVipRequest) (*v1.PageListAppUserVipResponseData, error)
}

func NewAppUserRepository(repo *Repository) AppUserRepository {
	return &appUserRepository{
		Repository: repo,
	}
}

type appUserRepository struct {
	*Repository
}

func (slf *appUserRepository) FetchMemberDurationByUserID(ctx context.Context, userID string) ([]*v1.AppUserMemberDuration, error) {
	var list []*v1.AppUserMemberDuration
	query := slf.db.NewSelect().Model(&model.MemberDuration{}).
		Join("left join app_role ar on ar.id = md.role").
		ColumnExpr("ar.id as role_id").
		ColumnExpr("ar.name as role_name").
		ColumnExpr("date_format(md.expire_time,'%Y-%m-%d %H:%i:%s') as expire_time").
		ColumnExpr("date_format(md.created_at,'%Y-%m-%d %H:%i:%s') as start_time").
		ColumnExpr("date_format(md.updated_at,'%Y-%m-%d %H:%i:%s') as last_gain_time").
		ColumnExpr("if(md.expire_time < now(), 1, 0) is_expired").
		ColumnExpr("md.last_gain_type").
		Where("md.user_id = ?", userID)
	if err := query.Order("md.expire_time DESC").Scan(ctx, &list); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *appUserRepository) PageListAppUser(ctx context.Context, req *v1.PageListAppUserRequest) (*v1.PageListAppUserResponseData, error) {
	var list []*v1.PageListAppUserResponseDataItem
	query := slf.db.NewSelect().Distinct().Model(&model.AppUser{}).
		ColumnExpr("au.user_id").
		ColumnExpr("au.name").
		ColumnExpr("au.display_name").
		ColumnExpr("au.avatar").
		ColumnExpr("au.phone").
		ColumnExpr("au.country_code").
		ColumnExpr("au.signup_application").
		ColumnExpr("au.signup_application_id").
		ColumnExpr("app.name as signup_application_name").
		ColumnExpr("ac.code as channel").
		ColumnExpr("au.channel_id as channel_id").
		ColumnExpr("ac.name as channel_name").
		ColumnExpr("date_format(au.created_at,'%Y-%m-%d %H:%i:%s') as signup_time").
		ColumnExpr("IF(o.id IS NULL, 0, 1) AS is_paid")

	query.Join("left join app on app.id = au.signup_application_id").
		Join("left join app_channel ac on ac.id = au.channel_id").
		Join("left join `order` o on o.user_id = au.user_id and o.pay_status = 1")

	if req.Param.Name != nil {
		query.Where("au.name like concat('%', ?, '%') or au.display_name like concat('%', ?, '%')",
			*req.Param.Name, *req.Param.Name)
	}
	if req.Param.Phone != nil {
		query.Where("au.phone like concat('%', ?, '%')", *req.Param.Phone)
	}
	if len(req.Param.SignupApplicationIDs) > 0 {
		query.Where("au.signup_application_id in (?)", bun.In(req.Param.SignupApplicationIDs))
	}
	if len(req.Param.RoleIDs) > 0 {
		query.
			Join("left join member_duration md on md.user_id = au.user_id").
			Join("left join app_role ar on ar.id = md.role_id").
			Where("ar.id in (?)", bun.In(req.Param.RoleIDs))
	}
	if req.Param.SignupTimeStart != nil {
		query.Where("au.created_at >= ?", *req.Param.SignupTimeStart)
	}
	if req.Param.SignupTimeEnd != nil {
		query.Where("au.created_at <= ?", *req.Param.SignupTimeEnd)
	}
	query.Order("au.created_at DESC")
	//query.Order("is_paid DESC")
	count, err := query.Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.PageListAppUserResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *appUserRepository) PageListAppUserVip(ctx context.Context, req *v1.PageListAppUserVipRequest) (*v1.PageListAppUserVipResponseData, error) {
	var list []*v1.PageListAppUserVipResponseDataItem
	query := slf.db.NewSelect().Model(&model.MemberDuration{}).
		ColumnExpr("au.user_id").
		ColumnExpr("au.name").
		ColumnExpr("au.display_name").
		ColumnExpr("au.avatar").
		ColumnExpr("au.phone").
		ColumnExpr("au.country_code").
		ColumnExpr("au.signup_application").
		ColumnExpr("au.signup_application_id").
		ColumnExpr("app1.name as signup_application_name").
		ColumnExpr("date_format(au.created_at,'%Y-%m-%d %H:%i:%s') as signup_time").
		ColumnExpr("au.channel as channel").
		ColumnExpr("au.channel_id as channel_id").
		ColumnExpr("ac.name as channel_name").
		ColumnExpr("ar.id as role_id").
		ColumnExpr("ar.name as role_name").
		ColumnExpr("date_format(md.expire_time,'%Y-%m-%d %H:%i:%s') as expire_time").
		Join("left join app_user au on au.user_id = md.user_id").
		Join("left join app_role ar on ar.id = md.role").
		Join("left join app as app1 on app1.id = au.signup_application_id").
		Join("left join app as app2 on app2.role = ar.id").
		Join("left join app_channel ac on ac.id = au.channel_id")

	if req.Param.Name != nil {
		query.Where("au.name like concat('%', ?, '%') or au.display_name like concat('%', ?, '%')",
			*req.Param.Name, *req.Param.Name)
	}
	if req.Param.Phone != nil {
		query.Where("au.phone like concat('%', ?, '%')", *req.Param.Phone)
	}
	if len(req.Param.AppIDs) > 0 {
		query.Where("app2.id in (?)", bun.In(req.Param.AppIDs))
	}
	if len(req.Param.RoleIDs) > 0 {
		query.Where("ar.id in (?)", bun.In(req.Param.RoleIDs))
	}
	if req.Param.ExpireTimeStart != nil {
		query.Where("md.expire_time >= ?", *req.Param.ExpireTimeStart)
	}
	if req.Param.ExpireTimeEnd != nil {
		query.Where("md.expire_time <= ?", *req.Param.ExpireTimeEnd)
	}
	query.Order("md.updated_at DESC")
	count, err := query.Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.PageListAppUserVipResponseData{
		Total: count,
		List:  list,
	}, nil
}
