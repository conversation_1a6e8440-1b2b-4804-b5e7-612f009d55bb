package repository

import (
	"context"
	"fmt"
	"github.com/uptrace/bun"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type UserOrderRepository interface {
	PageListUserOrder(ctx context.Context, req *v1.PageListUserOrderRequest) (*v1.PageListUserOrderResponseData, error)
	FetchUserOrderByLuncaiBazi(ctx context.Context, userID string, bazi []string) ([]*model.Order, error)
	FetchOrderByMingliBaziID(ctx context.Context, id int64) (*model.Order, error)
	FetchAnonymousOrderByBazi(ctx context.Context, deviceID string, bazi []string) ([]*model.Order, error)
	GetOrderByOrderNo(ctx context.Context, orderNo string) (*model.Order, error)
	UpdateOrderExtraInfo(ctx context.Context, orderNo string, extraInfo map[string]interface{}) error
}

func NewUserOrderRepository(
	repo *Repository,
) UserOrderRepository {
	return &userOrderRepository{
		Repository: repo,
	}
}

type userOrderRepository struct {
	*Repository
}

func (slf *userOrderRepository) UpdateOrderExtraInfo(ctx context.Context, orderNo string, extraInfo map[string]any) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.Order{}).Where("order_no = ?", orderNo).
		Set("extra_info = ?", extraInfo).OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

// GetOrderByOrderNo 查询订单
func (slf *userOrderRepository) GetOrderByOrderNo(ctx context.Context, orderNo string) (*model.Order, error) {
	order := &model.Order{}
	err := slf.DB(ctx).NewSelect().Model(order).Where("order_no = ?", orderNo).Scan(ctx)
	if err != nil {
		err = fmt.Errorf("NewSelect: %v", err)
		return nil, err
	}
	return order, nil
}

func (slf *userOrderRepository) FetchAnonymousOrderByBazi(ctx context.Context, deviceID string, bazi []string) ([]*model.Order, error) {
	var orders []*model.Order
	if err := slf.DB(ctx).NewSelect().Model(&model.Order{}).
		Where("extra_info->>'$.device_id' = ?", deviceID).                                                                                                                                                                                                                                                                                                                    // 订单的额外信息中的设备ID字段
		Where("CONCAT_WS(',',\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[0]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[1]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[2]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[3]'))\n\t\t\t) = ?", strings.Join(bazi, ",")). // 订单的额外信息中的论财八字字段
		Where("pay_status = 1").Scan(ctx, &orders); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return orders, nil
}

func (slf *userOrderRepository) FetchUserOrderByLuncaiBazi(ctx context.Context, userID string, bazi []string) ([]*model.Order, error) {
	var orders []*model.Order
	if err := slf.DB(ctx).NewSelect().Model(&model.Order{}).
		Where("user_id = ?", userID).
		Where("CONCAT_WS(',',\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[0]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[1]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[2]')),\n\t\t\t\tJSON_UNQUOTE(JSON_EXTRACT(extra_info->'$.luncai.bazi', '$[3]'))\n\t\t\t) = ?", strings.Join(bazi, ",")). // 订单的额外信息中的论财八字字段
		Where("pay_status = 1").Scan(ctx, &orders); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return orders, nil
}

func (slf *userOrderRepository) FetchOrderByMingliBaziID(ctx context.Context, id int64) (*model.Order, error) {
	var order model.Order
	if err := slf.DB(ctx).NewSelect().Model(&model.Order{}).
		Where("extra_info->>'$.minglibazi.id' = ?", id).
		Where("pay_status = 1").Scan(ctx, &order); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &order, nil
}

func (slf *userOrderRepository) PageListUserOrder(ctx context.Context, req *v1.PageListUserOrderRequest) (*v1.PageListUserOrderResponseData, error) {
	var list []*v1.PageListUserOrderResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.Order{}).
		Join("left join app_user as au on au.user_id = o.user_id").
		Join("left join app on app.id = o.app_id").
		ColumnExpr("o.id").
		ColumnExpr("o.order_no").
		ColumnExpr("o.user_id").
		ColumnExpr("au.name as user_name").
		ColumnExpr("au.display_name as user_display_name").
		ColumnExpr("au.avatar as user_avatar").
		ColumnExpr("au.phone as user_phone").
		ColumnExpr("o.product_id").
		ColumnExpr("o.product_snapshot").
		ColumnExpr("o.quantity").
		ColumnExpr("o.amount").
		ColumnExpr("o.pay_amount").
		ColumnExpr("o.pay_channel").
		ColumnExpr("o.pay_status").
		ColumnExpr("o.transaction_id").
		ColumnExpr("o.pay_time").
		ColumnExpr("o.expire_time").
		ColumnExpr("o.created_at").
		ColumnExpr("o.app_id").
		ColumnExpr("app.name as app_name").
		ColumnExpr("o.ua").
		ColumnExpr("o.ip").
		ColumnExpr("o.source")

	if req.Param.UserName != nil {
		query.Where("au.name like concat('%', ?, '%') or au.display_name like concat('%', ?, '%')", *req.Param.UserName, *req.Param.UserName)
	}
	if req.Param.UserPhone != nil {
		query.Where("au.phone like concat('%', ?, '%')", *req.Param.UserPhone)
	}
	if len(req.Param.PayStatus) > 0 {
		query.Where("o.pay_status in (?)", bun.In(req.Param.PayStatus))
	}
	if req.Param.OrderNo != nil {
		query.Where("o.order_no like concat('%', ?, '%')", *req.Param.OrderNo)
	}
	if len(req.Param.UserID) > 0 {
		query.Where("o.user_id in (?)", bun.In(req.Param.UserID))
	}
	if len(req.Param.ProductID) > 0 {
		query.Where("o.product_id in (?)", bun.In(req.Param.ProductID))
	}
	if req.Param.CreatedTimeStart != nil {
		query.Where("o.created_at >= ?", *req.Param.CreatedTimeStart)
	}
	if req.Param.CreatedTimeEnd != nil {
		query.Where("o.created_at <= ?", *req.Param.CreatedTimeEnd)
	}
	if req.Param.PayTimeStart != nil {
		query.Where("o.pay_time >= ?", *req.Param.PayTimeStart)
	}
	if req.Param.PayTimeEnd != nil {
		query.Where("o.pay_time <= ?", *req.Param.PayTimeEnd)
	}
	if req.Param.PayAmountMin != nil {
		query.Where("o.pay_amount >= ?", *req.Param.PayAmountMin)
	}
	if req.Param.PayAmountMax != nil {
		query.Where("o.pay_amount <= ?", *req.Param.PayAmountMax)
	}
	if req.Param.SkuName != nil {
		query.Where("o.product_snapshot->>'$.sku_name' like concat('%', ?, '%')", *req.Param.SkuName)
	}

	count, err := query.Order("o.created_at DESC").
		Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	for _, item := range list {
		if !item.PayTime.IsZero() {
			item.PayTimeStr = item.PayTime.Format("2006-01-02 15:04:05")
		}
		item.ExpireTimeStr = item.ExpireTime.Format("2006-01-02 15:04:05")
		item.CreatedTimeStr = item.CreatedTime.Format("2006-01-02 15:04:05")
	}
	return &v1.PageListUserOrderResponseData{
		Total: count,
		List:  list,
	}, nil
}
