package repository

import (
	"context"
	"database/sql"
	"errors"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
)

type LuncaiRepository interface {
	GetGuaxiangByValue(ctx context.Context, value int) (*model.Guaxiang, error)
	GetGanzhiByName(ctx context.Context, name string) (*model.Ganzhi, error)
	GetShishenByRiyuan(ctx context.Context, ry1, ry2 string) (*model.RiyuanShishen, error)
	GetShishenLiunianSuggestion(ctx context.Context, shishen, gender string, scoreAbove50 bool) (*model.ShishenLiunianSuggestion, error)
	GetChangshengByName(ctx context.Context, name string) (*model.Changsheng, error)
	GetDizhiXiangchong(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiXiangchong, error)
	GetDizhiLiuhe(ctx context.Context, dizhi1, dizhi2 string) (*model.Di<PERSON><PERSON><PERSON><PERSON>, error)
	GetDizhiLiuheByOne(ctx context.Context, dizhi string) (*model.Dizhi<PERSON>iuhe, error)
	GetDizhiSanhe(ctx context.Context, dizhi1, dizhi2, dizhi3 string) (*model.DizhiSanhe, error)
	GetDizhiSanheByOne(ctx context.Context, dizhi string) (*model.DizhiSanhe, error)
	GetTianganWuhe(ctx context.Context, tiangan1, tiangan2 string) (*model.TianganWuhe, error)
	GetWuxingXiangke(ctx context.Context, wuxing, xiangke string) (*model.WuxingXiangke, error)
	GetWuxingXiangkeByXiangke(ctx context.Context, wuxing string) (*model.WuxingXiangke, error)
	GetWuxingAboutXiangkeByName(ctx context.Context, name string) (*model.WuxingXiangke, error)
	GetWuxingXiangsheng(ctx context.Context, wuxing, xiangsheng string) (*model.WuxingXiangsheng, error)
	GetTiangan(ctx context.Context, name string) (*model.Tiangan, error)
	GetDizhi(ctx context.Context, name string) (*model.Dizhi, error)
	Nayins(ctx context.Context) ([]*model.Nayin, error)
	Shenshas(ctx context.Context) ([]*model.Shensha2, error)
	GetOffset4TimeByLocation(ctx context.Context, location string) (int, error)
	GetYizhuLunmin(ctx context.Context, rizhu string) ([]*model.YizhuLunmin, error)
}

func NewLuncaiRepository(
	repo *Repository,
) LuncaiRepository {
	return &luncaiRepository{
		Repository: repo,
	}
}

type luncaiRepository struct {
	*Repository
}

func (slf *luncaiRepository) GetYizhuLunmin(ctx context.Context, rizhu string) ([]*model.YizhuLunmin, error) {
	var yls []*model.YizhuLunmin
	if err := slf.DB(ctx).NewSelect().Model((*model.YizhuLunmin)(nil)).
		Where("rizhu = ?", rizhu).Scan(ctx, &yls); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return yls, nil
}

func (slf *luncaiRepository) GetWuxingXiangkeByXiangke(ctx context.Context, wuxing string) (*model.WuxingXiangke, error) {
	var wx model.WuxingXiangke
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangke)(nil)).
		Where("wuxing_xiangke = ?", wuxing).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}

func (slf *luncaiRepository) GetOffset4TimeByLocation(ctx context.Context, location string) (int, error) {
	var t int
	if err := slf.DB(ctx).NewSelect().TableExpr("addr_localtotime").
		ColumnExpr("addtime").
		Where("fullname = ?", location).
		Scan(ctx, &t); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, nil
		}
		return 0, err
	}
	return t, nil
}

func (slf *luncaiRepository) GetWuxingAboutXiangkeByName(ctx context.Context, name string) (*model.WuxingXiangke, error) {
	var wx model.WuxingXiangke
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangke)(nil)).
		Where("wuxing_xiangke = ?", name).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}

func (slf *luncaiRepository) GetGuaxiangByValue(ctx context.Context, value int) (*model.Guaxiang, error) {
	var guaxiang model.Guaxiang
	if err := slf.DB(ctx).NewSelect().Model((*model.Guaxiang)(nil)).
		Where("value = ?", value).Scan(ctx, &guaxiang); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &guaxiang, nil
}

func (slf *luncaiRepository) GetGanzhiByName(ctx context.Context, name string) (*model.Ganzhi, error) {
	var ganzhi model.Ganzhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Ganzhi)(nil)).
		Where("name = ?", name).Scan(ctx, &ganzhi); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &ganzhi, nil
}

func (slf *luncaiRepository) GetShishenByRiyuan(ctx context.Context, ry1, ry2 string) (*model.RiyuanShishen, error) {
	var ryss model.RiyuanShishen
	if err := slf.DB(ctx).NewSelect().Model((*model.RiyuanShishen)(nil)).
		Where("riyuan_1 = ? AND riyuan_2 = ?", ry1, ry2).Scan(ctx, &ryss); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &ryss, nil
}

func (slf *luncaiRepository) GetShishenLiunianSuggestion(ctx context.Context, shishen, gender string, scoreAbove50 bool) (*model.ShishenLiunianSuggestion, error) {
	var sls model.ShishenLiunianSuggestion
	query := slf.DB(ctx).NewSelect().Model((*model.ShishenLiunianSuggestion)(nil)).
		Where("shishen = ? AND score_above_50 = ?", shishen, scoreAbove50)
	if gender == "男" {
		query.Where("gender = 1 or gender = 0")
	} else if gender == "女" {
		query.Where("gender = 2 or gender = 0")
	} else {
		query.Where("gender = 0")
	}
	if err := query.Scan(ctx, &sls); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &sls, nil
}

func (slf *luncaiRepository) Shenshas(ctx context.Context) ([]*model.Shensha2, error) {
	var list []*model.Shensha2
	if err := slf.DB(ctx).NewSelect().Model((*model.Shensha2)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	for _, item := range list {
		item.Setup()
	}
	return list, nil
}

func (slf *luncaiRepository) Nayins(ctx context.Context) ([]*model.Nayin, error) {
	var list []*model.Nayin
	if err := slf.DB(ctx).NewSelect().Model((*model.Nayin)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *luncaiRepository) GetChangshengByName(ctx context.Context, name string) (*model.Changsheng, error) {
	var changsheng model.Changsheng
	if err := slf.DB(ctx).NewSelect().Model((*model.Changsheng)(nil)).
		Where("name = ?", name).Scan(ctx, &changsheng); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &changsheng, nil
}

func (slf *luncaiRepository) GetDizhiXiangchong(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiXiangchong, error) {
	var dzxc model.DizhiXiangchong
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiXiangchong)(nil)).
		Where("(dizhi = ? AND dizhi_two = ?) OR (dizhi = ? AND dizhi_two = ?)", dizhi1, dizhi2, dizhi2, dizhi1).Scan(ctx, &dzxc); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzxc, nil
}

func (slf *luncaiRepository) GetDizhiSanhe(ctx context.Context, dizhi1, dizhi2, dizhi3 string) (*model.DizhiSanhe, error) {
	arr := []string{dizhi1, dizhi2, dizhi3}
	var dzsh model.DizhiSanhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiSanhe)(nil)).
		Where("dizhi in (?) and dizhi_two in (?) and dizhi_three in (?)", bun.In(arr), bun.In(arr), bun.In(arr)).
		Where("type = 0").Scan(ctx, &dzsh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzsh, nil
}

func (slf *luncaiRepository) GetDizhiLiuhe(ctx context.Context, dizhi1, dizhi2 string) (*model.DizhiLiuhe, error) {
	var dzlh model.DizhiLiuhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiLiuhe)(nil)).
		Where("(dizhi = ? AND dizhi_two = ?) OR (dizhi = ? AND dizhi_two = ?)", dizhi1, dizhi2, dizhi2, dizhi1).Scan(ctx, &dzlh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzlh, nil
}

func (slf *luncaiRepository) GetDizhiLiuheByOne(ctx context.Context, dizhi string) (*model.DizhiLiuhe, error) {
	var dzlh model.DizhiLiuhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiLiuhe)(nil)).
		Where("dizhi = ? OR dizhi_two = ?", dizhi, dizhi).Scan(ctx, &dzlh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzlh, nil
}

func (slf *luncaiRepository) GetDizhiSanheByOne(ctx context.Context, dizhi string) (*model.DizhiSanhe, error) {
	var dzsh model.DizhiSanhe
	if err := slf.DB(ctx).NewSelect().Model((*model.DizhiSanhe)(nil)).
		Where("dizhi = ? OR dizhi_two = ? OR dizhi_three = ?", dizhi, dizhi, dizhi).
		Where("type = 0").Scan(ctx, &dzsh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dzsh, nil
}

func (slf *luncaiRepository) GetDizhi(ctx context.Context, name string) (*model.Dizhi, error) {
	var dz model.Dizhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Dizhi)(nil)).
		Where("dizhi = ?", name).Scan(ctx, &dz); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &dz, nil
}

func (slf *luncaiRepository) GetTianganWuhe(ctx context.Context, tiangan1, tiangan2 string) (*model.TianganWuhe, error) {
	var tgwh model.TianganWuhe
	if err := slf.DB(ctx).NewSelect().Model((*model.TianganWuhe)(nil)).
		Where("(tiangan = ? AND tiangan_two = ?) OR (tiangan = ? AND tiangan_two = ?)", tiangan1, tiangan2, tiangan2, tiangan1).Scan(ctx, &tgwh); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &tgwh, nil
}

func (slf *luncaiRepository) GetTiangan(ctx context.Context, name string) (*model.Tiangan, error) {
	var tiangan model.Tiangan
	if err := slf.DB(ctx).NewSelect().Model((*model.Tiangan)(nil)).
		Where("tiangan = ?", name).Scan(ctx, &tiangan); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &tiangan, nil
}

func (slf *luncaiRepository) GetWuxingXiangke(ctx context.Context, wuxing, xiangke string) (*model.WuxingXiangke, error) {
	var wx model.WuxingXiangke
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangke)(nil)).
		Where("wuxing = ? AND wuxing_xiangke = ?", wuxing, xiangke).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}

func (slf *luncaiRepository) GetWuxingXiangsheng(ctx context.Context, wuxing, xiangsheng string) (*model.WuxingXiangsheng, error) {
	var wx model.WuxingXiangsheng
	if err := slf.DB(ctx).NewSelect().Model((*model.WuxingXiangsheng)(nil)).
		Where("wuxing = ? AND wuxing_xiangsheng = ?", wuxing, xiangsheng).Scan(ctx, &wx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &wx, nil
}
