package repository

import (
	"context"
	"zodiacus/internal/model"
)

type UserEventRepository interface {
	CreateUserEvent(ctx context.Context, eve *model.UserEvent) (int64, error)
}

func NewUserEventRepository(
	repo *Repository,
) UserEventRepository {
	return &userEventRepository{
		Repository: repo,
	}
}

type userEventRepository struct {
	*Repository
}

func (slf *userEventRepository) CreateUserEvent(ctx context.Context, eve *model.UserEvent) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(eve).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
