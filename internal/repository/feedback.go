package repository

import (
	"context"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type FeedbackRepository interface {
	PageListFeedback(ctx context.Context, req *v1.PageListFeedbackRequest) (*v1.PageListFeedbackResponseData, error)
	ReplyFeedback(ctx context.Context, req *v1.ReplyFeedbackRequest) error
	FetchOneByID(ctx context.Context, id int64) (*model.Feedback, error)
}

func NewFeedbackRepository(repo *Repository) FeedbackRepository {
	return &feedbackRepository{
		Repository: repo,
	}
}

type feedbackRepository struct {
	*Repository
}

func (slf *feedbackRepository) FetchOneByID(ctx context.Context, id int64) (*model.Feedback, error) {
	var feedback model.Feedback
	if err := slf.DB(ctx).NewSelect().Model(&feedback).Where("id = ?", id).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &feedback, nil
}

func (slf *feedbackRepository) PageListFeedback(ctx context.Context, req *v1.PageListFeedbackRequest) (*v1.PageListFeedbackResponseData, error) {
	var list []*v1.PageListFeedbackResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.Feedback{}).
		ColumnExpr("fb.id, fb.user_id, fb.application").
		ColumnExpr("au.name, au.display_name, au.avatar, au.phone").
		ColumnExpr("a.name as application_name").
		ColumnExpr("fb.content, fb.image_key, fb.created_at").
		ColumnExpr("fb.reply_content, fb.reply_image_key, fb.reply_time")
	query.
		Join("left join app_user au on au.user_id = fb.user_id").
		Join("left join app a on a.app = fb.application")
	count, err := query.Order("fb.created_at desc").
		Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil && !slf.NotFound(err) {
		return nil, err
	}
	return &v1.PageListFeedbackResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *feedbackRepository) ReplyFeedback(ctx context.Context, req *v1.ReplyFeedbackRequest) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.Feedback{ID: req.ID}).
		Set("reply_content = ?", req.ReplyContent).
		Set("reply_image_key = ?", req.ReplyImageKey).
		Set("reply_time = ?", time.Now()).WherePK().OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}
