package repository

import (
	"context"
	"database/sql"
	"errors"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type MingliRuleRepository interface {
	Create(ctx context.Context, rule *model.MingliRule) (int64, error)
	Update(ctx context.Context, rule *model.MingliRule) error
	GetByID(ctx context.Context, id int64) (*model.MingliRule, error)
	GetByRuleNo(ctx context.Context, ruleNo string) (*model.MingliRule, error)
	DeleteByID(ctx context.Context, id int64) error
	PageList(ctx context.Context, req *v1.PageListMingliRuleRequest) (*v1.PageListMingliRuleResponseData, error)
	GetAllEnabledRules(ctx context.Context, module ...int64) ([]*model.MingliRule, error)
}

func NewMingliRuleRepository(
	repo *Repository,
) MingliRuleRepository {
	return &mingliRuleRepository{
		Repository: repo,
	}
}

type mingliRuleRepository struct {
	*Repository
}

func (slf *mingliRuleRepository) Create(ctx context.Context, rule *model.MingliRule) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().Model(rule).Exec(ctx)
	if err != nil {
		if slf.IsDuplicateEntry(err) {
			return 0, v1.ErrMingliRuleNoAlreadyTaken
		}
		return 0, err
	}
	return result.LastInsertId()
}

func (slf *mingliRuleRepository) Update(ctx context.Context, rule *model.MingliRule) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.MingliRule{ID: rule.ID}).
		Set("no = ?", rule.No).
		Set("name = ?", rule.Name).
		Set("module = ?", rule.Module).
		Set("source = ?", rule.Source).
		Set("is_enabled = ?", rule.IsEnabled).
		Set("result = ?", rule.Result).
		Set("description = ?", rule.Description).
		WherePK().OmitZero().Exec(ctx); err != nil {
		if slf.IsDuplicateEntry(err) {
			return v1.ErrMingliRuleNoAlreadyTaken
		}
		return err
	}
	return nil
}

func (slf *mingliRuleRepository) GetByID(ctx context.Context, id int64) (*model.MingliRule, error) {
	var rule model.MingliRule
	if err := slf.DB(ctx).NewSelect().Model((*model.MingliRule)(nil)).
		Where("id = ?", id).
		Scan(ctx, &rule); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

func (slf *mingliRuleRepository) GetByRuleNo(ctx context.Context, ruleNo string) (*model.MingliRule, error) {
	var rule model.MingliRule
	if err := slf.DB(ctx).NewSelect().Model((*model.MingliRule)(nil)).
		Where("no = ?", ruleNo).
		Scan(ctx, &rule); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

func (slf *mingliRuleRepository) DeleteByID(ctx context.Context, id int64) error {
	if _, err := slf.DB(ctx).NewDelete().Model((*model.MingliRule)(nil)).
		Where("id = ?", id).
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *mingliRuleRepository) PageList(ctx context.Context, req *v1.PageListMingliRuleRequest) (*v1.PageListMingliRuleResponseData, error) {
	var (
		err error
		res v1.PageListMingliRuleResponseData
	)
	res.Total, err = slf.DB(ctx).NewSelect().Model((*model.MingliRule)(nil)).
		ColumnExpr("mr.id").
		ColumnExpr("mr.no").
		ColumnExpr("mr.name").
		ColumnExpr("mr.module").
		ColumnExpr("m.name as module_name").
		ColumnExpr("mr.source").
		ColumnExpr("if(mr.source = 1, cu.name, 'APP用户') as creator_name").
		ColumnExpr("mr.is_enabled").
		ColumnExpr("mr.result").
		ColumnExpr("mr.description").
		Join("left join cms_user cu on cu.id = mr.creator_id and mr.source = 1").
		Join("left join module m on m.id = mr.module").
		Offset(req.Offset()).Limit(req.Limit()).Order("mr.created_at desc").
		ScanAndCount(ctx, &res.List)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &res, nil
}

func (slf *mingliRuleRepository) GetAllEnabledRules(ctx context.Context, module ...int64) ([]*model.MingliRule, error) {
	var rules []*model.MingliRule
	query := slf.DB(ctx).NewSelect().Model((*model.MingliRule)(nil)).
		Where("is_enabled = ?", true)
	if len(module) > 0 {
		query = query.Where("module = ?", module[0])
	}
	if err := query.Scan(ctx, &rules); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return rules, nil
}
