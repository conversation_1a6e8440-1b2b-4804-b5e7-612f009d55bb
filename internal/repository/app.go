package repository

import (
	"context"
	"zodiacus/internal/model"
)

type AppRepository interface {
	FetchAppByID(ctx context.Context, id int64) (*model.App, error)
	FetchAllApps(ctx context.Context, exclude ...bool) ([]*model.App, error)
	FetchAppByAppKey(ctx context.Context, appKey string) (*model.App, error)
}

func NewAppRepository(repo *Repository) AppRepository {
	return &appRepository{Repository: repo}
}

type appRepository struct {
	*Repository
}

func (slf *appRepository) FetchAppByAppKey(ctx context.Context, appKey string) (*model.App, error) {
	var app model.App
	if err := slf.DB(ctx).NewSelect().Model(&model.App{}).
		Where("app = ?", appKey).Scan(ctx, &app); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

func (slf *appRepository) FetchAllApps(ctx context.Context, exclude ...bool) ([]*model.App, error) {
	var apps []*model.App
	query := slf.DB(ctx).NewSelect().Model(&model.App{})
	if len(exclude) > 0 && exclude[0] {
		query.Where("is_self = ?", true)
	}
	if err := query.Order("created_at desc").Scan(ctx, &apps); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return apps, nil
}

func (slf *appRepository) FetchAppByID(ctx context.Context, id int64) (*model.App, error) {
	var app model.App
	if err := slf.DB(ctx).NewSelect().Model(&model.App{}).
		Where("id = ?", id).Scan(ctx, &app); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}
