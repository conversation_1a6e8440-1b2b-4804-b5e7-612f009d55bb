package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type AtlasProductRepository interface {
	CreateAtlasProduct(ctx context.Context, req *model.Product) (int64, error)
	ListAtlasProduct(ctx context.Context, req *v1.AtlasListProductRequest) (*v1.AtlasListProductResponse, error)
	DeleteAtlasProduct(ctx context.Context, req int64) error
	UpdateAtlasProduct(ctx context.Context, req *v1.AtlasUpdateProductReq) error
}

func NewAtlasProductRepository(repo *Repository) AtlasProductRepository {
	return &atlasProductRepository{
		Repository: repo,
	}
}

type atlasProductRepository struct {
	*Repository
}

// 创建商品
func (slf *atlasProductRepository) CreateAtlasProduct(ctx context.Context, req *model.Product) (int64, error) {
	_, err := slf.db.NewInsert().Model(req).Exec(ctx)
	if err != nil {
		err = fmt.Errorf("NewInsert: %w", err)
		return 0, err
	}

	return req.ID, nil
}

// 查询商品列表
func (slf *atlasProductRepository) ListAtlasProduct(ctx context.Context, req *v1.AtlasListProductRequest) (*v1.AtlasListProductResponse, error) {

	query := slf.db.NewSelect().Model(&model.Product{})

	if req.Param.Name != "" {
		query.Where("name LIKE ?", "%"+req.Param.Name+"%")
	}

	if req.Param.Application != "" {
		query.Where("application = ?", req.Param.Application)
	}

	if req.Param.Sort == 1 {
		query.Order("create_time DESC")
	}

	if req.Param.Sort == 2 {
		query.Order("create_time ASC")
	}

	var products []model.Product
	total, err := query.Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &products)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}

		err = fmt.Errorf("ScanAndCount: %w", err)
		return nil, err
	}

	return &v1.AtlasListProductResponse{
		Total: total,
		List:  products,
	}, nil
}

// 删除商品
func (slf *atlasProductRepository) DeleteAtlasProduct(ctx context.Context, req int64) error {
	_, err := slf.db.NewDelete().Model(&model.Product{ID: req}).Exec(ctx)
	if err != nil {
		err = fmt.Errorf("NewDelete: %w", err)
		return err
	}

	return nil
}

// 修改商品
func (slf *atlasProductRepository) UpdateAtlasProduct(ctx context.Context, req *v1.AtlasUpdateProductReq) error {
	_, err := slf.db.NewUpdate().Model(
		&model.Product{
			ID:            req.Id,
			ProductCreate: req.ProductCreate,
		},
	).OmitZero().Exec(ctx)
	if err != nil {
		err = fmt.Errorf("NewUpdate: %w", err)
		return err
	}

	return nil
}
