package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"zodiacus/internal/model"
)

type HistoryTodayRepository interface {
	GetDayActions(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error)
	GetDayEvents4AD(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error)
	GetDayEvents4BC(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error)
}

func NewHistoryTodayRepository(
	repo *Repository,
) HistoryTodayRepository {
	return &historyTodayRepository{
		Repository: repo,
	}
}

type historyTodayRepository struct {
	*Repository
}

// GetDayActions 获取每日节日/活动日
func (slf *historyTodayRepository) GetDayActions(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error) {
	var items []*model.HistoryDayEvent
	if err := slf.DB(ctx).NewSelect().Model(&model.HistoryDayEvent{}).
		Where("year is null or year = ''").
		Where("day = ?", fmt.Sprintf("%02d%02d", month, day)).
		Scan(ctx, &items); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}

// GetDayEvents4AD 获取公元每日历史事件
func (slf *historyTodayRepository) GetDayEvents4AD(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error) {
	var items []*model.HistoryDayEvent
	if err := slf.DB(ctx).NewSelect().
		NewRaw("SELECT `hde`.`id`, `hde`.`day`, `hde`.`year`, `hde`.`ad`, `hde`.`keyword`, `hde`.`type`, `hde`.`title`, `hde`.`content` "+
			"FROM `history_day_event` AS `hde` WHERE (day = ?) AND (ad = 0) AND (year IS NOT NULL AND year != 0) "+
			"ORDER BY COALESCE(CAST(hde.`year` AS UNSIGNED), 0) desc", fmt.Sprintf("%02d%02d", month, day)).
		Scan(ctx, &items); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}

// GetDayEvents4BC 获取公元前每日历史事件
func (slf *historyTodayRepository) GetDayEvents4BC(ctx context.Context, month, day int) ([]*model.HistoryDayEvent, error) {
	var items []*model.HistoryDayEvent
	if err := slf.DB(ctx).NewSelect().
		NewRaw("SELECT `hde`.`id`, `hde`.`day`, `hde`.`year`, `hde`.`ad`, `hde`.`keyword`, `hde`.`type`, `hde`.`title`, `hde`.`content` "+
			"FROM `history_day_event` AS `hde` WHERE (day = ?) AND (ad = 1) AND (year IS NOT NULL AND year != 0) "+
			"ORDER BY COALESCE(CAST(hde.`year` AS UNSIGNED), 0) asc", fmt.Sprintf("%02d%02d", month, day)).
		Scan(ctx, &items); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}
