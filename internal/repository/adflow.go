package repository

import (
	"context"
	"zodiacus/internal/model"
)

type AdflowRepository interface {
	InsertQwLink(ctx context.Context, link *model.AdflowQwLink) (int64, error)
	FetchQwUserIDs(ctx context.Context) ([]string, error)
}

type adflowRepository struct {
	*Repository
}

func (slf *adflowRepository) FetchQwUserIDs(ctx context.Context) ([]string, error) {
	var (
		record model.AdflowQwUser
	)
	if err := slf.DB(ctx).NewSelect().Model(&record).Limit(1).Scan(ctx, &record); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return record.UserIds, nil
}

func NewAdflowRepository(repo *Repository) AdflowRepository {
	return &adflowRepository{Repository: repo}
}

func (slf *adflowRepository) InsertQwLink(ctx context.Context, link *model.AdflowQwLink) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(link).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
