package repository

import (
	"context"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type MinProgramRepository interface {
	CreateQrCode(ctx context.Context, code *model.MpQrcode) (int64, error)
	PageListQrCode(ctx context.Context, req *v1.MpQrCodePageListRequest) (*v1.MpQrCodePageListResponseData, error)
	FetchQrCodesByCodes(ctx context.Context, codes []string) ([]*model.MpQrcode, error)
}

func NewMinProgramRepository(repo *Repository) MinProgramRepository {
	return &minProgramRepository{Repository: repo}
}

type minProgramRepository struct {
	*Repository
}

func (slf *minProgramRepository) FetchQrCodesByCodes(ctx context.Context, codes []string) ([]*model.MpQrcode, error) {
	var list []*model.MpQrcode
	if err := slf.DB(ctx).NewSelect().Model(&model.MpQrcode{}).
		Where("scene_str in (?)", bun.In(codes)).
		Scan(ctx, &list); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *minProgramRepository) PageListQrCode(ctx context.Context, req *v1.MpQrCodePageListRequest) (*v1.MpQrCodePageListResponseData, error) {
	var list []*v1.MpQrCodePageListResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.MpQrcode{}).
		ColumnExpr("mpq.app_id").
		ColumnExpr("app.name as app_name").
		ColumnExpr("mpq.scene_str").
		ColumnExpr("mpq.remark").
		ColumnExpr("mpq.page").
		ColumnExpr("mpq.image_data").
		ColumnExpr("mpq.image_type").
		ColumnExpr("date_format(mpq.created_at,'%Y-%m-%d %H:%i:%s') as created_at").
		Join("LEFT JOIN app on app.id = mpq.app_id")

	if len(req.Param.AppIDs) != 0 {
		query.Where("mpq.app_id in ?", bun.In(req.Param.AppIDs))
	}
	if req.Param.SceneStr != nil {
		query.Where("mpq.scene_str like concat('%', ?, '%')", *req.Param.SceneStr)
	}
	if req.Param.Remark != nil {
		query.Where("mpq.remark like concat('%', ?, '%')", *req.Param.Remark)
	}
	if req.Param.CreateAtStart != nil {
		query.Where("mpq.created_at >= ?", *req.Param.CreateAtStart)
	}
	if req.Param.CreateAtEnd != nil {
		query.Where("mpq.created_at <= ?", *req.Param.CreateAtEnd)
	}
	count, err := query.Offset(req.Offset()).Limit(req.Limit()).Order("mpq.created_at desc").ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.MpQrCodePageListResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *minProgramRepository) CreateQrCode(ctx context.Context, code *model.MpQrcode) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().Model(code).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return result.LastInsertId()
}
