package model

import "github.com/uptrace/bun"

type DizhiXiangchong struct {
	bun.BaseModel `bun:"table:dizhi_xiangchong,alias:dzxc"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Dizhi1        string `bun:"dizhi"`
	Dizhi2        string `bun:"dizhi_two"`
}

type Dizhi<PERSON>iuhe struct {
	bun.BaseModel `bun:"table:dizhi_liuhe,alias:dzlh"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Dizhi1        string `bun:"dizhi"`
	Dizhi2        string `bun:"dizhi_two"`
}

type DizhiSanhe struct {
	bun.BaseModel `bun:"table:dizhi_sanhe,alias:dzsh"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Dizhi1        string `bun:"dizhi"`
	Dizhi2        string `bun:"dizhi_two"`
	Dizhi3        string `bun:"dizhi_three"`
	Hehua         string `bun:"hehua"` // 五行
}

type Dizhi struct {
	bun.BaseModel `bun:"table:dizhi,alias:dz"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Dizhi         string `bun:"dizhi"`
	Wuxing        string `bun:"wuxing"`
	Shuxiang      string `bun:"shuxiang"`
}
