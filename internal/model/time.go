package model

import "github.com/uptrace/bun"

type Time struct {
	bun.BaseModel `bun:"table:time,alias:t"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Name          string `bun:"name"`
	<PERSON><PERSON>         string `bun:"alias"`
	<PERSON>shi        string `bun:"kaishi"`
	Jieshu        string `bun:"jieshu"`
	Dangling      string `bun:"dangling"`
	Jingmai       string `bun:"jingmai"`
	Zhongdian     string `bun:"zhongdian"`
	Tishi         string `bun:"tishi"`
}
