package model

import "github.com/uptrace/bun"

type Wuxing struct {
	bun.BaseModel `bun:"table:wuxing,alias:wx"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Wuxing        string `bun:",notnull"`
	<PERSON>zang        string `bun:",notnull"`
	<PERSON>fu         string `bun:",notnull"`
	<PERSON>wei         string `bun:",notnull"`
	<PERSON>chang       string `bun:",notnull"`
	Yuefen        string `bun:",notnull"`
	Yan<PERSON>         string `bun:",notnull"`
	YanseTwo      string `bun:",notnull"`
	Fangwei       string `bun:",notnull"`
	<PERSON>zi         string `bun:",notnull"`
	Jijie         string `bun:",notnull"`
}

type WuxingXiangke struct {
	bun.BaseModel `bun:"table:wuxing_xiangke,alias:wxxk"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Wuxing        string `bun:"wuxing"`
	<PERSON><PERSON><PERSON>       string `bun:"wuxing_xiangke"`
}

type WuxingXiangsheng struct {
	bun.BaseModel `bun:"table:wuxing_xiangsheng,alias:wxxs"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Wuxing        string `bun:"wuxing"`
	Xiangsheng    string `bun:"wuxing_xiangsheng"`
}
