package model

import "github.com/uptrace/bun"

// Guaxiang 卦相
type Guaxiang struct {
	bun.BaseModel `bun:"table:sixtyfour_gua,alias:sg"`
	ID            int64  `json:"id" bun:"id,pk,autoincrement"` // 主键ID
	Name          string `json:"name" bun:",notnull"`          // 卦名
	Value         int    `json:"value" bun:",notnull"`         // 卦值
	Topdown       string `json:"topdown" bun:",notnull"`       // 上下卦
	Yao6          int    `json:"yao6" bun:"yao_6,notnull"`     // 爻6
	Yao5          int    `json:"yao5" bun:"yao_5,notnull"`     // 爻5
	Yao4          int    `json:"yao4" bun:"yao_4,notnull"`     // 爻4
	Yao3          int    `json:"yao3" bun:"yao_3,notnull"`     // 爻3
	Yao2          int    `json:"yao2" bun:"yao_2,notnull"`     // 爻2
	Yao1          int    `json:"yao1" bun:"yao_1,notnull"`     // 爻1
	YaoValue      int    `json:"yaoValue" bun:",notnull"`      // 爻值
	Idiom         string `json:"idiom" bun:",notnull"`         // 成语
	Guaji         string `json:"guaji" bun:",notnull"`         // 卦吉
	Gejue         string `json:"gejue" bun:",notnull"`         // 歌诀
	Jieshi        string `json:"jieshi" bun:",notnull"`        // 解释
	Duanri        string `json:"duanri" bun:",notnull"`        // 断日
}
