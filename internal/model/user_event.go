package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
create table user_event (
    id bigint auto_increment,
    user_id varchar(255),
    app_id bigint not null,
    event_type tinyint comment '事件类型：1-用户点击命理八字',
    event_time datetime,
    created_at      datetime   default CURRENT_TIMESTAMP     not null,
    updated_at      datetime   default CURRENT_TIMESTAMP     not null,
    deleted_at      datetime   default '0001-01-01 00:00:00' null,
    primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户事件';
*/

type UserEvent struct {
	bun.BaseModel `bun:"user_event,alias:ue"`
	ID            int64     `bun:"id,pk"`                                      // 用户ID
	UserID        string    `bun:"user_id"`                                    // 用户ID
	AppID         int64     `bun:",notnull"`                                   // 应用：2-排盘、3-万年历、4-运势、5-论财
	EventType     int       `bun:"event_type"`                                 // 事件类型：1-点击排盘
	EventTime     time.Time `bun:"event_time"`                                 // 事件时间
	DeletedAt     time.Time `bun:",soft_delete,default:'0001-01-01 00:00:00'"` // 删除时间
	BaseFields4Time
}
