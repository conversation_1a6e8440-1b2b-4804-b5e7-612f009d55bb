package model

import "github.com/uptrace/bun"

type BirthYearFortune struct {
	bun.BaseModel   `bun:"birth_year_fortune,alias:byf"`
	ID              int64  `bun:"id,pk" json:"-"`
	BirthYear       string `bun:"birth_year" json:"birthYear"`
	Wuxing          string `bun:"wuxing" json:"wuxing"`
	Zodiac          string `bun:"zodiac" json:"zodiac"`
	Basis           string `bun:"basis" json:"basis"`
	ZodiacDetail    string `bun:"zodiac_detail" json:"zodiacDetail"`
	MaleFortune     string `bun:"male_fortune" json:"maleFortune"`
	FemaleFortune   string `bun:"female_fortune" json:"femaleFortune"`
	BaseFields4Time `json:"-"`
}

type BirthMonthFortune struct {
	bun.BaseModel   `bun:"birth_month_fortune,alias:bmf"`
	ID              int64  `bun:"id,pk" json:"-"`
	BirthMonth      string `bun:"birth_month" json:"birthMonth"`
	StartDate       string `bun:"start_date" json:"startDate"`
	EndDate         string `bun:"end_date" json:"endDate"`
	ConceptionMonth string `bun:"conception_month" json:"conceptionMonth"`
	SolarTerm       string `bun:"solar_term" json:"solarTerm"`
	FortuneText     string `bun:"fortune_text" json:"fortuneText"`
	BaseFields4Time `json:"-"`
}

type BirthDayFortune struct {
	bun.BaseModel   `bun:"birth_day_fortune,alias:bdf"`
	ID              int64  `bun:"id,pk" json:"-"`
	BirthDay        string `bun:"birth_day" json:"birthDay"`
	FortuneText     string `bun:"fortune_text" json:"fortuneText"`
	BaseFields4Time `json:"-"`
}

type BirthHourFortune struct {
	bun.BaseModel   `bun:"birth_hour_fortune,alias:bhf"`
	ID              int64  `bun:"id,pk" json:"-"`
	BirthHour       string `bun:"birth_hour" json:"birthHour"`
	TimeRange       string `bun:"time_range" json:"timeRange"`
	FortuneText     string `bun:"fortune_text" json:"fortuneText"`
	SuitableCareer  string `bun:"suitable_career" json:"suitableCareer"`
	CriticalYears   string `bun:"critical_years" json:"criticalYears"`
	BaseFields4Time `json:"-"`
}

type BirthMonthRizhu struct {
	bun.BaseModel   `bun:"birth_month_rizhu,alias:bmr"`
	ID              int64  `bun:"id,pk" json:"-"`
	Rizhu           string `bun:"rizhu" json:"rizhu"`
	BirthMonth      string `bun:"birth_month" json:"birthMonth"`
	God             string `bun:"god" json:"god"`
	Explanation     string `bun:"explanation" json:"explanation"`
	BaseFields4Time `json:"-"`
}

type BirthHourRizhu struct {
	bun.BaseModel   `bun:"birth_hour_rizhu,alias:bhr"`
	ID              int64  `bun:"id,pk" json:"-"`
	Rizhu           string `bun:"rizhu" json:"rizhu"`
	BirthHour       string `bun:"birth_hour" json:"birthHour"`
	Basis           string `bun:"basis" json:"basis"`
	Conclusion      string `bun:"conclusion" json:"conclusion"`
	BaseFields4Time `json:"-"`
}

type BirthDayHour struct {
	bun.BaseModel   `bun:"birth_day_hour,alias:bdh"`
	ID              int64  `bun:"id,pk" json:"-"`
	Rizhu           string `bun:"rizhu" json:"rizhu"`
	Shizhu          string `bun:"shizhu" json:"shizhu"`
	Basis           string `bun:"basis" json:"basis"`
	Conclusion      string `bun:"conclusion" json:"conclusion"`
	BaseFields4Time `json:"-"`
}
