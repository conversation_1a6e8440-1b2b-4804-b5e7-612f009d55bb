package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
	create table mingli_rule
	(
		id              bigint auto_increment,
		no          	varchar(255)                             not null,
		name        	varchar(255)                             not null,
		module     		bigint                                   not null,
		source      	tinyint(1) default 1                     not null,
		is_enabled      tinyint(1) default 1                     not null,
		result 			varchar(255)                             not null,
		description 	varchar(255)                             not null,
		creator_id      bigint                                   not null,
		created_at      datetime   default CURRENT_TIMESTAMP     not null,
		updated_at      datetime   default CURRENT_TIMESTAMP     not null,
		deleted_at      datetime   default '0001-01-01 00:00:00' null,
		primary key (id),
		unique index idx_no_deleted_at (rule_no, deleted_at)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='命理规则';
*/
// MingliRule 命理规则
type MingliRule struct {
	bun.BaseModel `bun:"table:mingli_rule,alias:mr"`
	ID            int64     `bun:"id,pk,autoincrement"`
	No            string    `bun:",notnull,unique:idx_rule_no_deleted_at"`
	Name          string    `bun:",notnull"`
	Module        int64     `bun:",notnull"`
	Source        int       `bun:",notnull"`              // 规则来源: 1-系统，2-用户
	IsEnabled     bool      `bun:",notnull,default:true"` //  是否启用: true-启用，false-禁用"`
	Result        string    `bun:",notnull"`
	Description   string    `bun:",notnull"`
	CreatorID     string    `bun:",notnull"`
	DeletedAt     time.Time `bun:",soft_delete,unique:idx_no_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}
