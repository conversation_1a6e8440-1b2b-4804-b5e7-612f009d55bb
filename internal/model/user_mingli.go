package model

import (
	"github.com/uptrace/bun"
	"strings"
	"time"
)

// UserMingli 用户命例
type UserMingli struct {
	bun.BaseModel  `bun:"table:user_mingli,alias:um"`
	ID             int64             `bun:"id,pk,autoincrement"`
	UserID         string            `bun:",notnull"`
	AppID          int64             `bun:",notnull"` // 应用：2-排盘、3-万年历、4-运势、5-论财
	GroupID        int64             `bun:",notnull"`
	Name           string            `bun:",notnull"`
	Birthtime      time.Time         `bun:",notnull"`
	BirthtimeLunar string            `bun:",notnull"`
	BirthtimeSun   time.Time         `bun:",notnull"`
	Birthplace     []string          `bun:","`
	Gender         int               `bun:",notnull"`
	IsDefault      bool              `bun:",notnull,default:false"`
	Wuxing         []string          `bun:","`       // 五行：用神,喜神,忌神,仇神,闲神
	Bazi           []string          `bun:","`       // 八字：年份干支,月份干支,日期干支,时辰干支
	Xiaoyun        UserMingliXiaoyun `bun:"xiaoyun"` // 小运
	Dayun          UserMingliDayun   `bun:"dayun"`   // 大运
	DeletedAt      time.Time         `bun:",soft_delete,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

func (slf *UserMingli) Bazi2D() [][]string {
	var bazi [][]string
	for _, v := range slf.Bazi {
		arr := strings.Split(v, "")
		bazi = append(bazi, []string{arr[0], arr[1]})
	}
	return bazi
}

type UserMingliXiaoyun struct {
	SubYear   int      `json:"subYear"`
	StartYear int      `json:"startYear"`
	EndYear   int      `json:"endYear"`
	Values    []string `json:"values"`
}

type UserMingliDayun struct {
	StartYear int      `json:"startYear"`
	EndYear   int      `json:"endYear"`
	Values    []string `json:"values"`
}

func (slf *UserMingli) GetDayunOrXiaoyun(year int) string {
	if year >= slf.Xiaoyun.StartYear && year <= slf.Xiaoyun.EndYear {
		offset := year - slf.Xiaoyun.StartYear
		index := offset % slf.Xiaoyun.SubYear
		return slf.Xiaoyun.Values[index]
	} else if year >= slf.Dayun.StartYear && year <= slf.Dayun.EndYear {
		offset := year - slf.Dayun.StartYear
		index := (offset / 10) % len(slf.Dayun.Values)
		return slf.Dayun.Values[index]
	}
	return ""
}
