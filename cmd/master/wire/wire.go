//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/master"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/log"
	aliyun_oss "zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/pkg/uaip"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/identity"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewEnumsRepository,
	repository.NewMasterRepository,
	repository.NewUserPaipanRecordRepository,
	repository.NewDateRepository,
	repository.NewUserMingliRepository,
	repository.NewUserMingliGroupRepository,
	repository.NewAppVersionRepository,
	repository.NewAppRepository,
	repository.NewLuncaiRepository,
	repository.NewMingliRuleRepository,
	repository.NewMingliRuleConditionRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewLocationService,
	service.NewEnumsService,
	service.NewDatetimeService,
	service.NewMasterService,
	service.NewDateService,
	service.NewAppVersionService,
	service.NewPaipanRecordService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	master.NewDateTimeHandler,
	master.NewLocationHandler,
	master.NewEnumsHandler,
	master.NewMasterHandler,
	master.NewDateHandler,
	master.NewAppVersionHandler,
	master.NewPaipanRecordHandler,
)

var serverSet = wire.NewSet(
	server.NewMasterHTTPServer,
	server.NewJob,
)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, job),
		app.WithName("luncai-app-api"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		serverSet,
		sid.NewSid,
		identity.NewClient,
		corona.NewClient,
		aliyun_sms.NewClient,
		uaip.NewIP2RegionSearcher,
		aliyun_oss.NewClient,
		newApp,
	))
}
