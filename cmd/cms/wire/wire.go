//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/cms"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/log"
	aliyun_oss "zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/pkg/uaip"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/identity"
	"zodiacus/third_party/miniprogram"
	"zodiacus/third_party/offiaccount"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/wecom"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewMingliRuleRepository,
	repository.NewEnumsRepository,
	repository.NewMingliRuleConditionRepository,
	repository.NewUserPaipanRecordRepository,
	repository.NewTermRepository,
	repository.NewQwRepository,
	repository.NewAtlasProductRepository,
	repository.NewUserMingliRepository,
	repository.NewUserMingliGroupRepository,
	repository.NewDateRepository,
	repository.NewUserOrderRepository,
	repository.NewAppUserRepository,
	repository.NewOffiaccountRepository,
	repository.NewAppRepository,
	repository.NewMinProgramRepository,
	repository.NewAppChannelRepository,
	repository.NewAppVersionRepository,
	repository.NewPortalRepository,
	repository.NewBaziAnalysisLinkRepository,
	repository.NewSubMailRepository,
	repository.NewFeedbackRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewMingliRuleService,
	service.NewEnumsService,
	service.NewMingliRuleConditionService,
	service.NewPaipanRecordService,
	service.NewTermService,
	service.NewWecomService,
	service.NewOffiaccountService,
	service.NewAtlasProductService,
	service.NewUserMingliService,
	service.NewUserOrderService,
	service.NewAppUserService,
	service.NewMiniProgramService,
	service.NewAppChannelService,
	service.NewAppVersionService,
	service.NewPortalService,
	service.NewBaziAnalysisLinkService,
	service.NewSubMailService,
	service.NewFeedbackService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	cms.NewMingliRuleHandler,
	cms.NewEnumsHandler,
	cms.NewMingliRuleConditionHandler,
	cms.NewPaipanRecordHandler,
	cms.NewTermHandler,
	cms.NewWecomHandler,
	cms.NewAtlasProductHandler,
	cms.NewUserMingliHandler,
	cms.NewUserOrderHandler,
	cms.NewAppUserHandler,
	cms.NewOffiaccountHandler,
	cms.NewMiniProgramHandler,
	cms.NewAppChannelHandler,
	cms.NewAppVersionHandler,
	cms.NewPortalHandler,
	cms.NewBaziAnalysisLinkHandler,
	cms.NewSubMailHandler,
	cms.NewFeedbackHandler,
)

var serverSet = wire.NewSet(
	server.NewAtlasServer,
	server.NewJob,
)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, job, task),
		app.WithName("cms-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		serverSet,
		sid.NewSid,
		server.NewTask,
		newApp,
		identity.NewClient,
		wecom.NewDoraemon,
		offiaccount.NewOfficialAccount,
		miniprogram.NewManager,
		aliyun_sms.NewClient,
		corona.NewClient,
		uaip.NewIP2RegionSearcher,
		submail.NewClient,
		aliyun_oss.NewClient,
	))
}
